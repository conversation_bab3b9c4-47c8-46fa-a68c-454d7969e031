[I 2025-07-30 10:34:19,210 minium minitest#432 _miniSetUp] =========Current case: test_02_navigate_all_main_pages=========
[I 2025-07-30 10:34:19,211 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_02_navigate_all_main_pages
[I 2025-07-30 10:34:19,211 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:19,211 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:19,212 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:19,212 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:19,212 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:19,213 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:19,213 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"04ea56e9-38c4-4884-bf62-4ad021363733","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,301 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"04ea56e9-38c4-4884-bf62-4ad021363733","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAnEWZP/489V59Tc+RO5kc5CIJhHAFEEFBuaMicgjIIioIC8t67oHKui54rL9d5bcIKseiiKIQFgEDK7AGFTkkgYSQg5wkmckxM5mjp4/3rPr+Ue/7dvXbb/f0TBKSkPpA3nm6ut56q97u+vTzfOp40bRdAAAARGSMSVva0pb2PrZDlpGQkJDYHyAAgIjha2lLW9rS3re29GUkJCT2L9TQ2k+xGaV0v5YvbWlLey9tQsgho8uEhUpISBzqEAOfvYQ6dJahIMlFQuK9B9Er2cuiRs4yklwkJA4H7D3dCCyDCCFx1LX9qzac368rwLDyS1va0t6vtjgU1Eh+3vERcbjXGp4u04j/In0cCYlDGo34LMPyaxqNm
[I 2025-07-30 10:34:19,304 minium minitest#487 _miniSetUp] =========case: test_02_navigate_all_main_pages start=========
[I 2025-07-30 10:34:20,306 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:34:20,307 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"eb696679-b195-4a08-9178-974ab1751002","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:20,350 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:20,352 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"eb696679-b195-4a08-9178-974ab1751002","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:20,353 minium.Conn9680 connection#704 _handle_async_msg] received async msg: eb696679-b195-4a08-9178-974ab1751002
[D 2025-07-30 10:34:35,313 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842875310,"webviewId":3,"routeEventId":"3_1753842874825","renderer":"webview"},1753842875311]}}
[I 2025-07-30 10:34:35,315 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842875310, 'webviewId': 3, 'routeEventId': '3_1753842874825', 'renderer': 'webview'}, 1753842875311]}
[W 2025-07-30 10:34:35,315 minium.App4384 app#1013 switch_tab] Switch tab(/pages/home/<USER>/pages/mine/index)
[D 2025-07-30 10:34:35,421 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842875418,"webviewId":2,"routeEventId":"2_1753842875314","renderer":"webview"},1753842875419]}}
[I 2025-07-30 10:34:35,422 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842875418, 'webviewId': 2, 'routeEventId': '2_1753842875314', 'renderer': 'webview'}, 1753842875419]}
[D 2025-07-30 10:34:37,316 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"fbad7f58-546f-4df4-b2c3-e063777a9aa0","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:37,318 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"fbad7f58-546f-4df4-b2c3-e063777a9aa0","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:37,319 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:37,319 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"fedd1d89-0dba-46c9-8493-d244f4b9b388","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:37,322 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"fedd1d89-0dba-46c9-8493-d244f4b9b388","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:37,323 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"cee572a4-eea6-4c33-a509-86e46faf6bba","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:37,395 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"cee572a4-eea6-4c33-a509-86e46faf6bba","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6ec9daunrJvoeQHUIQCCAGFYc9biggKqOOgjAyjOtvZlDR4cP9U8ZvGFBZRkUUZBFZAiM6gIgsQgIhJAGykaQ7S3enl+pa7nbO+/vj3Hvr1q1b1dWdhE7IeQK33zp17rnn3Krz1Ps+Z7nEcjwAAABCCCJKW9rSlvZ+tkOWkZCQkDgQoABACAlfS1va0pb2/rWlLyMhIXFgoYbWAYrNOOcHtHxpS1va+2hTSg8ZXSYsVEJC4lBHNPDZR6jDZxkOklwkJN56iHol+1jU6FlGkouExOGAfaebCMsQAiFxNLT9qzad368rwIjyS1va0j6gdnQoqJn8ouMTQkZ6rZHpMs34L9LHkZA4pNGMzzIiv6bZi
[D 2025-07-30 10:34:37,426 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"001a7371-6428-4512-90c7-5b16a1496dd5","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:34:37,427 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:34:38,292 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842878275,"webviewId":5,"routeEventId":"5_1753842877723","renderer":"webview"},1753842878279]}}
[I 2025-07-30 10:34:38,295 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842878275, 'webviewId': 5, 'routeEventId': '5_1753842877723', 'renderer': 'webview'}, 1753842878279]}
[D 2025-07-30 10:34:38,297 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"001a7371-6428-4512-90c7-5b16a1496dd5","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:38,297 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 001a7371-6428-4512-90c7-5b16a1496dd5
[D 2025-07-30 10:34:40,299 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"1e7a69f0-e2d8-4e24-9ece-35da7c71bce8","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:40,303 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"1e7a69f0-e2d8-4e24-9ece-35da7c71bce8","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:34:40,304 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:40,305 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"625f23c7-7a2e-4554-b76b-eeee125a631a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:40,308 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"625f23c7-7a2e-4554-b76b-eeee125a631a","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:40,309 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"870a2e9d-273f-4149-9453-e16171e1b7a3","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:40,389 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"870a2e9d-273f-4149-9453-e16171e1b7a3","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXm8ZVV1J77W2vsMd3rzVFUUUBSCSoEMEY0TnQiKIr+0SUw0BjQdkeAv0e60RdpfNGow7S9CD7ETacRWxCmJSfx0EA0G/QXtJE5QSFFiFVQVVcWb53enM+y91u+Pc+599431qnivxvP9VN23z3D32Xffe77nu9Zee20MIgMAAICIIpKV11LOkCHD2oFNlsmQIUOGjQABACI2t7Py6uUMGTIcKzItkyFDho0FNUunglI4XcoZMmRYOzItkyFDho0FHf2UDBkyZHgeyFgmQ4YMG4sWlmn1O2Tl1csZMmRYMzK/TIYMGTYWmcWUIUOGjUXGMhkyZNhY6JYyAkhWXlt5eSQTnVpfEUApUkREmEXcZDhNISLMYpmt5
[D 2025-07-30 10:34:40,392 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"34c24b2d-fbb8-4e05-b6d7-800d2d94a488","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:34:40,393 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:34:40,626 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842880620,"webviewId":3,"routeEventId":"3_1753842880425","renderer":"webview"},1753842880621]}}
[D 2025-07-30 10:34:40,627 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"34c24b2d-fbb8-4e05-b6d7-800d2d94a488","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:40,628 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842880620, 'webviewId': 3, 'routeEventId': '3_1753842880425', 'renderer': 'webview'}, 1753842880621]}
[I 2025-07-30 10:34:40,628 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 34c24b2d-fbb8-4e05-b6d7-800d2d94a488
[D 2025-07-30 10:34:40,905 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842880902,"webviewId":2,"routeEventId":"2_1753842880689","renderer":"webview"},1753842880903]}}
[I 2025-07-30 10:34:40,907 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842880902, 'webviewId': 2, 'routeEventId': '2_1753842880689', 'renderer': 'webview'}, 1753842880903]}
[D 2025-07-30 10:34:42,630 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"6bdbee72-8272-4481-ab03-61112283db18","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:42,633 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"6bdbee72-8272-4481-ab03-61112283db18","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:42,635 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:42,636 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"e9df6139-6091-4c4f-a34d-c5252e32c173","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:42,638 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"e9df6139-6091-4c4f-a34d-c5252e32c173","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:42,638 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"1cb5f109-a622-4ad1-bfdc-da5b81e8fc64","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,711 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"1cb5f109-a622-4ad1-bfdc-da5b81e8fc64","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//dbfc35rtw3hCSEQAgCAcSwissdL+QQFW8QVtYVXfe3iyu6LLoeu7LusrDKsSqiKIcIEljRFVREEBIIIQmQiyTv5Xjv5R3z5uirqn5/VHdPTU/PvHkviUlIfSD9vlNTXV3VM/WZ7/dTR6Pt+gAAAIjIOVe2spWt7H1sRyyjoKCgsD9AAAARo9fKVraylb1vbeXLKCgo7F/okbWfYjPG2H4tX9nKVvZe2oSQQ0aXiQpVUFA41CEHPnsJffQso0GRi4LCGw+yV7KXRY2fZRS5KCgcDth7upFYBhEi4mhqB1dtOX9QV4Ax5Ve2spW9X215KKiV/KLjI+JYrzU2XaYV/0X5OAoKhzRa8VnG5Ne0GjE15
[I 2025-07-30 10:34:42,713 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:34:42,713 minium minitest#799 _miniTearDown] =========Current case Down: test_02_navigate_all_main_pages=========
[I 2025-07-30 10:34:42,714 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:34:42,714 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"8e7fabc4-8cef-4d67-8d2e-c728e908d998","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,791 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"8e7fabc4-8cef-4d67-8d2e-c728e908d998","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgHFWZNv6+p7be7pp9JyF7CEEgBBFUHPa4IciiiIwDwsjnJ+r4KY7LMLiPMv6GARVwVEQRhEHQwIiMgCKLkEAISYBsZLlZ7r25S99eaj3v749TVV1dXd23701iEnIeSN23T586dU51n6ff9zlLoWm7AAAAiEhE0pa2tKW9n+2QZSQkJCQOBBgAIGL4WtrSlra0968tfRkJCYkDCzW0DlBsxjk/oOVLW9rS3kebMXbY6DJhoRISEoc7ooHPPkIdPstwkOQiIfHmQ9Qr2ceiRs8yklwkJI4E7DvdRFgGEULiaGj7V206v19XgBHll7a0pX1A7ehQUDP5RcdHxJFea2S6TDP+i/RxJCQOazTjs4zIr2k2YmrMH
[I 2025-07-30 10:34:42,793 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:34:42,793 minium basenative#63 wrapper] call BaseNative.get_start_up end 
