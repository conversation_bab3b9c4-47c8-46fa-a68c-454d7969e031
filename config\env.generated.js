/**
 * 自动生成的环境配置文件
 * 请勿手动修改此文件
 * 生成时间: 2025/7/30 17:30:44
 * 环境: development
 */

// 当前环境变量
const ENV_VARS = {
  "NODE_ENV": "development",
  "API_BASE_URL": "http://localhost:3000/api",
  "API_TIMEOUT": "10000",
  "WECHAT_APP_ID": "wx82283b353918af82",
  "DEBUG": "true",
  "ENABLE_MOCK": "false",
  "LOG_LEVEL": "debug",
  "ENABLE_CACHE": "true",
  "CACHE_TIMEOUT": "300000",
  "CURRENT_SERVER": "dev",
  "ENV_DESCRIPTION": "本地开发环境"
};

// 环境配置映射
const ENV_CONFIG = {
  development: {
    baseURL: ENV_VARS.API_BASE_URL || 'http://localhost:3000/api',
    timeout: parseInt(ENV_VARS.API_TIMEOUT) || 10000,
    wechatAppId: ENV_VARS.WECHAT_APP_ID || 'wx82283b353918af82',
    debug: ENV_VARS.DEBUG === 'true',
    enableMock: ENV_VARS.ENABLE_MOCK === 'true',
    logLevel: ENV_VARS.LOG_LEVEL || 'debug',
    enableCache: ENV_VARS.ENABLE_CACHE === 'true',
    cacheTimeout: parseInt(ENV_VARS.CACHE_TIMEOUT) || 300000
  },
  test: {
    baseURL: ENV_VARS.API_BASE_URL || 'http://*************:3000/api',
    timeout: parseInt(ENV_VARS.API_TIMEOUT) || 12000,
    wechatAppId: ENV_VARS.WECHAT_APP_ID || 'wx82283b353918af82',
    debug: ENV_VARS.DEBUG === 'true',
    enableMock: ENV_VARS.ENABLE_MOCK === 'true',
    logLevel: ENV_VARS.LOG_LEVEL || 'info',
    enableCache: ENV_VARS.ENABLE_CACHE === 'true',
    cacheTimeout: parseInt(ENV_VARS.CACHE_TIMEOUT) || 300000
  },
  production: {
    baseURL: ENV_VARS.API_BASE_URL || 'http://*************:3001/api',
    timeout: parseInt(ENV_VARS.API_TIMEOUT) || 15000,
    wechatAppId: ENV_VARS.WECHAT_APP_ID || 'wx82283b353918af82',
    debug: ENV_VARS.DEBUG === 'true',
    enableMock: ENV_VARS.ENABLE_MOCK === 'true',
    logLevel: ENV_VARS.LOG_LEVEL || 'error',
    enableCache: ENV_VARS.ENABLE_CACHE === 'true',
    cacheTimeout: parseInt(ENV_VARS.CACHE_TIMEOUT) || 600000
  }
};

// 当前环境
const CURRENT_ENV = 'development';

// 导出当前环境配置
module.exports = {
  currentEnv: CURRENT_ENV,
  envDescription: ENV_VARS.ENV_DESCRIPTION || 'development环境',
  ...ENV_CONFIG[CURRENT_ENV],
  
  // 保持向后兼容
  ENV_CONFIG,
  ENV_VARS
};
