{"test_time": "2025-07-30T11:13:32.335178", "duration_seconds": 1.382451, "test_users_created": 2, "api_tests": {"total": 13, "passed": 11, "failed": 2, "critical_failed": 2}, "overall_success": false, "test_users": [{"name": "测试用户A", "phone": "13800000001"}, {"name": "测试用户B", "phone": "13800000002"}], "detailed_results": [{"test_name": "API连通性", "success": true, "details": "API服务正常: 200", "critical": true, "timestamp": "2025-07-30T11:13:32.405540"}, {"test_name": "用户注册-测试用户A", "success": true, "details": "注册成功", "critical": true, "timestamp": "2025-07-30T11:13:32.581135"}, {"test_name": "用户注册-测试用户B", "success": true, "details": "注册成功", "critical": true, "timestamp": "2025-07-30T11:13:32.746774"}, {"test_name": "用户登录验证-测试用户A", "success": true, "details": "登录验证成功", "critical": true, "timestamp": "2025-07-30T11:13:32.879939"}, {"test_name": "用户登录验证-测试用户B", "success": true, "details": "登录验证成功", "critical": true, "timestamp": "2025-07-30T11:13:33.012824"}, {"test_name": "获取可关联用户", "success": true, "details": "找到5个可关联用户", "critical": true, "timestamp": "2025-07-30T11:13:33.097144"}, {"test_name": "发送关联申请", "success": true, "details": "申请发送成功，ID: cmdpe5oop003lskd14zzr4iw8", "critical": true, "timestamp": "2025-07-30T11:13:33.194106"}, {"test_name": "处理关联申请", "success": true, "details": "关联申请处理成功", "critical": true, "timestamp": "2025-07-30T11:13:33.303222"}, {"test_name": "获取关联列表", "success": true, "details": "找到1个关联关系", "critical": true, "timestamp": "2025-07-30T11:13:33.377681"}, {"test_name": "获取菜品列表", "success": true, "details": "找到5个菜品", "critical": true, "timestamp": "2025-07-30T11:13:33.454499"}, {"test_name": "创建菜品", "success": false, "details": "创建失败: 400", "critical": true, "timestamp": "2025-07-30T11:13:33.534574"}, {"test_name": "获取今日订单", "success": true, "details": "找到0个今日订单", "critical": true, "timestamp": "2025-07-30T11:13:33.616911"}, {"test_name": "创建订单", "success": false, "details": "创建失败: 500", "critical": true, "timestamp": "2025-07-30T11:13:33.715433"}], "test_coverage": {"api_connectivity": "API连通性测试", "user_management": "用户注册登录测试", "user_connection": "用户关联功能测试", "dish_management": "菜品管理功能测试", "order_management": "订单管理功能测试"}}