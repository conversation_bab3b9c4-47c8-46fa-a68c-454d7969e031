{"case_name": "test_04_order_page_functionality", "run_time": "20250730 10:35:02", "test_type": "CompleteMiniProgramTest", "case_doc": "测试订餐页面功能", "success": true, "failures": "", "errors": "", "start_timestamp": 1753842902.3274658, "is_failure": false, "is_error": false, "module": "E:.wx-nan.complete_miniprogram_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/home/<USER>", "path": "images\\setup.png", "ts": 1753842902, "datetime": "2025-07-30 10:35:02", "use_region": false}, {"name": "assertIn-success", "url": "/pages/order/index", "path": "images\\assertIn-success.png", "ts": 1753842906, "datetime": "2025-07-30 10:35:06", "use_region": false}, {"name": "teardown", "url": "/pages/order/index", "path": "images\\teardown.png", "ts": 1753842907, "datetime": "2025-07-30 10:35:07", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [{"name": "assertIn", "ret": true, "msg": null, "img": "assertIn-success", "wxml": ""}], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753842907.022681, "appId": "", "appName": "", "source": {"code": ["    def test_04_order_page_functionality(self):\n", "        \"\"\"测试订餐页面功能\"\"\"\n", "        print(\"🧪 测试订餐页面功能\")\n", "        \n", "        try:\n", "            # 导航到订餐页\n", "            self.app.switch_tab(\"/pages/order/index\")\n", "            time.sleep(3)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 检查页面是否加载\n", "            self.assertIn(\"order\", current_page.path, \"应该在订餐页面\")\n", "            \n", "            # 查找可点击的元素\n", "            try:\n", "                clickable_elements = current_page.get_elements(\"view[bindtap], button\")\n", "                print(f\"🔘 找到 {len(clickable_elements)} 个可点击元素\")\n", "                \n", "                # 尝试点击第一个可点击元素（如果存在）\n", "                if clickable_elements:\n", "                    first_element = clickable_elements[0]\n", "                    print(\"🔘 尝试点击第一个可点击元素\")\n", "                    first_element.click()\n", "                    time.sleep(1)\n", "                    print(\"✅ 点击操作完成\")\n", "                \n", "            except Exception as e:\n", "                print(f\"⚠️ 点击操作失败: {e}\")\n", "            \n", "            self.take_screenshot(\"04_order_functionality\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ 订餐页面功能测试失败: {e}\")\n", "        \n", "        print(\"✅ 订餐页面功能测试完成\")\n"], "start": 148}, "filename": "result.json"}