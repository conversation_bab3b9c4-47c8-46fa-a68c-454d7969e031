# 楠楠家厨小程序测试执行指南

## 🎯 测试目标

对楠楠家厨小程序进行全面的系统测试，验证所有核心功能的正确性和稳定性。

## 📋 测试覆盖范围

### ✅ 核心功能模块
1. **用户认证系统**
   - 账号密码登录
   - 微信一键登录
   - 用户注册
   - 登录状态保持
   - 退出登录

2. **首页功能**
   - 页面加载和数据展示
   - 用户信息显示
   - 今日菜单展示
   - 通知消息轮播
   - 页面导航

3. **菜单浏览系统**
   - 菜品分类切换
   - 菜品列表展示
   - 菜品详情查看
   - 菜品搜索功能
   - 图片加载和预览

4. **订餐核心流程**
   - 菜品选择和数量调整
   - 购物车功能
   - 订单创建
   - 订单列表查看
   - 订单详情查看

5. **菜品管理**
   - 我的菜品列表
   - 新增菜品
   - 编辑菜品
   - 菜品发布/下架

6. **用户管理**
   - 个人信息查看
   - 用户关联功能
   - 关联历史查看
   - 个人资料编辑

7. **消息通知**
   - 消息列表查看
   - 通知中心
   - 消息推送接收
   - 消息状态更新

8. **历史记录**
   - 历史菜单查看
   - 订单历史
   - 统计信息展示

## 🚀 快速执行

### 方式一：一键执行（推荐）

```bash
python run_miniprogram_tests.py
```

这个脚本会自动：
1. 检查Python环境
2. 安装测试依赖
3. 设置测试环境
4. 运行所有测试
5. 生成测试报告
6. 清理测试文件

### 方式二：手动执行

```bash
# 1. 安装依赖
pip install -r tests/requirements.txt

# 2. 运行测试
python tests/run_tests.py

# 3. 查看报告
open tests/reports/test_report.html
```

## 📊 测试配置

### 环境配置
- **小程序AppID**: wx82283b353918af82
- **测试服务器**: http://*************:3000/api (test环境)
- **测试框架**: minium
- **测试设备**: 微信开发者工具

### 测试用户
- **用户名**: test_user
- **密码**: test123456
- **手机号**: 13800138000

## 📁 测试文件结构

```
tests/
├── config/
│   └── minium.json              # minium配置
├── utils/
│   └── base_test.py             # 基础测试类
├── test_01_login.py             # 登录功能测试
├── test_02_home.py              # 首页功能测试
├── test_03_order.py             # 订餐页面测试
├── test_04_order_flow.py        # 完整订餐流程测试
├── test_05_user_management.py   # 用户管理测试
├── run_tests.py                 # 测试运行器
├── requirements.txt             # 依赖列表
└── reports/                     # 测试报告
    ├── test_report.json         # JSON报告
    ├── test_report.html         # HTML报告
    └── screenshots/             # 测试截图
```

## 📈 预期测试结果

### 成功指标
- **功能覆盖率**: 100%
- **测试通过率**: ≥95%
- **核心流程**: 全部通过
- **异常处理**: 完整验证

### 测试报告
1. **JSON报告**: 机器可读的详细结果
2. **HTML报告**: 可视化的测试总结
3. **截图记录**: 关键步骤的视觉验证

## ⚠️ 注意事项

### 执行前准备
1. 确保微信开发者工具已安装
2. 小程序项目已在开发者工具中打开
3. 网络连接稳定
4. Python 3.7+ 环境

### 测试环境
- 使用test环境，不影响生产数据
- 测试完成后自动清理测试数据
- 所有测试代码在完成后可选择删除

### 常见问题
1. **minium连接失败**: 检查开发者工具是否打开
2. **网络超时**: 确认测试服务器状态
3. **元素定位失败**: 查看测试截图定位问题

## 🔄 测试流程

1. **环境检查** → 验证Python版本和依赖
2. **依赖安装** → 自动安装minium等测试框架
3. **配置设置** → 创建测试目录和配置文件
4. **执行测试** → 按模块顺序运行所有测试
5. **结果收集** → 生成详细的测试报告
6. **数据清理** → 清理测试数据和代码文件

## 📞 技术支持

如遇到问题，请检查：
1. 测试报告中的错误信息
2. tests/reports/screenshots/ 中的截图
3. 控制台输出的详细日志

## 🎉 测试完成

测试完成后，你将获得：
- ✅ 完整的功能验证报告
- 📊 详细的测试统计数据
- 📸 关键步骤的截图记录
- 🧹 干净的项目环境（测试代码已清理）

---

**准备好了吗？运行以下命令开始测试：**

```bash
python run_miniprogram_tests.py
```
