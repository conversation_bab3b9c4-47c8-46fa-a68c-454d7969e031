{"case_name": "test_02_navigate_all_main_pages", "run_time": "20250730 10:34:19", "test_type": "CompleteMiniProgramTest", "case_doc": "测试主要页面导航", "success": true, "failures": "", "errors": "", "start_timestamp": 1753842859.3043704, "is_failure": false, "is_error": false, "module": "E:.wx-nan.complete_miniprogram_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/home/<USER>", "path": "images\\setup.png", "ts": 1753842859, "datetime": "2025-07-30 10:34:19", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753842882, "datetime": "2025-07-30 10:34:42", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753842882.824953, "appId": "", "appName": "", "source": {"code": ["    def test_02_navigate_all_main_pages(self):\n", "        \"\"\"测试主要页面导航\"\"\"\n", "        print(\"🧪 测试主要页面导航\")\n", "        \n", "        # 主要页面列表\n", "        main_pages = [\n", "            (\"/pages/home/<USER>\", \"首页\"),\n", "            (\"/pages/order/index\", \"订餐页\"),\n", "            (\"/pages/mine/index\", \"个人中心\"),\n", "        ]\n", "        \n", "        for page_path, page_name in main_pages:\n", "            try:\n", "                print(f\"📱 导航到{page_name}: {page_path}\")\n", "                \n", "                # 导航到页面\n", "                if \"home\" in page_path or \"order\" in page_path or \"mine\" in page_path:\n", "                    self.app.switch_tab(page_path)\n", "                else:\n", "                    self.app.navigate_to(page_path)\n", "                \n", "                time.sleep(2)  # 等待页面加载\n", "                \n", "                # 验证页面\n", "                current_page = self.app.get_current_page()\n", "                actual_path = current_page.path\n", "                \n", "                print(f\"✅ 成功导航到: {actual_path}\")\n", "                \n", "                # 截图\n", "                screenshot_name = f\"02_navigate_{page_name.replace('页', '')}\"\n", "                self.take_screenshot(screenshot_name)\n", "                \n", "            except Exception as e:\n", "                print(f\"⚠️ 导航到{page_name}失败: {e}\")\n", "        \n", "        print(\"✅ 页面导航测试完成\")\n"], "start": 68}, "filename": "result.json"}