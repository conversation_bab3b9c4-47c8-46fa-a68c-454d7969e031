[I 2025-07-30 11:16:45,838 minium minitest#432 _miniSetUp] =========Current case: test_03_complete_order_flow_with_data=========
[I 2025-07-30 11:16:45,839 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.complete_functional_test, case info: CompleteFunctionalTest.test_03_complete_order_flow_with_data
[I 2025-07-30 11:16:45,839 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:16:45,839 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:16:45,840 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:16:45,840 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:16:45,840 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:16:45,867 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:16:45,867 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"5b0c52c7-a22c-43f2-9562-db292da623f7","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:45,954 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"5b0c52c7-a22c-43f2-9562-db292da623f7","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:16:45,955 minium minitest#487 _miniSetUp] =========case: test_03_complete_order_flow_with_data start=========
[I 2025-07-30 11:16:46,956 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:16:46,956 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"fd74858b-9900-47eb-808b-eaf418d5f27a","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:46,958 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"fd74858b-9900-47eb-808b-eaf418d5f27a","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:16:46,958 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:46,959 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"a5442f0e-413f-4b07-8b0d-77bdb332be0c","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:46,960 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"a5442f0e-413f-4b07-8b0d-77bdb332be0c","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:16:46,961 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [2639063586128]{"id":"3221ddb1-6ab2-4a19-a017-abf05bace134","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:16:46,963 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:16:57,019 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"3221ddb1-6ab2-4a19-a017-abf05bace134","error":{"message":"timeout"}}
[E 2025-07-30 11:16:57,019 minium.Conn6128 connection#668 __on_message] [3221ddb1-6ab2-4a19-a017-abf05bace134]: timeout
[I 2025-07-30 11:16:57,019 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 3221ddb1-6ab2-4a19-a017-abf05bace134
[W 2025-07-30 11:16:57,020 minium.App0832 app#1172 _change_route_async] 可能因频繁调用switchTab导致timeout
[W 2025-07-30 11:17:12,033 minium.App0832 app#1180 _change_route_async] recall switchTab
[D 2025-07-30 11:17:12,033 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [2639063586128]{"id":"290143ea-4e6c-43ac-ab67-94ee685f593f","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:17:12,035 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:17:12,039 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"290143ea-4e6c-43ac-ab67-94ee685f593f","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:17:12,040 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 290143ea-4e6c-43ac-ab67-94ee685f593f
[D 2025-07-30 11:17:27,044 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"45ba91fc-ad32-4788-8197-342e1a347191","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:17:27,046 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"45ba91fc-ad32-4788-8197-342e1a347191","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:17:27,046 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:17:27,047 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"36a7df39-ed33-4007-86b8-8c9c8afa6199","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:17:27,049 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"36a7df39-ed33-4007-86b8-8c9c8afa6199","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[W 2025-07-30 11:17:27,050 minium.App0832 app#1013 switch_tab] Switch tab(/pages/order/index) but(/pages/user_connection/index)
[D 2025-07-30 11:17:30,051 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"e5e1e2df-88cb-485f-99ff-c167cb1c9ab6","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:17:30,053 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"e5e1e2df-88cb-485f-99ff-c167cb1c9ab6","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:17:30,053 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:17:30,054 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"0c427915-366b-4bbc-a239-44737ec17e99","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:17:30,055 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"0c427915-366b-4bbc-a239-44737ec17e99","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:17:33,056 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item
[D 2025-07-30 11:17:33,057 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"33420e15-55ce-40f9-87ba-0df69024c91d","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item","pageId":14}}
[I 2025-07-30 11:18:33,073 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item
[D 2025-07-30 11:18:33,074 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"439b7a85-a622-453c-8544-2499a48aa9d0","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item","pageId":14}}
[I 2025-07-30 11:19:33,086 minium page#716 _get_elements_by_css] try to get elements: .cart-btn, .basket-btn, .shopping-cart, button[bindtap*='cart']
[D 2025-07-30 11:19:33,087 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"9fb6735f-1055-4e92-92af-b4b057ea44b7","method":"Page.getElements","params":{"selector":".cart-btn, .basket-btn, .shopping-cart, button[bindtap*='cart']","pageId":14}}
[D 2025-07-30 11:20:35,096 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"4a813458-9e64-4e74-b6ae-b9b9ceb53790","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:20:35,101 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"4a813458-9e64-4e74-b6ae-b9b9ceb53790","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:20:35,101 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:20:35,102 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"00ac866c-c980-4026-a782-86500fc14839","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:20:35,105 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"00ac866c-c980-4026-a782-86500fc14839","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:20:35,106 minium page#716 _get_elements_by_css] try to get elements: .van-toast, .toast, .success-tip
[D 2025-07-30 11:20:35,106 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"5a00c03a-aa79-480a-bd04-38cd04ab696b","method":"Page.getElements","params":{"selector":".van-toast, .toast, .success-tip","pageId":14}}
[D 2025-07-30 11:20:53,287 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"navigateBack","timeStamp":1753845653271,"webviewId":3,"routeEventId":"3_1753845653045","renderer":"webview"},1753845653273]}}
[I 2025-07-30 11:20:53,289 minium.App0832 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'navigateBack', 'timeStamp': 1753845653271, 'webviewId': 3, 'routeEventId': '3_1753845653045', 'renderer': 'webview'}, 1753845653273]}
[I 2025-07-30 11:20:53,291 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:20:53,292 minium minitest#799 _miniTearDown] =========Current case Down: test_03_complete_order_flow_with_data=========
[I 2025-07-30 11:20:53,293 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:20:53,293 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"9f9d0426-466c-401e-a3ff-b5c0f65ab857","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:20:53,343 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753845653340,"webviewId":2,"routeEventId":"2_1753845653279","renderer":"webview"},1753845653342]}}
[I 2025-07-30 11:20:53,347 minium.App0832 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753845653340, 'webviewId': 2, 'routeEventId': '2_1753845653279', 'renderer': 'webview'}, 1753845653342]}
[D 2025-07-30 11:20:53,422 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"9f9d0426-466c-401e-a3ff-b5c0f65ab857","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:20:53,423 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:20:53,424 minium basenative#63 wrapper] call BaseNative.get_start_up end 
