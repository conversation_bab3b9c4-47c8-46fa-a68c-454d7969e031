[I 2025-07-30 10:40:01,741 minium minitest#432 _miniSetUp] =========Current case: test_connection=========
[I 2025-07-30 10:40:01,741 minium minitest#435 _miniSetUp] package info: E:.wx-nan.connection_test, case info: ConnectionTest.test_connection
[I 2025-07-30 10:40:01,742 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:01,742 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:01,743 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:01,743 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:01,744 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:01,745 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:01,747 minium.Conn8160 connection#427 _safely_send] SEND > [2323241948160]{"id":"0f65ba4b-7774-44b2-9d50-ea2dd9044cff","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:01,893 minium.Conn8160 connection#660 __on_message] RECV < [2323241948160]{"id":"0f65ba4b-7774-44b2-9d50-ea2dd9044cff","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//dbfU3PzDtzJy8JuUlCCHIbEwXljopIuASVRXBZWVZ0D8VjXQV1ZVf0tyyuCl6IINcCyrEccihCEAKEkARykuS9JO/IO+fos+r3R3X31PT0zJv3kpBg6gPp952a6uqqnq5Pf7+fqupGy/EAAAAQkTEmbWlLW9r72I5YRkJCQmJ/gAAAIkafpS1taUt739rSl5GQkNi/UCNrP8VmlNL9Wr60pS3tvbQJIe8aXSYqVEJC4t0OMfDZS6jDZxkOklwkJP76IHole1nU6FlGkouExKGAvacbgWUQISKOmnZw1LrzB3UFGFF+aUtb2vvVFoeC6snPOz4ijvRYI9Nl6vFfpI8jIfGuRj0+y4j8mnojptrcI
[I 2025-07-30 10:40:01,895 minium minitest#487 _miniSetUp] =========case: test_connection start=========
[I 2025-07-30 10:40:01,895 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 10:40:02,068 minium minitest#897 capture] capture assertIsNotNone-success.png
[D 2025-07-30 10:40:02,068 minium.Conn8160 connection#427 _safely_send] SEND > [2323241948160]{"id":"00c565e8-de4c-470a-9450-22d1bde39cb4","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:02,161 minium.Conn8160 connection#660 __on_message] RECV < [2323241948160]{"id":"00c565e8-de4c-470a-9450-22d1bde39cb4","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHUWV/jnV293fkj15Sci+EYJAADFRUPaoiOyCyiA4jAwjOoviMo6COjIj+hsGRwU3RJBtAGUZFgm4IAjBhJAEspLkvSRvyVvv0mvV74/q7lu3b9/77ntJSCD1QfqdW7e6uqpv19fnfFXVjabtAgAAICJjTNrSlra097MdsoyEhITEgQABAEQMP0tb2tKW9v61pS8jISFxYKGG1gGKzSilB7R8aUtb2vtoE0LeNrpMWKiEhMTbHWLgs49Qh88yHCS5SEi88yB6JftY1OhZRpKLhMThgH2nG4FlECEkjrq2f9SG8/t1BRhRfmlLW9oH1BaHghrJzzs+Io70WCPTZRrxX6SPIyHxtkYjPsuI/JpGI6b63CGZR
[D 2025-07-30 10:40:02,163 minium.Conn8160 connection#427 _safely_send] SEND > [2323241948160]{"id":"668592fb-f541-4c36-b1d3-a61577ee8dfc","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:02,165 minium.Conn8160 connection#660 __on_message] RECV < [2323241948160]{"id":"668592fb-f541-4c36-b1d3-a61577ee8dfc","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:02,168 minium.App2864 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:02,170 minium.Conn8160 connection#427 _safely_send] SEND > [2323241948160]{"id":"ae43494f-fe1b-46e6-89a4-3b74ef92c63a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:02,172 minium.Conn8160 connection#660 __on_message] RECV < [2323241948160]{"id":"ae43494f-fe1b-46e6-89a4-3b74ef92c63a","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:02,174 minium minitest#897 capture] capture assertIsNotNone-success_104002174196.png
[D 2025-07-30 10:40:02,174 minium.Conn8160 connection#427 _safely_send] SEND > [2323241948160]{"id":"e871161a-a570-4a5d-9824-9faefa078d3a","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:02,262 minium.Conn8160 connection#660 __on_message] RECV < [2323241948160]{"id":"e871161a-a570-4a5d-9824-9faefa078d3a","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmAHEW9//dbfc2xM3vkTjYJCTlIQkjkFoMCckdBriCIyFPxofxQ9PlUntdTQd7zp8jvIT4V8EAEuR4CAk9AQEQMQoAQckAukt3NsbvZY3aOPqt+f1R3T01Pz+zsJiGB1AfS+52a6uqqnq5Pf7+fqupG03YBAAAQkTEmbWlLW9p72A5ZRkJCQmJvgAAAIoafpS1taUt7z9rSl5GQkNi7UENrL8VmlNK9Wr60pS3t3bQJIe8YXSYsVEJC4p0OMfDZTajDZxkOklwkJN59EL2S3Sxq9CwjyUVC4kDA7tONwDKIEBJHXds/asP5/boCjCi/tKUt7b1qi0NBjeTnHR8RR3qskekyjfgv0seRkHhHoxGfZUR+TaMRU
[I 2025-07-30 10:40:02,263 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:02,264 minium minitest#799 _miniTearDown] =========Current case Down: test_connection=========
[I 2025-07-30 10:40:02,264 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:02,264 minium.Conn8160 connection#427 _safely_send] SEND > [2323241948160]{"id":"d745f082-2ca5-4e61-84af-f51c2d7dc161","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:02,407 minium.Conn8160 connection#660 __on_message] RECV < [2323241948160]{"id":"d745f082-2ca5-4e61-84af-f51c2d7dc161","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//dbfc39jtzJS0JCDpIQgtxiUFDuqIjcgsqyoKwsK7qH4rGugrqyK/pbFlcFL0SQawHlWA45PBCEYEJIAuQiyXtJ3pF3zJujz6rfH9XdU9PTM2/eywsJpD6Qft+pqa6u6un69Pf7qapuNG0XAAAAERlj0pa2tKU9znbIMhISEhJ7AwQAEDH8LG1pS1va42tLX0ZCQmLvQg2tvRSbUUr3avnSlra099AmhLxtdJmwUAkJibc7xMBnD6GOnGUkSHKRkHjnQfRK9rCosbOMJBcJiQMBe043AssgQkgcDW3/qE3n9+sKMKr80pa2tPeqLQ4FNZOfd3xEHO2xRqfLNOO/SB9HQuJtjWZ8llH5Nc1GTI25Q
[I 2025-07-30 10:40:02,408 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:02,409 minium basenative#63 wrapper] call BaseNative.get_start_up end 
