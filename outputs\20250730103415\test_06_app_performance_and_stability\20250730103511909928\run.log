[I 2025-07-30 10:35:11,911 minium minitest#432 _miniSetUp] =========Current case: test_06_app_performance_and_stability=========
[I 2025-07-30 10:35:11,911 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_06_app_performance_and_stability
[I 2025-07-30 10:35:11,912 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:11,912 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:11,912 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:11,913 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:11,913 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:11,914 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:11,914 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"139cf7b4-6003-44e2-b1dc-428360f171d1","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:12,004 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"139cf7b4-6003-44e2-b1dc-428360f171d1","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:12,036 minium minitest#487 _miniSetUp] =========case: test_06_app_performance_and_stability start=========
[I 2025-07-30 10:35:13,037 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:13,038 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"f75cce4b-c65d-4d88-a4d9-bc4b523e0687","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:13,041 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:13,262 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842913259,"webviewId":2,"routeEventId":"2_1753842913060","renderer":"webview"},1753842913260]}}
[I 2025-07-30 10:35:13,264 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842913259, 'webviewId': 2, 'routeEventId': '2_1753842913060', 'renderer': 'webview'}, 1753842913260]}
[D 2025-07-30 10:35:13,268 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"f75cce4b-c65d-4d88-a4d9-bc4b523e0687","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:13,269 minium.Conn9680 connection#704 _handle_async_msg] received async msg: f75cce4b-c65d-4d88-a4d9-bc4b523e0687
[D 2025-07-30 10:35:13,772 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"a0dad50a-12d1-4e7a-8b90-e02e83a10a45","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:13,777 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"a0dad50a-12d1-4e7a-8b90-e02e83a10a45","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:13,778 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:13,779 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"49fde259-fa64-4705-b11a-de2e5ce5c5c7","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:13,783 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"49fde259-fa64-4705-b11a-de2e5ce5c5c7","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:13,789 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"524ed504-f378-44e5-ab0c-61a023871133","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:13,791 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:14,091 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842914023,"webviewId":5,"routeEventId":"5_1753842913809","renderer":"webview"},1753842914077]}}
[I 2025-07-30 10:35:14,096 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842914023, 'webviewId': 5, 'routeEventId': '5_1753842913809', 'renderer': 'webview'}, 1753842914077]}
[D 2025-07-30 10:35:14,118 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"524ed504-f378-44e5-ab0c-61a023871133","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:14,120 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 524ed504-f378-44e5-ab0c-61a023871133
[D 2025-07-30 10:35:14,622 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"03cae24d-cb2e-4295-83af-e3b203cb1327","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:14,627 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"03cae24d-cb2e-4295-83af-e3b203cb1327","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:35:14,629 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:14,630 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"4cd40a8b-f56a-42d5-862e-f3f844caea64","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:14,641 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"4cd40a8b-f56a-42d5-862e-f3f844caea64","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:14,644 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:14,649 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:14,943 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842914927,"webviewId":3,"routeEventId":"3_1753842914693","renderer":"webview"},1753842914936]}}
[I 2025-07-30 10:35:14,946 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842914927, 'webviewId': 3, 'routeEventId': '3_1753842914693', 'renderer': 'webview'}, 1753842914936]}
[D 2025-07-30 10:35:14,954 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:14,956 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec
[D 2025-07-30 10:35:15,458 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"3d88f374-0f09-4b44-97ed-fe88e97ba6b5","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:15,461 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"3d88f374-0f09-4b44-97ed-fe88e97ba6b5","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:35:15,461 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:15,461 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"c36293c5-c5e9-4911-aa65-c23aad12da51","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:15,464 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"c36293c5-c5e9-4911-aa65-c23aad12da51","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:15,465 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"eb9eb9dc-f338-4304-8fe7-5af06daca455","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:15,470 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:15,731 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842915713,"webviewId":2,"routeEventId":"2_1753842915485","renderer":"webview"},1753842915722]}}
[D 2025-07-30 10:35:15,733 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"eb9eb9dc-f338-4304-8fe7-5af06daca455","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:15,735 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842915713, 'webviewId': 2, 'routeEventId': '2_1753842915485', 'renderer': 'webview'}, 1753842915722]}
[I 2025-07-30 10:35:15,736 minium.Conn9680 connection#704 _handle_async_msg] received async msg: eb9eb9dc-f338-4304-8fe7-5af06daca455
[D 2025-07-30 10:35:16,239 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"b8ad26fd-7683-4bd9-ae11-148fb7517282","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:16,240 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"b8ad26fd-7683-4bd9-ae11-148fb7517282","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:16,241 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:16,241 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"5a6df102-2237-4898-bf13-c17fe4e61b71","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:16,242 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"5a6df102-2237-4898-bf13-c17fe4e61b71","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:16,243 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"ea07110b-41a1-466e-b74b-e884c0971dca","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:16,339 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"ea07110b-41a1-466e-b74b-e884c0971dca","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAHkWZP/48VX2817wzk/u+7xDCFYIYVFzu4MGNisqiKCtfVkR3V3Y91kVXZVeW37K4KrgqoigEFRBQRA4RDEKAEJIAuUgyk2NmMsc779FXVf3+qO5+++*****************************+Vd2NluMBAAAgohBCccUVV/wA81BlFBQUFA4GCAAgYvhaccUVV/zA8oMVy4TBkoKCwlsFUYE4gCA1DzBcDgAiAEQk5vBRU8UVV3xIHo7fcCwfmPr3M5ZRMYuCwtse+xnjaCP4jFIWBYUjCtEhPwLFGZ7KNKMvSoMUFN7SaKwjcoAPS2vKvgxEP1aLCyHq7SOiudxQ9SiuuOKHMxd1jJnYPs3X2awvUzNCq
[I 2025-07-30 10:35:16,340 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:16,341 minium minitest#799 _miniTearDown] =========Current case Down: test_06_app_performance_and_stability=========
[I 2025-07-30 10:35:16,341 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:16,341 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"3cca82ac-8611-452d-8173-2de58527adf6","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:16,409 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"3cca82ac-8611-452d-8173-2de58527adf6","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//db1cccb96V+yb3ASFcAcSwCnLHixsVb1FWlvXc366ux7rgeuyK7LK4KuKBKMqhghyKyKGIQQgQQhIgF0ney/HeyzvmzdFXVf3+qO6enp6eefNeEhJIfdDOZ2p6qqv6TX3m+/1UdTdajgcAAICIQgjFFVdc8f3MQ5VRUFBQOBAgAICI4WvFFVdc8f3LD1QsEwZLCgoKrxVEBWI/giQeYLQcAEQAiEjMoaOmiiuu+Ig8HL/hWN4/9e9jLKNiFgWF1z32McbRxvAZpSwKCocVokN+DIozOpVpRl+UBikovKbRWEfkAB+V1lR8GYh+LIkLIertI6K53Ej1KK644ocyF3WMmdg+zdfZrC+TGKHUC1tUO
[I 2025-07-30 10:35:16,410 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:16,411 minium basenative#63 wrapper] call BaseNative.get_start_up end 
