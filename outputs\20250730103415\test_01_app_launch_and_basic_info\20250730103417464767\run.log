[I 2025-07-30 10:34:17,472 minium minitest#432 _miniSetUp] =========Current case: test_01_app_launch_and_basic_info=========
[I 2025-07-30 10:34:17,472 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_01_app_launch_and_basic_info
[I 2025-07-30 10:34:17,473 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:17,473 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:17,473 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:17,474 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:17,474 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:17,489 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:17,489 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"3951dc8c-38a8-481b-b30f-fa78c80eb42c","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:17,590 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"3951dc8c-38a8-481b-b30f-fa78c80eb42c","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXeAHMWZNv6+1WnSzgZJqxxRBiEMCDAWZ+MjywZjgsHmMGeTDj5/xuH33TlwPg4cf2eO7ziwTXDA2NiEw4AFZ/ABtjEmSUgIBVBC0q602l1tmJ3Qser7o7p7anp6ZmdXkpXqAfW+U1NdXdUz9cz7PhUaTdsFAABARMaYtKUtbWnvYztkGQkJCYn9AQIAiBi+lra0pS3tfWtLX0ZCQmL/Qg2t/RSbUUr3a/nSlra099ImhBwyukxYqISExKEOMfDZS6jDZxkOklwkJA4/iF7JXhY1epaR5CIhcSRg7+lGYBlECImjru1fteH8fl0BRpRf2tKW9n61xaGgRvLzjo+II73WyHSZRvwX6eNISBzSaMRnGZFf02jEV
[I 2025-07-30 10:34:17,592 minium minitest#487 _miniSetUp] =========case: test_01_app_launch_and_basic_info start=========
[I 2025-07-30 10:34:18,593 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 10:34:18,802 minium minitest#897 capture] capture assertIsNotNone-success.png
[D 2025-07-30 10:34:18,802 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"61e870da-065e-4466-abf8-a2dd46775918","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:18,879 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"61e870da-065e-4466-abf8-a2dd46775918","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq5t++S5GZPICQhBELYQQQVZY8bCgjKICoIA8/nNm9mcGMY3MY3MrxhRGUZFVGUZRA0MIIjqIhsCYSQBbKR5SZ3y1369lLr+d4fp6q6urq6b9+bxARyfpC6X58+deqc6j6//r7fWQpN2wUAAEBEIpK2tKUt7b1shywjISEhsS/AAAARw9fSlra0pb13benLSEhI7FuoobWPYjPO+T4tX9rSlvYe2oyxN40uExYqISHxZkc08NlDqGNnGQuSXCQk3nqIeiV7WNTEWUaSi4TEwYA9p5sIyyBCSBwNbf+qTef36wowrvzSlra096kdHQpqJr/o+Ig43muNT5dpxn+RPo6ExJsazfgs4/Jrmo2YG
[D 2025-07-30 10:34:18,881 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"12291ce3-67cf-4803-a6cc-817cf2237153","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:18,883 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"12291ce3-67cf-4803-a6cc-817cf2237153","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:18,885 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:18,886 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"b25b0aa1-d3ea-4b47-899d-c50d1f30cb56","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:18,888 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"b25b0aa1-d3ea-4b47-899d-c50d1f30cb56","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:34:18,890 minium minitest#897 capture] capture assertIsNotNone-success_103418890827.png
[D 2025-07-30 10:34:18,891 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"7e6b3491-6ef6-412f-9231-36b79a9538de","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:18,963 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"7e6b3491-6ef6-412f-9231-36b79a9538de","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6+5261dFcvSTp7AiEJSSCEHURQUfa4oYCoDKKCIHx+brOIyzAMbuNvhuE3jKgsoyKKsgyCBkZwBBWRLYEQkgDZSNKddLo7vVTXctfzfn+ce2/dunWrurqTkEDOA7n91qlzzz3nVp2n3vc5y0XTdgEAABCRiKQtbWlLey/bIctISEhI7AswAEDE8LW0pS1tae9dW/oyEhIS+xZqaO2j2Ixzvk/Ll7a0pb2HNmPsTaPLhIVKSEi82RENfPYQ6thZxoIkFwmJtx6iXskeFjVxlpHkIiFxMGDP6SbCMogQEkdD279q0/n9ugKMK7+0pS3tfWpHh4KayS86PiKO91rj02Wa8V+kjyMh8aZGMz7LuPyaZ
[D 2025-07-30 10:34:18,988 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"8ee7fc8d-669c-4e27-95d9-ddb264b410e1","method":"App.callFunction","params":{"functionDeclaration":"wx.getSystemInfoSync()","args":[]}}
[D 2025-07-30 10:34:18,989 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"ea1730fb-be61-4eb4-84d9-bec5a31a4c36","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,004 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"8ee7fc8d-669c-4e27-95d9-ddb264b410e1","error":{"message":"Arg string terminates parameters early"}}
[E 2025-07-30 10:34:19,004 minium.Conn9680 connection#668 __on_message] [8ee7fc8d-669c-4e27-95d9-ddb264b410e1]: Arg string terminates parameters early
[I 2025-07-30 10:34:19,005 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 8ee7fc8d-669c-4e27-95d9-ddb264b410e1
[D 2025-07-30 10:34:19,087 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"ea1730fb-be61-4eb4-84d9-bec5a31a4c36","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6+5661dPWSpLMnJCQhCYSwBRBBRdnjhgKCMogKwsDn5/p9M7iMMriNv5HhNwyoLKMiirIMggRGcAQVkS2BEJIA2UjSnXS6O71U13LXc74/zr23Tt26VV3dSUhCzgO5/dapc88951adp973OctFy/EAAAAQkTEmbWlLW9p72Y5YRkJCQmJfgAAAIkavpS1taUt779rSl5GQkNi3UCNrH8VmlNJ9Wr60pS3tPbQJIQeNLhMVKiEhcbBDDHz2EOroWUaDJBcJibcfRK9kD4saP8tIcpGQOBSw53QjsAwiRMTR0A6u2nT+oK4AY8ovbWlLe5/a4lBQM/l5x0fEsV5rbLpMM/6L9HEkJA5qNOOzjMmva
[I 2025-07-30 10:34:19,089 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:34:19,089 minium minitest#799 _miniTearDown] =========Current case Down: test_01_app_launch_and_basic_info=========
[I 2025-07-30 10:34:19,089 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:34:19,090 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"10cce0aa-fb5f-4589-a758-93c39eecdc7d","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,164 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"10cce0aa-fb5f-4589-a758-93c39eecdc7d","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXeAHMWV93vVadLOBkmrnAOSQIgkwFjYgMlywibaHMY2GM6cP4fz3Rmfw3E4nP3dYb7j4GzC2cbY2ARjMIIzYINtjEkSCKEASkjalTZpw+yEjlXfH9XdU9PTMzu7kpCE6gfqfVNTXV3VM/Wb934VGk3bBQAAQETGmLSlLW1p72M7ZBkJCQmJ/QECAIgYvpa2tKUt7X1rS19GQkJi/0INrf0Um1FK92v50pa2tPfSJoQcMrpMWKiEhMShDjHw2UuoI2cZCZJcJCTeeRC9kr0sauwsI8lFQuJwwN7TjcAyiBASR13bv2rD+f26Aowqv7SlLe39aotDQY3k5x0fEUd7rdHpMo34L9LHkZA4pNGIzzIqv6bRiKk+d
[I 2025-07-30 10:34:19,167 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:34:19,167 minium basenative#63 wrapper] call BaseNative.get_start_up end 
