{"case_name": "test_05_mine_page_navigation", "run_time": "20250730 10:35:07", "test_type": "CompleteMiniProgramTest", "case_doc": "测试个人中心页面导航", "success": true, "failures": "", "errors": "", "start_timestamp": 1753842907.1518936, "is_failure": false, "is_error": false, "module": "E:.wx-nan.complete_miniprogram_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/order/index", "path": "images\\setup.png", "ts": 1753842907, "datetime": "2025-07-30 10:35:07", "use_region": false}, {"name": "assertIn-success", "url": "/pages/mine/index", "path": "images\\assertIn-success.png", "ts": 1753842911, "datetime": "2025-07-30 10:35:11", "use_region": false}, {"name": "teardown", "url": "/pages/mine/index", "path": "images\\teardown.png", "ts": 1753842911, "datetime": "2025-07-30 10:35:11", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [{"name": "assertIn", "ret": true, "msg": null, "img": "assertIn-success", "wxml": ""}], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753842911.9082754, "appId": "", "appName": "", "source": {"code": ["    def test_05_mine_page_navigation(self):\n", "        \"\"\"测试个人中心页面导航\"\"\"\n", "        print(\"🧪 测试个人中心页面导航\")\n", "        \n", "        try:\n", "            # 导航到个人中心\n", "            self.app.switch_tab(\"/pages/mine/index\")\n", "            time.sleep(3)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 检查页面是否加载\n", "            self.assertIn(\"mine\", current_page.path, \"应该在个人中心页面\")\n", "            \n", "            # 查找菜单项\n", "            try:\n", "                menu_elements = current_page.get_elements(\"view[bindtap]\")\n", "                print(f\"📋 找到 {len(menu_elements)} 个菜单项\")\n", "                \n", "                # 检查菜单项文本\n", "                menu_texts = []\n", "                for element in menu_elements[:5]:  # 只检查前5个\n", "                    try:\n", "                        text = element.inner_text\n", "                        if text and text.strip():\n", "                            menu_texts.append(text.strip())\n", "                    except:\n", "                        pass\n", "                \n", "                print(f\"📋 菜单项文本: {menu_texts}\")\n", "                \n", "            except Exception as e:\n", "                print(f\"⚠️ 查找菜单项失败: {e}\")\n", "            \n", "            self.take_screenshot(\"05_mine_navigation\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ 个人中心页面测试失败: {e}\")\n", "        \n", "        print(\"✅ 个人中心页面测试完成\")\n"], "start": 185}, "filename": "result.json"}