{"case_name": "test_06_app_performance_and_stability", "run_time": "20250730 10:35:11", "test_type": "CompleteMiniProgramTest", "case_doc": "测试应用性能和稳定性", "success": true, "failures": "", "errors": "", "start_timestamp": 1753842912.036823, "is_failure": false, "is_error": false, "module": "E:.wx-nan.complete_miniprogram_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/mine/index", "path": "images\\setup.png", "ts": 1753842912, "datetime": "2025-07-30 10:35:12", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753842916, "datetime": "2025-07-30 10:35:16", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753842916.4432313, "appId": "", "appName": "", "source": {"code": ["    def test_06_app_performance_and_stability(self):\n", "        \"\"\"测试应用性能和稳定性\"\"\"\n", "        print(\"🧪 测试应用性能和稳定性\")\n", "        \n", "        try:\n", "            # 快速切换页面测试稳定性\n", "            pages = [\n", "                \"/pages/home/<USER>\",\n", "                \"/pages/order/index\", \n", "                \"/pages/mine/index\",\n", "                \"/pages/home/<USER>\"\n", "            ]\n", "            \n", "            for i, page_path in enumerate(pages):\n", "                print(f\"🔄 快速切换到: {page_path}\")\n", "                self.app.switch_tab(page_path)\n", "                time.sleep(0.5)  # 短暂等待\n", "                \n", "                # 验证页面切换成功\n", "                current_page = self.app.get_current_page()\n", "                if page_path.split('/')[-2] in current_page.path:\n", "                    print(f\"✅ 页面切换成功 ({i+1}/{len(pages)})\")\n", "                else:\n", "                    print(f\"⚠️ 页面切换可能失败 ({i+1}/{len(pages)})\")\n", "            \n", "            # 最终截图\n", "            self.take_screenshot(\"06_performance_test\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ 性能稳定性测试失败: {e}\")\n", "        \n", "        print(\"✅ 性能稳定性测试完成\")\n"], "start": 226}, "filename": "result.json"}