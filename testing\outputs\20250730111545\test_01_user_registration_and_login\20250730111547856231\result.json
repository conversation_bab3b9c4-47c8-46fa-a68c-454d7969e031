{"case_name": "test_01_user_registration_and_login", "run_time": "20250730 11:15:47", "test_type": "CompleteFunctionalTest", "case_doc": "测试用户注册和登录功能", "success": true, "failures": "", "errors": "", "start_timestamp": 1753845348.2001925, "is_failure": false, "is_error": false, "module": "E:.wx-nan.testing.complete_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "", "path": "images\\setup.png", "ts": 1753845348, "datetime": "2025-07-30 11:15:48", "use_region": false}, {"name": "teardown", "url": "/pages/mine/index", "path": "images\\teardown.png", "ts": 1753845390, "datetime": "2025-07-30 11:16:30", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753845390.0292342, "appId": "", "appName": "", "source": {"code": ["    def test_01_user_registration_and_login(self):\n", "        \"\"\"测试用户注册和登录功能\"\"\"\n", "        print(\"🧪 测试用户注册和登录功能\")\n", "        \n", "        try:\n", "            # 导航到登录页面\n", "            self.app.navigate_to(\"/pages/login/index\")\n", "            time.sleep(2)\n", "            \n", "            login_page = self.app.get_current_page()\n", "            self.assertIn(\"login\", login_page.path)\n", "            \n", "            # 测试注册页面跳转\n", "            register_links = login_page.get_elements(\"text[bindtap*='register'], .register-link\")\n", "            if register_links:\n", "                register_links[0].click()\n", "                time.sleep(2)\n", "                \n", "                register_page = self.app.get_current_page()\n", "                if \"register\" in register_page.path:\n", "                    self.log_result(\"注册页面跳转\", True, \"成功跳转到注册页\", critical=True)\n", "                    \n", "                    # 测试注册表单\n", "                    self._test_registration_form(register_page)\n", "                    \n", "                    # 返回登录页面\n", "                    self.app.navigate_back()\n", "                    time.sleep(1)\n", "                else:\n", "                    self.log_result(\"注册页面跳转\", False, \"未跳转到注册页\", critical=True)\n", "            else:\n", "                self.log_result(\"注册入口\", False, \"未找到注册入口\", critical=True)\n", "            \n", "            # 测试登录功能\n", "            self._test_login_functionality(login_page)\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"用户注册登录测试\", False, f\"测试异常: {e}\", critical=True)\n"], "start": 137}, "filename": "result.json"}