{"test_time": "2025-07-30T10:42:37.751635", "duration_seconds": 52.351108, "framework_tests": {"total": 4, "passed": 4, "failed": 0, "errors": 0}, "functional_tests": {"total": 20, "passed": 16, "failed": 4}, "overall_success": false, "detailed_results": [{"test_name": "首页API数据加载", "success": true, "details": "页面内容长度: 89", "timestamp": "2025-07-30T10:42:44.526183"}, {"test_name": "首页UI元素", "success": true, "details": "找到40个UI元素", "timestamp": "2025-07-30T10:42:44.534991"}, {"test_name": "订餐页菜品数据", "success": false, "details": "可交互元素较少: 2", "timestamp": "2025-07-30T10:42:47.926431"}, {"test_name": "个人中心菜单", "success": true, "details": "找到6个菜单项", "timestamp": "2025-07-30T10:42:51.348404"}, {"test_name": "功能入口-用户关联", "success": true, "details": "找到入口: 用户关联", "timestamp": "2025-07-30T10:43:09.603782"}, {"test_name": "功能入口-我的菜品", "success": true, "details": "找到入口: 我的菜品", "timestamp": "2025-07-30T10:43:09.607689"}, {"test_name": "功能入口-通知中心", "success": true, "details": "找到入口: 通知中心", "timestamp": "2025-07-30T10:43:09.610887"}, {"test_name": "功能入口-新增菜品", "success": false, "details": "未找到入口", "timestamp": "2025-07-30T10:43:09.620180"}, {"test_name": "功能入口-消息", "success": false, "details": "未找到入口", "timestamp": "2025-07-30T10:43:09.620318"}, {"test_name": "页面交互跳转", "success": true, "details": "从/pages/mine/index跳转到/pages/family_message/index", "timestamp": "2025-07-30T10:43:12.775004"}, {"test_name": "订餐页面交互元素", "success": true, "details": "找到2个可交互元素", "timestamp": "2025-07-30T10:43:18.541353"}, {"test_name": "订餐交互-1", "success": true, "details": "点击了: 小菜\n今天\n火锅\n麻\n加入购物车", "timestamp": "2025-07-30T10:43:20.707026"}, {"test_name": "订餐交互-2", "success": true, "details": "点击了: 红烧肉\n今天\n火锅\n甜\n加入购物车", "timestamp": "2025-07-30T10:43:22.796702"}, {"test_name": "购物车功能", "success": false, "details": "未找到购物车元素", "timestamp": "2025-07-30T10:43:22.807820"}, {"test_name": "页面切换-1", "success": true, "details": "成功切换到home", "timestamp": "2025-07-30T10:43:25.871990"}, {"test_name": "页面切换-2", "success": true, "details": "成功切换到order", "timestamp": "2025-07-30T10:43:27.124974"}, {"test_name": "页面切换-3", "success": true, "details": "成功切换到mine", "timestamp": "2025-07-30T10:43:28.757904"}, {"test_name": "页面切换-4", "success": true, "details": "成功切换到home", "timestamp": "2025-07-30T10:43:30.011325"}, {"test_name": "应用状态-页面栈", "success": true, "details": "页面栈深度: 2da30c8d-b9ca-41b3-94d6-0a5eedd19793", "timestamp": "2025-07-30T10:43:30.012083"}, {"test_name": "应用状态-存储", "success": true, "details": "存储信息: 0b2d930e-c59a-4495-a8d3-64939307309b", "timestamp": "2025-07-30T10:43:30.012631"}], "summary": {"api_connectivity": "测试API连接和数据加载", "user_interaction": "测试用户界面交互功能", "order_flow": "测试订餐流程模拟", "data_persistence": "测试数据持久化和状态管理"}}