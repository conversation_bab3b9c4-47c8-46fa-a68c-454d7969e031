{"case_name": "test_05_dish_management_functionality", "run_time": "20250730 10:40:56", "test_type": "ComprehensiveFunctionalTest", "case_doc": "测试菜品管理功能", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843256.6590424, "is_failure": false, "is_error": false, "module": "E:.wx-nan.comprehensive_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/order/index", "path": "images\\setup.png", "ts": 1753843256, "datetime": "2025-07-30 10:40:56", "use_region": false}, {"name": "teardown", "url": "/pages/dish/my-dishes/index", "path": "images\\teardown.png", "ts": 1753843264, "datetime": "2025-07-30 10:41:04", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843264.1697912, "appId": "", "appName": "", "source": {"code": ["    def test_05_dish_management_functionality(self):\n", "        \"\"\"测试菜品管理功能\"\"\"\n", "        print(\"🧪 测试菜品管理功能\")\n", "        \n", "        try:\n", "            # 导航到个人中心\n", "            self.app.switch_tab(\"/pages/mine/index\")\n", "            time.sleep(2)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 查找菜品管理入口\n", "            management_found = False\n", "            clickable_elements = current_page.get_elements(\"view[bindtap], button\")\n", "            \n", "            for element in clickable_elements:\n", "                try:\n", "                    text = element.inner_text\n", "                    if \"我的菜品\" in text or \"菜品管理\" in text:\n", "                        management_found = True\n", "                        self.log_test_result(\"菜品管理入口\", True, f\"找到管理入口: {text}\")\n", "                        \n", "                        # 点击进入菜品管理\n", "                        element.click()\n", "                        time.sleep(3)\n", "                        \n", "                        # 检查菜品管理页面\n", "                        new_page = self.app.get_current_page()\n", "                        if \"dish\" in new_page.path or \"菜品\" in new_page.path:\n", "                            self.log_test_result(\"菜品管理页面\", True, f\"跳转到: {new_page.path}\")\n", "                            self._test_dish_management_features(new_page)\n", "                        break\n", "                except:\n", "                    continue\n", "            \n", "            if not management_found:\n", "                # 尝试查找新增菜品入口\n", "                for element in clickable_elements:\n", "                    try:\n", "                        text = element.inner_text\n", "                        if \"新增菜品\" in text or \"添加菜品\" in text:\n", "                            self.log_test_result(\"新增菜品入口\", True, f\"找到新增入口: {text}\")\n", "                            \n", "                            element.click()\n", "                            time.sleep(3)\n", "                            \n", "                            new_page = self.app.get_current_page()\n", "                            if \"add\" in new_page.path or \"新增\" in new_page.path:\n", "                                self.log_test_result(\"新增菜品页面\", True, f\"跳转到: {new_page.path}\")\n", "                                self._test_add_dish_features(new_page)\n", "                            break\n", "                    except:\n", "                        continue\n", "            \n", "        except Exception as e:\n", "            self.log_test_result(\"菜品管理测试\", False, f\"测试异常: {e}\")\n"], "start": 328}, "filename": "result.json"}