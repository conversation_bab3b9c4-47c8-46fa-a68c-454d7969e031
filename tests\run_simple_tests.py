#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """运行简化测试"""
    print("🚀 开始运行楠楠家厨小程序简化测试")
    print("=" * 60)
    print("📱 小程序AppID: wx82283b353918af82")
    print("🌐 测试环境: http://8.148.231.104:3000/api")
    print("🧪 测试框架: minium")
    print("=" * 60)
    
    # 检查minium
    try:
        import minium
        print(f"✅ minium框架版本: {minium.__version__}")
    except ImportError as e:
        print(f"❌ minium框架导入失败: {e}")
        return False
    
    # 创建报告目录
    reports_dir = "tests/reports"
    screenshots_dir = "tests/reports/screenshots"
    os.makedirs(reports_dir, exist_ok=True)
    os.makedirs(screenshots_dir, exist_ok=True)
    
    # 运行测试
    try:
        from test_simple import TestRunner
        runner = TestRunner()
        success = runner.run_tests()
        
        # 生成简单报告
        report_data = {
            "test_time": datetime.now().isoformat(),
            "success": success,
            "results": runner.results
        }
        
        # 保存JSON报告
        with open(os.path.join(reports_dir, "simple_test_report.json"), 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        # 生成HTML报告
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>楠楠家厨小程序简化测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
        .container {{ max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 30px; }}
        .card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }}
        .success {{ color: #28a745; }}
        .failed {{ color: #dc3545; }}
        .total {{ color: #007bff; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 楠楠家厨小程序简化测试报告</h1>
            <p>测试时间: {report_data['test_time']}</p>
        </div>
        
        <div class="summary">
            <div class="card">
                <h3>总测试数</h3>
                <div class="total" style="font-size: 2em;">{runner.results['total_tests']}</div>
            </div>
            <div class="card">
                <h3>通过测试</h3>
                <div class="success" style="font-size: 2em;">{runner.results['passed_tests']}</div>
            </div>
            <div class="card">
                <h3>失败测试</h3>
                <div class="failed" style="font-size: 2em;">{runner.results['failed_tests']}</div>
            </div>
        </div>
        
        <div class="card">
            <h3>测试结果</h3>
            <p>{'✅ 所有测试通过！' if success else '❌ 部分测试失败！'}</p>
        </div>
    </div>
</body>
</html>
"""
        
        with open(os.path.join(reports_dir, "simple_test_report.html"), 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"\n📊 测试报告已生成:")
        print(f"  JSON报告: {os.path.join(reports_dir, 'simple_test_report.json')}")
        print(f"  HTML报告: {os.path.join(reports_dir, 'simple_test_report.html')}")
        
        return success
        
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if success:
        print("\n🎉 测试完成！")
    else:
        print("\n❌ 测试失败！")
