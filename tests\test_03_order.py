#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import unittest
from minium import minitest
from utils.base_test import BaseTest


class TestOrder(BaseTest):
    """订餐功能测试"""
    
    def setUp(self):
        super().setUp()
        # 确保用户已登录
        self.clear_app_data()
        login_success = self.login_with_account()
        if not login_success:
            self.fail("登录失败，无法进行订餐测试")
    
    def test_01_order_page_elements(self):
        """测试订餐页面基本元素"""
        print("🧪 测试订餐页面基本元素")
        
        # 导航到订餐页面
        self.switch_to_tab("/pages/order/index")
        
        # 验证页面标题
        try:
            page_title = self.page.get_element(".page-title, .navbar-title")
            if page_title:
                print(f"📄 页面标题: {page_title.inner_text}")
        except:
            print("⚠️ 页面标题未找到")
        
        # 验证分类选择器
        try:
            category_selector = self.assert_element_exists(".category-selector, .categories")
            print("✅ 分类选择器存在")
            
            # 检查分类项
            category_items = self.page.get_elements(".category-item, .category-tab")
            print(f"📋 找到 {len(category_items)} 个分类")
        except:
            print("⚠️ 分类选择器未找到")
        
        # 验证菜品列表区域
        self.assert_element_exists(".dishes-main, .food-list")
        
        # 验证购物车按钮
        try:
            basket_btn = self.page.get_element(".basket-btn, .cart-btn")
            if basket_btn:
                print("✅ 购物车按钮存在")
        except:
            print("⚠️ 购物车按钮未找到")
        
        self.take_screenshot("order_page_elements")
        print("✅ 订餐页面基本元素验证完成")
    
    def test_02_category_switching(self):
        """测试分类切换功能"""
        print("🧪 测试分类切换功能")
        
        self.switch_to_tab("/pages/order/index")
        time.sleep(2)
        
        try:
            # 获取所有分类
            category_items = self.page.get_elements(".category-item, .category-tab")
            
            if len(category_items) > 1:
                # 记录初始分类
                initial_category = category_items[0]
                initial_text = initial_category.inner_text
                print(f"📂 初始分类: {initial_text}")
                
                # 点击第二个分类
                second_category = category_items[1]
                second_text = second_category.inner_text
                print(f"📂 切换到分类: {second_text}")
                
                second_category.click()
                time.sleep(2)
                
                # 验证分类切换效果
                # 检查菜品列表是否有变化
                dishes = self.page.get_elements(".dish-card, .food-item")
                print(f"🍽️ 当前分类下有 {len(dishes)} 个菜品")
                
                # 切换回第一个分类
                initial_category.click()
                time.sleep(2)
                
                print("✅ 分类切换功能正常")
            else:
                print("⚠️ 分类数量不足，无法测试切换")
        
        except Exception as e:
            print(f"⚠️ 分类切换测试异常: {e}")
        
        self.take_screenshot("category_switching")
        print("✅ 分类切换功能测试完成")
    
    def test_03_dish_selection(self):
        """测试菜品选择功能"""
        print("🧪 测试菜品选择功能")
        
        self.switch_to_tab("/pages/order/index")
        time.sleep(2)
        
        try:
            # 获取菜品列表
            dishes = self.page.get_elements(".dish-card, .food-item")
            
            if dishes:
                # 选择第一个菜品
                first_dish = dishes[0]
                
                # 获取菜品名称
                dish_name_element = first_dish.get_element(".dish-name, .food-name")
                dish_name = dish_name_element.inner_text if dish_name_element else "未知菜品"
                print(f"🍽️ 选择菜品: {dish_name}")
                
                # 查找添加按钮
                add_btn = first_dish.get_element(".add-btn, .plus-btn, .van-stepper__plus")
                if add_btn:
                    # 点击添加按钮
                    add_btn.click()
                    time.sleep(1)
                    
                    # 验证数量变化
                    count_element = first_dish.get_element(".count, .van-stepper__input")
                    if count_element:
                        count = count_element.value or count_element.inner_text
                        print(f"📊 菜品数量: {count}")
                        
                        # 再次点击添加
                        add_btn.click()
                        time.sleep(1)
                        
                        # 验证数量再次变化
                        new_count = count_element.value or count_element.inner_text
                        print(f"📊 更新后数量: {new_count}")
                    
                    # 测试减少按钮
                    minus_btn = first_dish.get_element(".minus-btn, .van-stepper__minus")
                    if minus_btn:
                        minus_btn.click()
                        time.sleep(1)
                        print("✅ 减少按钮功能正常")
                    
                    print("✅ 菜品选择功能正常")
                else:
                    print("⚠️ 添加按钮未找到")
            else:
                print("⚠️ 未找到菜品")
        
        except Exception as e:
            print(f"⚠️ 菜品选择测试异常: {e}")
        
        self.take_screenshot("dish_selection")
        print("✅ 菜品选择功能测试完成")
    
    def test_04_dish_detail_view(self):
        """测试菜品详情查看"""
        print("🧪 测试菜品详情查看")
        
        self.switch_to_tab("/pages/order/index")
        time.sleep(2)
        
        try:
            # 获取菜品列表
            dishes = self.page.get_elements(".dish-card, .food-item")
            
            if dishes:
                # 点击第一个菜品查看详情
                first_dish = dishes[0]
                first_dish.click()
                time.sleep(2)
                
                # 验证是否跳转到详情页
                current_page = self.app.get_current_page()
                if "detail" in current_page.path:
                    print("✅ 成功跳转到菜品详情页")
                    
                    # 验证详情页元素
                    self.assert_element_exists(".detail-image, .dish-image")
                    self.assert_element_exists(".detail-name, .dish-name")
                    
                    # 查找描述信息
                    try:
                        description = self.page.get_element(".detail-description, .dish-description")
                        if description:
                            print(f"📝 菜品描述: {description.inner_text[:50]}...")
                    except:
                        print("⚠️ 菜品描述未找到")
                    
                    # 查找配料信息
                    try:
                        ingredients = self.page.get_element(".ingredients, .material")
                        if ingredients:
                            print(f"🥬 配料信息: {ingredients.inner_text[:50]}...")
                    except:
                        print("⚠️ 配料信息未找到")
                    
                    self.take_screenshot("dish_detail_view")
                    
                    # 返回订餐页面
                    self.go_back()
                    print("✅ 菜品详情查看功能正常")
                else:
                    print("⚠️ 未跳转到详情页")
            else:
                print("⚠️ 未找到菜品")
        
        except Exception as e:
            print(f"⚠️ 菜品详情查看测试异常: {e}")
        
        print("✅ 菜品详情查看测试完成")
    
    def test_05_shopping_cart(self):
        """测试购物车功能"""
        print("🧪 测试购物车功能")
        
        self.switch_to_tab("/pages/order/index")
        time.sleep(2)
        
        try:
            # 先添加一些菜品到购物车
            dishes = self.page.get_elements(".dish-card, .food-item")
            
            if dishes:
                # 添加第一个菜品
                first_dish = dishes[0]
                add_btn = first_dish.get_element(".add-btn, .plus-btn, .van-stepper__plus")
                if add_btn:
                    add_btn.click()
                    add_btn.click()  # 添加2个
                    time.sleep(1)
                
                # 如果有第二个菜品，也添加一个
                if len(dishes) > 1:
                    second_dish = dishes[1]
                    add_btn2 = second_dish.get_element(".add-btn, .plus-btn, .van-stepper__plus")
                    if add_btn2:
                        add_btn2.click()
                        time.sleep(1)
            
            # 检查购物车按钮状态
            basket_btn = self.page.get_element(".basket-btn, .cart-btn")
            if basket_btn:
                # 检查购物车数量显示
                count_badge = self.page.get_element(".basket-count, .cart-count, .van-badge")
                if count_badge:
                    count = count_badge.inner_text
                    print(f"🛒 购物车数量: {count}")
                
                # 点击购物车按钮
                basket_btn.click()
                time.sleep(2)
                
                # 验证是否跳转到购物车页面
                current_page = self.app.get_current_page()
                if "today_order" in current_page.path or "cart" in current_page.path:
                    print("✅ 成功跳转到购物车页面")
                    
                    # 验证购物车页面元素
                    try:
                        self.assert_element_exists(".order-list, .cart-list")
                        print("✅ 购物车列表存在")
                    except:
                        print("⚠️ 购物车列表未找到")
                    
                    self.take_screenshot("shopping_cart")
                    
                    # 返回订餐页面
                    self.go_back()
                else:
                    print("⚠️ 未跳转到购物车页面")
            else:
                print("⚠️ 购物车按钮未找到")
        
        except Exception as e:
            print(f"⚠️ 购物车功能测试异常: {e}")
        
        print("✅ 购物车功能测试完成")
    
    def test_06_search_functionality(self):
        """测试搜索功能"""
        print("🧪 测试搜索功能")
        
        self.switch_to_tab("/pages/order/index")
        time.sleep(2)
        
        try:
            # 查找搜索框
            search_input = self.page.get_element(".search-input, input[placeholder*='搜索']")
            
            if search_input:
                # 输入搜索关键词
                search_keyword = "鸡"
                search_input.input(search_keyword)
                time.sleep(2)
                
                print(f"🔍 搜索关键词: {search_keyword}")
                
                # 检查搜索结果
                dishes = self.page.get_elements(".dish-card, .food-item")
                print(f"🔍 搜索结果: {len(dishes)} 个菜品")
                
                # 验证搜索结果是否包含关键词
                if dishes:
                    first_dish = dishes[0]
                    dish_name_element = first_dish.get_element(".dish-name, .food-name")
                    if dish_name_element:
                        dish_name = dish_name_element.inner_text
                        print(f"🍽️ 第一个搜索结果: {dish_name}")
                
                # 清空搜索
                search_input.input("")
                time.sleep(2)
                
                print("✅ 搜索功能正常")
            else:
                print("⚠️ 搜索框未找到")
        
        except Exception as e:
            print(f"⚠️ 搜索功能测试异常: {e}")
        
        self.take_screenshot("search_functionality")
        print("✅ 搜索功能测试完成")


if __name__ == '__main__':
    unittest.main()
