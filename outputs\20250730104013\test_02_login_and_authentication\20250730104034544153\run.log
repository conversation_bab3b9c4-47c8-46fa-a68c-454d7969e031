[I 2025-07-30 10:40:34,586 minium minitest#432 _miniSetUp] =========Current case: test_02_login_and_authentication=========
[I 2025-07-30 10:40:34,587 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_02_login_and_authentication
[I 2025-07-30 10:40:34,588 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:34,589 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:34,589 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:34,589 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:34,590 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:34,594 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:34,595 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"48055cb7-918c-40fb-b6e6-e2f93e6f4cd9","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:34,680 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"48055cb7-918c-40fb-b6e6-e2f93e6f4cd9","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgXEWZNv6+dbbe75LkZk8gJCEJhCAQgkhUHPa4sYigDDIMCMrHhzr+ZtQRHQe38VNkhoFRwEERRRBEkMAIDkFFBEkCIWSBbGS5yd1yl769nLXq90edc7r69Om+fW8Sk5B6IOe+XV2nTtXprqff96nloGm7AAAAiMgYk7a0pS3t/WyHLCMhISFxIEAAABHD19KWtrSlvX9t6ctISEgcWKihdYBiM0rpAS1f2tKW9j7ahJDDRpcJC5WQkDjcIQY++wh15CwjQZKLhMTbD6JXso9FjZ1lJLlISBwJ2He6EVgGEULiaGj7V206v19XgFHll7a0pX1AbXEoqJn8vOMj4mivNTpdphn/Rfo4EhKHNZrxWUbl1zQbM
[I 2025-07-30 10:40:34,683 minium minitest#487 _miniSetUp] =========case: test_02_login_and_authentication start=========
[I 2025-07-30 10:40:35,685 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 10:40:35,686 minium.App1680 app#891 navigate_to] NavigateTo: /pages/login/index
[D 2025-07-30 10:40:35,686 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [2264960766976]{"id":"aae6510c-0e09-4ac4-bd8d-9c3ca4425e5c","method":"App.callWxMethod","params":{"method":"navigateTo","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 10:40:35,688 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 10:40:38,779 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/login/index","query":{},"openType":"navigateTo","timeStamp":1753843238771,"webviewId":6,"routeEventId":"6_1753843238144","renderer":"webview"},1753843238772]}}
[I 2025-07-30 10:40:38,780 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/login/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843238771, 'webviewId': 6, 'routeEventId': '6_1753843238144', 'renderer': 'webview'}, 1753843238772]}
[D 2025-07-30 10:40:38,782 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"aae6510c-0e09-4ac4-bd8d-9c3ca4425e5c","result":{"result":{"errMsg":"navigateTo:ok","eventChannel":{"listener":{}}}}}
[I 2025-07-30 10:40:38,783 minium.Conn6976 connection#704 _handle_async_msg] received async msg: aae6510c-0e09-4ac4-bd8d-9c3ca4425e5c
[D 2025-07-30 10:40:40,784 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"f561307e-c1d6-45ce-a486-636a8a4608ee","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:40,787 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"f561307e-c1d6-45ce-a486-636a8a4608ee","result":{"pageId":6,"path":"pages/login/index","query":{}}}
[D 2025-07-30 10:40:40,787 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:40,787 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"69308374-bc32-443e-91a2-386a54e3bd86","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:40,789 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"69308374-bc32-443e-91a2-386a54e3bd86","result":{"result":{"pageId":6,"path":"pages/login/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:40,790 minium page#716 _get_elements_by_css] try to get elements: .wechat-login-btn, button[bindtap*='wechat']
[D 2025-07-30 10:40:40,790 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"67f5bb2b-f76e-4f24-9dd2-70568cd94333","method":"Page.getElements","params":{"selector":".wechat-login-btn, button[bindtap*='wechat']","pageId":6}}
[D 2025-07-30 10:40:40,795 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"67f5bb2b-f76e-4f24-9dd2-70568cd94333","result":{"elements":[]}}
[W 2025-07-30 10:40:40,795 minium page#747 _get_elements_by_css] Could not found any element '.wechat-login-btn, button[bindtap*='wechat']' you need
[I 2025-07-30 10:40:40,796 minium page#716 _get_elements_by_css] try to get elements: .tab-item, .login-tab
[D 2025-07-30 10:40:40,796 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"a325dfe9-3449-44b3-8064-81fe67b36f1d","method":"Page.getElements","params":{"selector":".tab-item, .login-tab","pageId":6}}
[D 2025-07-30 10:40:40,801 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"a325dfe9-3449-44b3-8064-81fe67b36f1d","result":{"elements":[{"elementId":"0b7d4374-58d4-4f6b-add9-a76cd4d4279a","tagName":"view"},{"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","tagName":"view"}]}}
[I 2025-07-30 10:40:40,802 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2EAF90>]
[I 2025-07-30 10:40:40,803 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='用户名'], input[placeholder*='账号']
[D 2025-07-30 10:40:40,803 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"284be491-9803-4e73-992f-c5e550098d78","method":"Page.getElements","params":{"selector":"input[placeholder*='\u7528\u6237\u540d'], input[placeholder*='\u8d26\u53f7']","pageId":6}}
[D 2025-07-30 10:40:40,808 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"284be491-9803-4e73-992f-c5e550098d78","result":{"elements":[]}}
[W 2025-07-30 10:40:40,809 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='用户名'], input[placeholder*='账号']' you need
[I 2025-07-30 10:40:40,809 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='密码']
[D 2025-07-30 10:40:40,810 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"277bcb55-eb51-44a3-880e-2cc83b89c261","method":"Page.getElements","params":{"selector":"input[placeholder*='\u5bc6\u7801']","pageId":6}}
[D 2025-07-30 10:40:40,812 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"277bcb55-eb51-44a3-880e-2cc83b89c261","result":{"elements":[]}}
[W 2025-07-30 10:40:40,812 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='密码']' you need
[I 2025-07-30 10:40:40,813 minium page#716 _get_elements_by_css] try to get elements: .tab-item[data-type='password'], .password-tab
[D 2025-07-30 10:40:40,813 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"6b22c6a5-c171-434f-a993-2aac0952138a","method":"Page.getElements","params":{"selector":".tab-item[data-type='password'], .password-tab","pageId":6}}
[D 2025-07-30 10:40:40,818 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"6b22c6a5-c171-434f-a993-2aac0952138a","result":{"elements":[{"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","tagName":"view"}]}}
[I 2025-07-30 10:40:40,820 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A341310>]
[D 2025-07-30 10:40:40,862 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"4be067b2-5bf9-493b-8150-bd0c685c1de2","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","pageId":6}}
[D 2025-07-30 10:40:40,865 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"4be067b2-5bf9-493b-8150-bd0c685c1de2","result":{"styles":["auto"]}}
[D 2025-07-30 10:40:40,867 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"80f44acf-ada4-43df-99a6-7d3a0ccca994","method":"Element.tap","params":{"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","pageId":6}}
[D 2025-07-30 10:40:40,927 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"80f44acf-ada4-43df-99a6-7d3a0ccca994","result":{"pageX":262.5,"pageY":170,"clientX":262.5,"clientY":170}}
[I 2025-07-30 10:40:43,929 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='用户名']
[D 2025-07-30 10:40:43,930 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"092a73b4-efc6-4130-a584-0505cd45b432","method":"Page.getElements","params":{"selector":"input[placeholder*='\u7528\u6237\u540d']","pageId":6}}
[D 2025-07-30 10:40:43,936 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"092a73b4-efc6-4130-a584-0505cd45b432","result":{"elements":[]}}
[W 2025-07-30 10:40:43,936 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='用户名']' you need
[I 2025-07-30 10:40:43,937 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='密码']
[D 2025-07-30 10:40:43,938 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"fb3fcc10-96ec-4d7b-9e4f-2efa03b1e317","method":"Page.getElements","params":{"selector":"input[placeholder*='\u5bc6\u7801']","pageId":6}}
[D 2025-07-30 10:40:43,942 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"fb3fcc10-96ec-4d7b-9e4f-2efa03b1e317","result":{"elements":[{"elementId":"9c138cbd-6ba2-4c5a-a23f-2f24f00f7067","tagName":"input"}]}}
[I 2025-07-30 10:40:43,943 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.FormElement object at 0x0000020F5A2EACF0>]
[D 2025-07-30 10:40:43,944 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"e70b6cc1-04fd-4a82-b890-9dd61b20f48a","method":"Element.callFunction","params":{"functionName":"input.input","args":["test123",false],"elementId":"9c138cbd-6ba2-4c5a-a23f-2f24f00f7067","pageId":6}}
[D 2025-07-30 10:40:43,954 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"e70b6cc1-04fd-4a82-b890-9dd61b20f48a","result":{}}
[I 2025-07-30 10:40:44,456 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:44,456 minium minitest#799 _miniTearDown] =========Current case Down: test_02_login_and_authentication=========
[I 2025-07-30 10:40:44,457 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:44,457 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"c2fd96b5-278a-4812-963f-d643ea0db543","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:44,519 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"c2fd96b5-278a-4812-963f-d643ea0db543","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzt3Xl8FPXdwPHvXjm4jyQkipSiBRQ5CihYUUGuIsEW1IqhWgJBAYVSwAINaFVSoRzlAQWUQCgWREFiIRG5RC1q0HCmKKJQiiCbgwKSkGOPef6Y3c1usrmQ3+b6vF++nu7OTmY2D9lPfvPb2YmhsMgmAKCMsbqfAIA6jsoAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAU
[I 2025-07-30 10:40:44,522 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:44,522 minium basenative#63 wrapper] call BaseNative.get_start_up end 
