#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
楠楠家厨后台API功能测试
测试用户注册、登录、关联等后台功能
"""

import requests
import json
import time
from datetime import datetime


class BackendAPITest:
    """后台API测试类"""
    
    def __init__(self):
        self.api_base = "http://8.148.231.104:3000/api"
        self.test_results = []
        self.test_users = []
        
    def log_result(self, test_name, success, details="", critical=False):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        priority = "🔥" if critical else "📋"
        print(f"{status} {priority} {test_name}: {details}")
    
    def test_api_connectivity(self):
        """测试API连通性"""
        print("🧪 测试API连通性")

        try:
            # 测试一个实际的API端点而不是根路径
            response = requests.get(f"{self.api_base}/orders/today", timeout=10)
            if response.status_code in [200, 401]:  # 200成功或401未授权都表示API可用
                self.log_result("API连通性", True, f"API服务正常: {response.status_code}", critical=True)
                return True
            else:
                self.log_result("API连通性", False, f"API响应异常: {response.status_code}", critical=True)
                return False
        except Exception as e:
            self.log_result("API连通性", False, f"连接失败: {e}", critical=True)
            return False
    
    def test_user_registration(self):
        """测试用户注册"""
        print("🧪 测试用户注册")
        
        # 测试用户数据
        test_users_data = [
            {
                "name": "测试用户A",
                "phone": "13800000001", 
                "password": "test123456"
            },
            {
                "name": "测试用户B",
                "phone": "13800000002",
                "password": "test123456"
            }
        ]
        
        for user_data in test_users_data:
            try:
                response = requests.post(
                    f"{self.api_base}/auth/register",
                    json=user_data,
                    timeout=10
                )
                
                if response.status_code == 201:
                    result = response.json()
                    user_info = {
                        "id": result["data"]["user"]["id"],
                        "name": user_data["name"],
                        "phone": user_data["phone"],
                        "password": user_data["password"],
                        "token": result["data"]["token"]
                    }
                    self.test_users.append(user_info)
                    self.log_result(f"用户注册-{user_data['name']}", True, "注册成功", critical=True)
                    
                elif response.status_code == 409:
                    # 用户已存在，尝试登录
                    self.log_result(f"用户注册-{user_data['name']}", True, "用户已存在", critical=False)
                    self._login_existing_user(user_data)
                    
                else:
                    self.log_result(f"用户注册-{user_data['name']}", False, f"注册失败: {response.status_code}", critical=True)
                    
            except Exception as e:
                self.log_result(f"用户注册-{user_data['name']}", False, f"注册异常: {e}", critical=True)
    
    def _login_existing_user(self, user_data):
        """登录已存在的用户"""
        try:
            response = requests.post(
                f"{self.api_base}/auth/login",
                json={
                    "username": user_data["phone"],
                    "password": user_data["password"]
                },
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                user_info = {
                    "id": result["data"]["user"]["id"],
                    "name": user_data["name"],
                    "phone": user_data["phone"],
                    "password": user_data["password"],
                    "token": result["data"]["token"]
                }
                self.test_users.append(user_info)
                self.log_result(f"用户登录-{user_data['name']}", True, "登录成功", critical=True)
            else:
                self.log_result(f"用户登录-{user_data['name']}", False, f"登录失败: {response.status_code}", critical=True)
                
        except Exception as e:
            self.log_result(f"用户登录-{user_data['name']}", False, f"登录异常: {e}", critical=True)
    
    def test_user_login(self):
        """测试用户登录"""
        print("🧪 测试用户登录")
        
        if not self.test_users:
            self.log_result("用户登录前置条件", False, "没有可用的测试用户", critical=True)
            return
        
        for user in self.test_users:
            try:
                response = requests.post(
                    f"{self.api_base}/auth/login",
                    json={
                        "username": user["phone"],
                        "password": user["password"]
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    # 更新token
                    user["token"] = result["data"]["token"]
                    self.log_result(f"用户登录验证-{user['name']}", True, "登录验证成功", critical=True)
                else:
                    self.log_result(f"用户登录验证-{user['name']}", False, f"登录验证失败: {response.status_code}", critical=True)
                    
            except Exception as e:
                self.log_result(f"用户登录验证-{user['name']}", False, f"登录验证异常: {e}", critical=True)
    
    def test_user_connection(self):
        """测试用户关联"""
        print("🧪 测试用户关联")
        
        if len(self.test_users) < 2:
            self.log_result("用户关联前置条件", False, "测试用户不足2个", critical=True)
            return
        
        user_a = self.test_users[0]
        user_b = self.test_users[1]
        
        # 测试获取可关联用户列表
        self._test_get_available_users(user_a)
        
        # 测试发送关联申请
        connection_id = self._test_send_connection_request(user_a, user_b)
        
        if connection_id:
            # 测试处理关联申请
            self._test_respond_connection(user_b, connection_id)
            
            # 测试获取关联列表
            self._test_get_connections(user_a)
    
    def _test_get_available_users(self, user):
        """测试获取可关联用户列表"""
        try:
            response = requests.get(
                f"{self.api_base}/connections/users",
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                users_count = len(result.get("data", []))
                self.log_result("获取可关联用户", True, f"找到{users_count}个可关联用户", critical=True)
            else:
                self.log_result("获取可关联用户", False, f"获取失败: {response.status_code}", critical=True)
                
        except Exception as e:
            self.log_result("获取可关联用户", False, f"获取异常: {e}", critical=True)
    
    def _test_send_connection_request(self, sender, receiver):
        """测试发送关联申请"""
        try:
            response = requests.post(
                f"{self.api_base}/connections/request",
                json={
                    "receiverId": receiver["id"],
                    "message": "测试关联申请"
                },
                headers={"Authorization": f"Bearer {sender['token']}"},
                timeout=10
            )
            
            if response.status_code == 201:
                result = response.json()
                connection_id = result["data"]["id"]
                self.log_result("发送关联申请", True, f"申请发送成功，ID: {connection_id}", critical=True)
                return connection_id
            else:
                self.log_result("发送关联申请", False, f"申请发送失败: {response.status_code}", critical=True)
                return None
                
        except Exception as e:
            self.log_result("发送关联申请", False, f"申请发送异常: {e}", critical=True)
            return None
    
    def _test_respond_connection(self, user, connection_id):
        """测试处理关联申请"""
        try:
            response = requests.put(
                f"{self.api_base}/connections/{connection_id}/respond",
                json={"action": "accept"},
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )
            
            if response.status_code == 200:
                self.log_result("处理关联申请", True, "关联申请处理成功", critical=True)
            else:
                self.log_result("处理关联申请", False, f"处理失败: {response.status_code}", critical=True)
                
        except Exception as e:
            self.log_result("处理关联申请", False, f"处理异常: {e}", critical=True)
    
    def _test_get_connections(self, user):
        """测试获取关联列表"""
        try:
            response = requests.get(
                f"{self.api_base}/connections/my",
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                connections_count = len(result.get("data", []))
                self.log_result("获取关联列表", True, f"找到{connections_count}个关联关系", critical=True)
            else:
                self.log_result("获取关联列表", False, f"获取失败: {response.status_code}", critical=True)
                
        except Exception as e:
            self.log_result("获取关联列表", False, f"获取异常: {e}", critical=True)

    def test_dish_management(self):
        """测试菜品管理"""
        print("🧪 测试菜品管理")

        if not self.test_users:
            self.log_result("菜品管理前置条件", False, "没有可用的测试用户", critical=True)
            return

        user = self.test_users[0]

        # 测试获取菜品列表
        self._test_get_dishes(user)

        # 测试创建菜品
        dish_id = self._test_create_dish(user)

        if dish_id:
            # 测试获取我的菜品
            self._test_get_my_dishes(user)

    def _test_get_dishes(self, user):
        """测试获取菜品列表"""
        try:
            response = requests.get(
                f"{self.api_base}/dishes",
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                dishes_count = len(result.get("data", []))
                self.log_result("获取菜品列表", True, f"找到{dishes_count}个菜品", critical=True)
            else:
                self.log_result("获取菜品列表", False, f"获取失败: {response.status_code}", critical=True)

        except Exception as e:
            self.log_result("获取菜品列表", False, f"获取异常: {e}", critical=True)

    def _test_create_dish(self, user):
        """测试创建菜品"""
        try:
            dish_data = {
                "name": "测试菜品",
                "description": "这是一个测试菜品",
                "ingredients": "测试配料、调料、蔬菜",
                "cookingMethod": "炒制",
                "category": "测试分类",
                "remark": "测试备注",
                "tags": ["测试", "美味"],
                "isPublished": True
            }

            response = requests.post(
                f"{self.api_base}/dishes",
                json=dish_data,
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )

            if response.status_code == 201:
                result = response.json()
                dish_id = result["data"]["id"]
                self.log_result("创建菜品", True, f"菜品创建成功，ID: {dish_id}", critical=True)
                return dish_id
            else:
                self.log_result("创建菜品", False, f"创建失败: {response.status_code}", critical=True)
                return None

        except Exception as e:
            self.log_result("创建菜品", False, f"创建异常: {e}", critical=True)
            return None

    def _test_get_my_dishes(self, user):
        """测试获取我的菜品"""
        try:
            response = requests.get(
                f"{self.api_base}/dishes/my",
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                my_dishes_count = len(result.get("data", []))
                self.log_result("获取我的菜品", True, f"找到{my_dishes_count}个我的菜品", critical=True)
            else:
                self.log_result("获取我的菜品", False, f"获取失败: {response.status_code}", critical=True)

        except Exception as e:
            self.log_result("获取我的菜品", False, f"获取异常: {e}", critical=True)

    def test_order_management(self):
        """测试订单管理"""
        print("🧪 测试订单管理")

        if not self.test_users:
            self.log_result("订单管理前置条件", False, "没有可用的测试用户", critical=True)
            return

        user = self.test_users[0]

        # 测试获取今日订单
        self._test_get_today_orders()

        # 测试创建订单
        order_id = self._test_create_order(user)

        if order_id:
            # 测试获取订单列表
            self._test_get_orders(user)

    def _test_get_today_orders(self):
        """测试获取今日订单"""
        try:
            response = requests.get(f"{self.api_base}/orders/today", timeout=10)

            if response.status_code == 200:
                result = response.json()
                orders_count = len(result.get("data", []))
                self.log_result("获取今日订单", True, f"找到{orders_count}个今日订单", critical=True)
            else:
                self.log_result("获取今日订单", False, f"获取失败: {response.status_code}", critical=True)

        except Exception as e:
            self.log_result("获取今日订单", False, f"获取异常: {e}", critical=True)

    def _test_create_order(self, user):
        """测试创建订单"""
        try:
            order_data = {
                "items": [
                    {
                        "dishId": 1,
                        "quantity": 2,
                        "notes": "测试订单"
                    }
                ]
            }

            response = requests.post(
                f"{self.api_base}/orders",
                json=order_data,
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )

            if response.status_code == 201:
                result = response.json()
                order_id = result["data"]["id"]
                self.log_result("创建订单", True, f"订单创建成功，ID: {order_id}", critical=True)
                return order_id
            else:
                self.log_result("创建订单", False, f"创建失败: {response.status_code}", critical=True)
                return None

        except Exception as e:
            self.log_result("创建订单", False, f"创建异常: {e}", critical=True)
            return None

    def _test_get_orders(self, user):
        """测试获取订单列表"""
        try:
            response = requests.get(
                f"{self.api_base}/orders",
                headers={"Authorization": f"Bearer {user['token']}"},
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                orders_count = len(result.get("data", []))
                self.log_result("获取订单列表", True, f"找到{orders_count}个订单", critical=True)
            else:
                self.log_result("获取订单列表", False, f"获取失败: {response.status_code}", critical=True)

        except Exception as e:
            self.log_result("获取订单列表", False, f"获取异常: {e}", critical=True)

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行楠楠家厨后台API功能测试")
        print("=" * 70)
        print("🌐 API服务器: http://8.148.231.104:3000/api")
        print("🧪 测试内容: 用户注册、登录、关联、菜品管理、订单管理")
        print("=" * 70)

        start_time = datetime.now()

        # 运行测试
        if self.test_api_connectivity():
            self.test_user_registration()
            self.test_user_login()
            self.test_user_connection()
            self.test_dish_management()
            self.test_order_management()

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        critical_failed = len([r for r in self.test_results if not r['success'] and r.get('critical', False)])

        # 生成报告
        api_report = {
            "test_time": start_time.isoformat(),
            "duration_seconds": duration,
            "test_users_created": len(self.test_users),
            "api_tests": {
                "total": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "critical_failed": critical_failed
            },
            "overall_success": critical_failed == 0,
            "test_users": [{"name": u["name"], "phone": u["phone"]} for u in self.test_users],
            "detailed_results": self.test_results,
            "test_coverage": {
                "api_connectivity": "API连通性测试",
                "user_management": "用户注册登录测试",
                "user_connection": "用户关联功能测试",
                "dish_management": "菜品管理功能测试",
                "order_management": "订单管理功能测试"
            }
        }

        # 保存报告
        with open("api_backend_test_report.json", 'w', encoding='utf-8') as f:
            json.dump(api_report, f, ensure_ascii=False, indent=2)

        # 打印总结
        print("\n" + "=" * 70)
        print("🎯 后台API测试总结")
        print("=" * 70)
        print(f"⏱️ 测试耗时: {duration:.2f} 秒")
        print(f"👥 创建测试用户: {len(self.test_users)} 个")
        print(f"🔧 API测试: {passed_tests}/{total_tests} 通过")
        print(f"🔥 关键功能失败: {critical_failed} 个")

        if total_tests > 0:
            success_rate = passed_tests / total_tests * 100
            print(f"🎯 API成功率: {success_rate:.1f}%")

            if success_rate >= 90:
                print("🎉 API测试结果优秀！")
            elif success_rate >= 70:
                print("👍 API测试结果良好！")
            else:
                print("⚠️ API需要优化")

        # 分类统计
        categories = {}
        for result in self.test_results:
            category = result['test_name'].split('-')[0] if '-' in result['test_name'] else result['test_name']
            if category not in categories:
                categories[category] = {'passed': 0, 'total': 0}
            categories[category]['total'] += 1
            if result['success']:
                categories[category]['passed'] += 1

        print(f"\n📋 API功能分类结果:")
        for category, stats in categories.items():
            if stats['total'] > 0:
                rate = stats['passed'] / stats['total'] * 100
                status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
                print(f"  {status} {category}: {stats['passed']}/{stats['total']} ({rate:.0f}%)")

        print(f"\n📊 详细报告: api_backend_test_report.json")
        print("=" * 70)

        return api_report['overall_success']


if __name__ == "__main__":
    tester = BackendAPITest()
    success = tester.run_all_tests()

    if success:
        print("🎉 后台API测试完全成功！")
    else:
        print("⚠️ 部分API功能需要优化")
