#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复后的API测试
专门测试修复的菜品创建和订单创建功能
"""

import requests
import json
from datetime import datetime

def test_fixed_apis():
    """测试修复后的API"""
    api_base = "http://8.148.231.104:3000/api"
    
    print("🚀 开始测试修复后的API")
    print("=" * 60)
    
    # 先登录获取token
    print("🧪 用户登录...")
    try:
        login_response = requests.post(
            f"{api_base}/auth/login",
            json={
                "username": "13800000001",
                "password": "test123456"
            },
            timeout=10
        )
        
        if login_response.status_code == 200:
            result = login_response.json()
            token = result["data"]["token"]
            user_id = result["data"]["user"]["id"]
            print(f"✅ 登录成功: ID={user_id}")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return
    
    # 测试修复后的菜品创建
    print("\n🧪 测试修复后的菜品创建...")
    try:
        dish_data = {
            "name": "修复测试菜品",
            "description": "这是修复后的测试菜品",
            "ingredients": "测试配料、调料、蔬菜",
            "cookingMethod": "炒制",
            "category": "测试分类",
            "remark": "修复测试备注",
            "tags": ["测试", "美味"],
            "isPublished": True
        }
        
        response = requests.post(
            f"{api_base}/dishes",
            json=dish_data,
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if response.status_code == 201:
            result = response.json()
            dish_id = result["data"]["id"]
            print(f"✅ 菜品创建成功: ID={dish_id}, 名称={result['data']['name']}")
        else:
            print(f"❌ 菜品创建失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ 菜品创建异常: {e}")
    
    # 获取可用菜品ID用于订单测试
    print("\n🧪 获取可用菜品...")
    try:
        dishes_response = requests.get(
            f"{api_base}/dishes",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        dish_id = 1  # 默认值
        if dishes_response.status_code == 200:
            dishes_data = dishes_response.json()
            if dishes_data.get("data") and len(dishes_data["data"]) > 0:
                dish_id = dishes_data["data"][0]["id"]
                print(f"✅ 获取到菜品ID: {dish_id}")
            else:
                print("⚠️ 没有可用菜品，使用默认ID")
        else:
            print(f"⚠️ 获取菜品失败: {dishes_response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取菜品异常: {e}")
    
    # 测试修复后的订单创建
    print("\n🧪 测试修复后的订单创建...")
    try:
        order_data = {
            "items": [
                {
                    "dishId": dish_id,
                    "count": 2
                }
            ],
            "remark": "修复测试订单",
            "diningTime": datetime.now().isoformat()
        }
        
        response = requests.post(
            f"{api_base}/orders",
            json=order_data,
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if response.status_code == 201:
            result = response.json()
            order_id = result["data"]["order"]["id"]
            print(f"✅ 订单创建成功: ID={order_id}")
            print(f"   订单详情: {result['data']['order']['items']}")
        else:
            print(f"❌ 订单创建失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ 订单创建异常: {e}")
    
    # 验证创建的数据
    print("\n🧪 验证创建的数据...")
    
    # 验证我的菜品
    try:
        my_dishes_response = requests.get(
            f"{api_base}/dishes/my",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if my_dishes_response.status_code == 200:
            result = my_dishes_response.json()
            my_dishes_count = len(result.get("data", []))
            print(f"✅ 我的菜品验证: {my_dishes_count}个菜品")
        else:
            print(f"❌ 我的菜品验证失败: {my_dishes_response.status_code}")
            
    except Exception as e:
        print(f"❌ 我的菜品验证异常: {e}")
    
    # 验证我的订单
    try:
        my_orders_response = requests.get(
            f"{api_base}/orders",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if my_orders_response.status_code == 200:
            result = my_orders_response.json()
            my_orders_count = len(result.get("data", {}).get("list", []))
            print(f"✅ 我的订单验证: {my_orders_count}个订单")
        else:
            print(f"❌ 我的订单验证失败: {my_orders_response.status_code}")
            
    except Exception as e:
        print(f"❌ 我的订单验证异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 修复测试完成")

if __name__ == "__main__":
    test_fixed_apis()
