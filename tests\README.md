# 楠楠家厨小程序自动化测试

## 📋 测试概述

本测试套件使用minium框架对楠楠家厨小程序进行全面的系统测试，覆盖用户认证、菜单浏览、订餐流程、用户管理等核心功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r tests/requirements.txt
```

### 2. 配置测试环境

确保微信开发者工具已安装并配置好小程序项目：
- 小程序AppID: wx82283b353918af82
- 测试服务器: http://*************:3000/api

### 3. 运行测试

```bash
# 运行所有测试
python tests/run_tests.py

# 运行单个测试模块
python -m unittest tests.test_01_login
python -m unittest tests.test_02_home
python -m unittest tests.test_03_order
python -m unittest tests.test_04_order_flow
python -m unittest tests.test_05_user_management
```

## 📁 测试结构

```
tests/
├── config/
│   └── minium.json          # minium配置文件
├── utils/
│   └── base_test.py         # 基础测试类
├── test_01_login.py         # 登录功能测试
├── test_02_home.py          # 首页功能测试
├── test_03_order.py         # 订餐页面测试
├── test_04_order_flow.py    # 完整订餐流程测试
├── test_05_user_management.py # 用户管理测试
├── run_tests.py             # 测试运行器
├── requirements.txt         # 依赖列表
└── reports/                 # 测试报告目录
    ├── test_report.json     # JSON格式报告
    ├── test_report.html     # HTML格式报告
    └── screenshots/         # 测试截图
```

## 🧪 测试模块说明

### 1. 用户登录功能测试 (test_01_login.py)
- 登录页面元素验证
- 账号密码登录流程
- 登录表单验证
- 记住密码功能
- 注册页面跳转

### 2. 首页功能测试 (test_02_home.py)
- 首页基本元素验证
- 菜单展示功能
- 导航按钮测试
- 通知轮播功能
- 页面刷新功能

### 3. 订餐页面测试 (test_03_order.py)
- 订餐页面元素验证
- 分类切换功能
- 菜品选择功能
- 菜品详情查看
- 购物车功能
- 搜索功能

### 4. 完整订餐流程测试 (test_04_order_flow.py)
- 端到端订餐流程
- 订单修改功能
- 订单验证功能
- 购物车管理

### 5. 用户管理功能测试 (test_05_user_management.py)
- 个人中心页面
- 我的菜品管理
- 新增菜品功能
- 用户关联功能
- 通知中心
- 退出登录

## 📊 测试报告

测试完成后会生成以下报告：

1. **JSON报告** (`reports/test_report.json`)
   - 机器可读的详细测试结果
   - 包含所有测试数据和统计信息

2. **HTML报告** (`reports/test_report.html`)
   - 可视化的测试报告
   - 包含测试总结、详细结果和图表

3. **截图** (`reports/screenshots/`)
   - 关键测试步骤的截图
   - 便于问题定位和结果验证

## ⚙️ 配置说明

### minium.json 配置项

```json
{
  "project_path": "../../",              // 小程序项目路径
  "app_id": "wx82283b353918af82",       // 小程序AppID
  "test_port": 9420,                    // 测试端口
  "test_env": {                         // 测试环境变量
    "API_BASE_URL": "http://*************:3000/api"
  }
}
```

### 测试用户配置

默认测试用户：
- 用户名: test_user
- 密码: test123456
- 手机号: 13800138000

## 🔧 自定义配置

### 修改测试环境

编辑 `config/minium.json` 文件中的 `test_env` 部分：

```json
"test_env": {
  "API_BASE_URL": "你的API地址",
  "TEST_USER": {
    "username": "你的测试用户名",
    "password": "你的测试密码"
  }
}
```

### 添加新的测试用例

1. 继承 `BaseTest` 类
2. 实现具体的测试方法
3. 在 `run_tests.py` 中添加新的测试套件

## 🐛 常见问题

### 1. minium安装失败
```bash
pip install --upgrade pip
pip install minium
```

### 2. 微信开发者工具连接失败
- 确保开发者工具已打开
- 检查端口配置是否正确
- 确认小程序项目已加载

### 3. 测试用例失败
- 检查网络连接
- 确认测试服务器状态
- 查看测试截图定位问题

## 📝 注意事项

1. **测试环境**: 确保在测试环境运行，避免影响生产数据
2. **数据清理**: 测试完成后会自动清理测试数据
3. **网络依赖**: 部分测试需要网络连接，确保网络稳定
4. **设备兼容**: 建议在微信开发者工具中运行测试

## 🔄 持续集成

可以将测试集成到CI/CD流水线中：

```yaml
# GitHub Actions 示例
- name: Run WeChat Mini Program Tests
  run: |
    pip install -r tests/requirements.txt
    python tests/run_tests.py
```

## 📞 支持

如有问题，请查看：
1. 测试报告中的错误信息
2. 测试截图
3. 控制台输出日志
