[I 2025-07-30 10:40:44,526 minium minitest#432 _miniSetUp] =========Current case: test_03_user_association_functionality=========
[I 2025-07-30 10:40:44,526 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_03_user_association_functionality
[I 2025-07-30 10:40:44,527 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:44,527 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:44,527 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:44,527 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:44,528 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:44,529 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:44,529 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"f4a1b072-c945-41d9-af81-c98265d674be","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:44,601 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"f4a1b072-c945-41d9-af81-c98265d674be","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzt3Xl8FPXdwPHvXjm4jyQkipSiBRQ5CihYUUGuIsEW1IqhWgJBAYVSwAINaFVSoRzlAQWUQCgWREFiIRG5RC1q0HCmKKJQiiCbgwKSkGOPef6Y3c1usrmQ3+b6vF++nu7OTmY2D9lPfvPb2YmhsMgmAKCMsbqfAIA6jsoAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAU
[I 2025-07-30 10:40:44,603 minium minitest#487 _miniSetUp] =========case: test_03_user_association_functionality start=========
[I 2025-07-30 10:40:45,604 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:45,605 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [2264960766976]{"id":"0cacc3c1-2a28-4b3a-a67c-599005fcbc68","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:45,610 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:45,853 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843245846,"webviewId":3,"routeEventId":"3_1753843245630","renderer":"webview"},1753843245847]}}
[I 2025-07-30 10:40:45,854 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843245846, 'webviewId': 3, 'routeEventId': '3_1753843245630', 'renderer': 'webview'}, 1753843245847]}
[D 2025-07-30 10:40:45,856 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"0cacc3c1-2a28-4b3a-a67c-599005fcbc68","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:45,856 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 0cacc3c1-2a28-4b3a-a67c-599005fcbc68
[D 2025-07-30 10:40:47,857 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"a754eb15-23f5-4dc6-95b8-279121478fd9","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:47,859 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"a754eb15-23f5-4dc6-95b8-279121478fd9","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:40:47,860 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:47,860 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"476d12fd-379e-4950-a783-e2d069abf293","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:47,862 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"476d12fd-379e-4950-a783-e2d069abf293","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:47,862 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:40:47,863 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"bd921a2f-194e-49fa-9639-963a896988c0","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:40:47,869 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"bd921a2f-194e-49fa-9639-963a896988c0","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:40:47,870 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2D6D70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2D6C40>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2AEF90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2BF680>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2BF020>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A336250>]
[D 2025-07-30 10:40:47,871 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"d6e816fa-36ef-4e60-b532-e109221fd143","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:40:47,873 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"d6e816fa-36ef-4e60-b532-e109221fd143","result":{"properties":["留言"]}}
[D 2025-07-30 10:40:47,874 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"31e4ebf4-6885-41e2-9cc8-3a82fbcddd9a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:47,876 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"31e4ebf4-6885-41e2-9cc8-3a82fbcddd9a","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:40:47,877 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"f0af4cc6-cb40-4900-a892-066208d6d8a2","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:47,880 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"f0af4cc6-cb40-4900-a892-066208d6d8a2","result":{"styles":["auto"]}}
[D 2025-07-30 10:40:47,880 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"31e59c20-c20a-49a9-ac88-742a116132fe","method":"Element.tap","params":{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:47,954 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/user_connection/index"}]}}
[D 2025-07-30 10:40:47,996 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"31e59c20-c20a-49a9-ac88-742a116132fe","result":{"pageX":187.5,"pageY":244,"clientX":187.5,"clientY":244}}
[D 2025-07-30 10:40:49,881 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/user_connection/index","query":{},"openType":"navigateTo","timeStamp":1753843249872,"webviewId":7,"routeEventId":"7_1753843249090","renderer":"webview"},1753843249874]}}
[I 2025-07-30 10:40:49,884 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/user_connection/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843249872, 'webviewId': 7, 'routeEventId': '7_1753843249090', 'renderer': 'webview'}, 1753843249874]}
[D 2025-07-30 10:40:51,997 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"cacca522-71de-42d3-952a-e0ccb452c617","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:52,003 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"cacca522-71de-42d3-952a-e0ccb452c617","result":{"pageId":7,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 10:40:52,004 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:52,006 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"85f5390d-6cef-40fb-9b2a-0f26129fa961","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:52,011 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"85f5390d-6cef-40fb-9b2a-0f26129fa961","result":{"result":{"pageId":7,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:52,013 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='搜索'], .search-input
[D 2025-07-30 10:40:52,014 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"56135063-633d-454d-bd43-be6ed45f36a6","method":"Page.getElements","params":{"selector":"input[placeholder*='\u641c\u7d22'], .search-input","pageId":7}}
[D 2025-07-30 10:40:52,028 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"56135063-633d-454d-bd43-be6ed45f36a6","result":{"elements":[]}}
[W 2025-07-30 10:40:52,029 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='搜索'], .search-input' you need
[I 2025-07-30 10:40:52,029 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:52,030 minium minitest#799 _miniTearDown] =========Current case Down: test_03_user_association_functionality=========
[I 2025-07-30 10:40:52,031 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:52,031 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"99f44436-36d8-401e-80bb-eed5d0b944f1","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:52,094 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"99f44436-36d8-401e-80bb-eed5d0b944f1","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsXXd8VUXafueU21IhIYVQkkAQZSlJEBBUBAsKqwFc2MVPRUoS2FV0d91dPlxCAOVjlbWgK00Eu4IiUUHQFbCAgCShWOhBWgKEknbvPXW+P069LbkJuUlI5pHfOPecOVOeM+eZ933n3Bs0JacCAWAAhBDGWM0DwhgjhACwnsfmvCkFnyMNKQMIwMhjr3zd/QGEQGvLyOMAea2Mnir1mPIA4MUJ4YfwQ/hpGD9TcioAQCEFVAr8Q+moVkbNK/zpeaTWqtINWKlPa00bqlZcGbxvy8ZxzzJe/fHXZ49qtK5pA1baVS/W8sg8lIBjJ/wQfgg/DeSHUoaHAQAQNuXVYWMjxRgDIGzK68NGWh5jDKqqqXlt2JpSqsdBV
[I 2025-07-30 10:40:52,095 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:52,095 minium basenative#63 wrapper] call BaseNative.get_start_up end 
