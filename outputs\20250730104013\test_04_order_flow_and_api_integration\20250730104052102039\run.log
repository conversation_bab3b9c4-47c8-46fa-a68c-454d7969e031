[I 2025-07-30 10:40:52,103 minium minitest#432 _miniSetUp] =========Current case: test_04_order_flow_and_api_integration=========
[I 2025-07-30 10:40:52,104 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_04_order_flow_and_api_integration
[I 2025-07-30 10:40:52,104 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:52,104 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:52,105 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:52,105 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:52,105 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:52,106 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:52,106 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"cbdc5ac5-a67b-4a5c-ad32-e251c35872a9","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:52,164 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"cbdc5ac5-a67b-4a5c-ad32-e251c35872a9","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsXXd8VUXafueU21IhIYVQkkAQZSlJEBBUBAsKqwFc2MVPRUoS2FV0d91dPlxCAOVjlbWgK00Eu4IiUUHQFbCAgCShWOhBWgKEknbvPXW+P069LbkJuUlI5pHfOPecOVOeM+eZ933n3Bs0JacCAWAAhBDGWM0DwhgjhACwnsfmvCkFnyMNKQMIwMhjr3zd/QGEQGvLyOMAea2Mnir1mPIA4MUJ4YfwQ/hpGD9TcioAQCEFVAr8Q+moVkbNK/zpeaTWqtINWKlPa00bqlZcGbxvy8ZxzzJe/fHXZ49qtK5pA1baVS/W8sg8lIBjJ/wQfgg/DeSHUoaHAQAQNuXVYWMjxRgDIGzK68NGWh5jDKqqqXlt2JpSqsdBV
[I 2025-07-30 10:40:52,176 minium minitest#487 _miniSetUp] =========case: test_04_order_flow_and_api_integration start=========
[I 2025-07-30 10:40:53,177 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:53,178 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [2264960766976]{"id":"7343b583-7582-46e8-b795-38d179acfc82","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:40:53,181 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:40:53,414 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843253404,"webviewId":5,"routeEventId":"5_1753843253201","renderer":"webview"},1753843253405]}}
[D 2025-07-30 10:40:53,415 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"7343b583-7582-46e8-b795-38d179acfc82","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:53,415 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843253404, 'webviewId': 5, 'routeEventId': '5_1753843253201', 'renderer': 'webview'}, 1753843253405]}
[I 2025-07-30 10:40:53,415 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 7343b583-7582-46e8-b795-38d179acfc82
[D 2025-07-30 10:40:56,418 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"9fa57463-8b08-4207-a73e-a5350d68e447","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:56,422 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"9fa57463-8b08-4207-a73e-a5350d68e447","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:40:56,424 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:56,425 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"66f2dab1-9a98-4e64-a2ff-5f8bd9739b31","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:56,430 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"66f2dab1-9a98-4e64-a2ff-5f8bd9739b31","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:56,432 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item
[D 2025-07-30 10:40:56,435 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"25bb62d3-15b5-4588-929e-611a8c197411","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item","pageId":5}}
[D 2025-07-30 10:40:56,442 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"25bb62d3-15b5-4588-929e-611a8c197411","result":{"elements":[{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","tagName":"view"},{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","tagName":"view"}]}}
[I 2025-07-30 10:40:56,443 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A336250>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2F7110>]
[I 2025-07-30 10:40:56,444 minium element#521 _get_elements_by_css] try to get elements: .add-btn, .plus-btn, button[bindtap*='add']
[D 2025-07-30 10:40:56,444 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"13e78cca-8ba6-4c9c-b386-ad531722cbd2","method":"Element.getElements","params":{"selector":".add-btn, .plus-btn, button[bindtap*='add']","elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:40:56,448 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"13e78cca-8ba6-4c9c-b386-ad531722cbd2","result":{"elements":[]}}
[D 2025-07-30 10:40:56,448 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"75939932-13a8-40c6-ac54-5af6b682ca98","method":"Element.getProperties","params":{"names":["node_id"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:40:56,455 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"75939932-13a8-40c6-ac54-5af6b682ca98","result":{"properties":[null]}}
[D 2025-07-30 10:40:56,456 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"7f7586e5-e6df-4f9c-aba8-6722b24737a8","method":"Element.getAttributes","params":{"names":["node_id"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:40:56,461 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"7f7586e5-e6df-4f9c-aba8-6722b24737a8","result":{"attributes":[null]}}
[W 2025-07-30 10:40:56,461 minium element#526 _get_elements_by_css] Could not found any element '.add-btn, .plus-btn, button[bindtap*='add']' you need
[I 2025-07-30 10:40:56,462 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:56,462 minium minitest#799 _miniTearDown] =========Current case Down: test_04_order_flow_and_api_integration=========
[I 2025-07-30 10:40:56,463 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:56,463 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"d3d3ecb5-047d-4985-b2c8-e01330b028e9","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:56,550 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"d3d3ecb5-047d-4985-b2c8-e01330b028e9","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:40:56,553 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:56,553 minium basenative#63 wrapper] call BaseNative.get_start_up end 
