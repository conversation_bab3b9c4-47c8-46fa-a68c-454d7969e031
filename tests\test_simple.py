#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import unittest
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from minium import MiniTest
    print("✅ minium导入成功")
except ImportError as e:
    print(f"❌ minium导入失败: {e}")
    sys.exit(1)


class SimpleTest(MiniTest):
    """简化的小程序测试"""
    
    def setUp(self):
        """测试前置操作"""
        super().setUp()
        self.app = self.mini.app
        self.page = None
        print(f"🚀 开始测试: {self._testMethodName}")
    
    def tearDown(self):
        """测试后置操作"""
        print(f"✅ 测试完成: {self._testMethodName}")
        super().tearDown()
    
    def test_01_app_launch(self):
        """测试小程序启动"""
        print("🧪 测试小程序启动")
        
        try:
            # 获取小程序实例
            self.assertIsNotNone(self.app, "小程序应用实例应该存在")
            print("✅ 小程序启动成功")
            
            # 获取当前页面
            current_page = self.app.get_current_page()
            self.assertIsNotNone(current_page, "当前页面应该存在")
            
            page_path = current_page.path
            print(f"📱 当前页面: {page_path}")
            
            # 截图
            screenshot_path = "tests/reports/screenshots/app_launch.png"
            os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)
            self.app.screen_shot(screenshot_path)
            print(f"📸 截图保存: {screenshot_path}")
            
        except Exception as e:
            self.fail(f"小程序启动测试失败: {e}")
    
    def test_02_navigate_to_login(self):
        """测试导航到登录页"""
        print("🧪 测试导航到登录页")
        
        try:
            # 导航到登录页
            self.app.navigate_to("/pages/login/index")
            time.sleep(2)
            
            # 获取当前页面
            current_page = self.app.get_current_page()
            page_path = current_page.path
            
            self.assertIn("login", page_path, "应该在登录页面")
            print(f"✅ 成功导航到登录页: {page_path}")
            
            # 检查页面元素
            self.page = current_page
            
            # 查找页面标题
            try:
                page_elements = self.page.get_elements("view, text")
                found_title = False
                for element in page_elements:
                    text = element.inner_text
                    if "楠楠家厨" in text:
                        found_title = True
                        print(f"✅ 找到页面标题: {text}")
                        break
                
                if not found_title:
                    print("⚠️ 未找到页面标题")
            
            except Exception as e:
                print(f"⚠️ 检查页面元素失败: {e}")
            
            # 截图
            screenshot_path = "tests/reports/screenshots/login_page.png"
            self.app.screen_shot(screenshot_path)
            print(f"📸 截图保存: {screenshot_path}")
            
        except Exception as e:
            self.fail(f"导航到登录页测试失败: {e}")
    
    def test_03_navigate_to_home(self):
        """测试导航到首页"""
        print("🧪 测试导航到首页")
        
        try:
            # 导航到首页
            self.app.switch_tab("/pages/home/<USER>")
            time.sleep(2)
            
            # 获取当前页面
            current_page = self.app.get_current_page()
            page_path = current_page.path
            
            self.assertIn("home", page_path, "应该在首页")
            print(f"✅ 成功导航到首页: {page_path}")
            
            # 检查页面元素
            self.page = current_page
            
            try:
                # 查找欢迎信息
                page_elements = self.page.get_elements("view, text")
                found_welcome = False
                for element in page_elements:
                    text = element.inner_text
                    if "欢迎" in text:
                        found_welcome = True
                        print(f"✅ 找到欢迎信息: {text}")
                        break
                
                if not found_welcome:
                    print("⚠️ 未找到欢迎信息")
            
            except Exception as e:
                print(f"⚠️ 检查页面元素失败: {e}")
            
            # 截图
            screenshot_path = "tests/reports/screenshots/home_page.png"
            self.app.screen_shot(screenshot_path)
            print(f"📸 截图保存: {screenshot_path}")
            
        except Exception as e:
            self.fail(f"导航到首页测试失败: {e}")
    
    def test_04_navigate_to_order(self):
        """测试导航到订餐页"""
        print("🧪 测试导航到订餐页")
        
        try:
            # 导航到订餐页
            self.app.switch_tab("/pages/order/index")
            time.sleep(2)
            
            # 获取当前页面
            current_page = self.app.get_current_page()
            page_path = current_page.path
            
            self.assertIn("order", page_path, "应该在订餐页面")
            print(f"✅ 成功导航到订餐页: {page_path}")
            
            # 截图
            screenshot_path = "tests/reports/screenshots/order_page.png"
            self.app.screen_shot(screenshot_path)
            print(f"📸 截图保存: {screenshot_path}")
            
        except Exception as e:
            self.fail(f"导航到订餐页测试失败: {e}")
    
    def test_05_navigate_to_mine(self):
        """测试导航到个人中心"""
        print("🧪 测试导航到个人中心")
        
        try:
            # 导航到个人中心
            self.app.switch_tab("/pages/mine/index")
            time.sleep(2)
            
            # 获取当前页面
            current_page = self.app.get_current_page()
            page_path = current_page.path
            
            self.assertIn("mine", page_path, "应该在个人中心页面")
            print(f"✅ 成功导航到个人中心: {page_path}")
            
            # 截图
            screenshot_path = "tests/reports/screenshots/mine_page.png"
            self.app.screen_shot(screenshot_path)
            print(f"📸 截图保存: {screenshot_path}")
            
        except Exception as e:
            self.fail(f"导航到个人中心测试失败: {e}")


class TestRunner:
    """简化的测试运行器"""
    
    def __init__(self):
        self.results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": []
        }
    
    def run_tests(self):
        """运行测试"""
        print("🚀 开始运行楠楠家厨小程序简化测试")
        print("=" * 60)
        
        # 创建测试套件
        suite = unittest.TestLoader().loadTestsFromTestCase(SimpleTest)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        # 统计结果
        self.results["total_tests"] = result.testsRun
        self.results["passed_tests"] = result.testsRun - len(result.failures) - len(result.errors)
        self.results["failed_tests"] = len(result.failures) + len(result.errors)
        
        # 打印结果
        print("\n" + "=" * 60)
        print("🎯 测试总结")
        print("=" * 60)
        print(f"📊 总测试数: {self.results['total_tests']}")
        print(f"✅ 通过测试: {self.results['passed_tests']}")
        print(f"❌ 失败测试: {self.results['failed_tests']}")
        
        if self.results['total_tests'] > 0:
            success_rate = self.results['passed_tests'] / self.results['total_tests'] * 100
            print(f"🎯 成功率: {success_rate:.1f}%")
        
        print("=" * 60)
        
        return result.wasSuccessful()


if __name__ == '__main__':
    runner = TestRunner()
    success = runner.run_tests()
    
    if success:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败！")
