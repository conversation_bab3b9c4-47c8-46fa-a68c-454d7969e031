{"case_name": "test_01_app_launch_and_basic_info", "run_time": "20250730 10:34:17", "test_type": "CompleteMiniProgramTest", "case_doc": "测试小程序启动和基本信息", "success": true, "failures": "", "errors": "", "start_timestamp": 1753842857.592418, "is_failure": false, "is_error": false, "module": "E:.wx-nan.complete_miniprogram_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "", "path": "images\\setup.png", "ts": 1753842857, "datetime": "2025-07-30 10:34:17", "use_region": false}, {"name": "assertIsNotNone-success", "url": "", "path": "images\\assertIsNotNone-success.png", "ts": 1753842858, "datetime": "2025-07-30 10:34:18", "use_region": false}, {"name": "assertIsNotNone-success", "url": "/pages/home/<USER>", "path": "images\\assertIsNotNone-success_103418890827.png", "ts": 1753842858, "datetime": "2025-07-30 10:34:18", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753842859, "datetime": "2025-07-30 10:34:19", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [{"name": "assertIsNotNone", "ret": true, "msg": null, "img": "assertIsNotNone-success", "wxml": ""}, {"name": "assertIsNotNone", "ret": true, "msg": null, "img": "assertIsNotNone-success", "wxml": ""}], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753842859.1979358, "appId": "", "appName": "", "source": {"code": ["    def test_01_app_launch_and_basic_info(self):\n", "        \"\"\"测试小程序启动和基本信息\"\"\"\n", "        print(\"🧪 测试小程序启动和基本信息\")\n", "        \n", "        # 获取小程序基本信息\n", "        self.assertIsNotNone(self.app, \"小程序应用实例应该存在\")\n", "        \n", "        # 获取当前页面\n", "        current_page = self.app.get_current_page()\n", "        self.assertIsNotNone(current_page, \"当前页面应该存在\")\n", "        \n", "        page_path = current_page.path\n", "        print(f\"📱 当前页面: {page_path}\")\n", "        \n", "        # 获取系统信息\n", "        try:\n", "            system_info = self.app.evaluate(\"wx.getSystemInfoSync()\")\n", "            print(f\"📱 系统信息: {system_info.get('platform', 'unknown')}\")\n", "            print(f\"📱 微信版本: {system_info.get('version', 'unknown')}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 获取系统信息失败: {e}\")\n", "        \n", "        self.take_screenshot(\"01_app_launch\")\n", "        print(\"✅ 小程序启动测试完成\")\n"], "start": 43}, "filename": "result.json"}