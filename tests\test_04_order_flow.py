#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import unittest
from minium import minitest
from utils.base_test import BaseTest


class TestOrderFlow(BaseTest):
    """完整订餐流程测试"""
    
    def setUp(self):
        super().setUp()
        # 确保用户已登录
        self.clear_app_data()
        login_success = self.login_with_account()
        if not login_success:
            self.fail("登录失败，无法进行订餐流程测试")
    
    def test_01_complete_order_flow(self):
        """测试完整的订餐流程"""
        print("🧪 测试完整的订餐流程")
        
        # 步骤1: 进入订餐页面
        print("📱 步骤1: 进入订餐页面")
        self.switch_to_tab("/pages/order/index")
        time.sleep(2)
        
        # 步骤2: 浏览菜品并添加到购物车
        print("🍽️ 步骤2: 选择菜品")
        selected_dishes = []
        
        try:
            dishes = self.page.get_elements(".dish-card, .food-item")
            
            if len(dishes) >= 2:
                # 添加第一个菜品 (2份)
                first_dish = dishes[0]
                dish_name1 = self._get_dish_name(first_dish)
                self._add_dish_to_cart(first_dish, 2)
                selected_dishes.append({"name": dish_name1, "count": 2})
                
                # 添加第二个菜品 (1份)
                second_dish = dishes[1]
                dish_name2 = self._get_dish_name(second_dish)
                self._add_dish_to_cart(second_dish, 1)
                selected_dishes.append({"name": dish_name2, "count": 1})
                
                print(f"✅ 已选择菜品: {selected_dishes}")
            else:
                self.fail("可用菜品数量不足")
        
        except Exception as e:
            self.fail(f"选择菜品失败: {e}")
        
        # 步骤3: 查看购物车
        print("🛒 步骤3: 查看购物车")
        try:
            basket_btn = self.page.get_element(".basket-btn, .cart-btn")
            if basket_btn:
                basket_btn.click()
                time.sleep(2)
                
                # 验证跳转到购物车页面
                current_page = self.app.get_current_page()
                if "today_order" in current_page.path:
                    print("✅ 成功进入购物车页面")
                    
                    # 验证购物车内容
                    self._verify_cart_content(selected_dishes)
                else:
                    self.fail("未能跳转到购物车页面")
            else:
                self.fail("购物车按钮未找到")
        
        except Exception as e:
            self.fail(f"查看购物车失败: {e}")
        
        # 步骤4: 提交订单
        print("📝 步骤4: 提交订单")
        try:
            # 查找提交订单按钮
            submit_btn = self.page.get_element(".submit-btn, .order-btn, .confirm-btn")
            if submit_btn:
                # 添加备注（如果有备注输入框）
                try:
                    remark_input = self.page.get_element(".remark-input, textarea[placeholder*='备注']")
                    if remark_input:
                        remark_input.input("测试订单备注")
                        print("✅ 已添加订单备注")
                except:
                    print("⚠️ 备注输入框未找到")
                
                # 点击提交订单
                submit_btn.click()
                time.sleep(3)
                
                # 验证订单提交结果
                self._verify_order_submission()
            else:
                self.fail("提交订单按钮未找到")
        
        except Exception as e:
            self.fail(f"提交订单失败: {e}")
        
        # 步骤5: 查看订单列表
        print("📋 步骤5: 查看订单列表")
        try:
            # 导航到订单列表页面
            self.navigate_to_page("/pages/order_list/index")
            time.sleep(2)
            
            # 验证订单是否出现在列表中
            self._verify_order_in_list()
        
        except Exception as e:
            print(f"⚠️ 查看订单列表异常: {e}")
        
        self.take_screenshot("complete_order_flow")
        print("✅ 完整订餐流程测试完成")
    
    def test_02_order_modification(self):
        """测试订单修改功能"""
        print("🧪 测试订单修改功能")
        
        # 进入订餐页面
        self.switch_to_tab("/pages/order/index")
        time.sleep(2)
        
        try:
            # 添加菜品到购物车
            dishes = self.page.get_elements(".dish-card, .food-item")
            if dishes:
                first_dish = dishes[0]
                self._add_dish_to_cart(first_dish, 1)
                
                # 进入购物车
                basket_btn = self.page.get_element(".basket-btn, .cart-btn")
                if basket_btn:
                    basket_btn.click()
                    time.sleep(2)
                    
                    # 测试数量修改
                    self._test_quantity_modification()
                    
                    # 测试菜品删除
                    self._test_dish_removal()
        
        except Exception as e:
            print(f"⚠️ 订单修改测试异常: {e}")
        
        self.take_screenshot("order_modification")
        print("✅ 订单修改功能测试完成")
    
    def test_03_order_validation(self):
        """测试订单验证功能"""
        print("🧪 测试订单验证功能")
        
        # 进入购物车页面（空购物车）
        self.navigate_to_page("/pages/today_order/index")
        time.sleep(2)
        
        try:
            # 测试空购物车提交
            submit_btn = self.page.get_element(".submit-btn, .order-btn")
            if submit_btn:
                submit_btn.click()
                time.sleep(2)
                
                # 应该显示错误提示
                print("✅ 空购物车验证测试完成")
        
        except Exception as e:
            print(f"⚠️ 订单验证测试异常: {e}")
        
        self.take_screenshot("order_validation")
        print("✅ 订单验证功能测试完成")
    
    def _get_dish_name(self, dish_element):
        """获取菜品名称"""
        try:
            name_element = dish_element.get_element(".dish-name, .food-name")
            return name_element.inner_text if name_element else "未知菜品"
        except:
            return "未知菜品"
    
    def _add_dish_to_cart(self, dish_element, count=1):
        """添加菜品到购物车"""
        try:
            add_btn = dish_element.get_element(".add-btn, .plus-btn, .van-stepper__plus")
            if add_btn:
                for i in range(count):
                    add_btn.click()
                    time.sleep(0.5)
                print(f"✅ 已添加菜品 {count} 份")
            else:
                print("⚠️ 添加按钮未找到")
        except Exception as e:
            print(f"⚠️ 添加菜品失败: {e}")
    
    def _verify_cart_content(self, expected_dishes):
        """验证购物车内容"""
        try:
            # 检查购物车列表
            cart_items = self.page.get_elements(".order-item, .cart-item")
            print(f"🛒 购物车中有 {len(cart_items)} 个菜品")
            
            if len(cart_items) >= len(expected_dishes):
                print("✅ 购物车菜品数量正确")
                
                # 验证每个菜品的信息
                for i, item in enumerate(cart_items[:len(expected_dishes)]):
                    try:
                        name_element = item.get_element(".dish-name, .item-name")
                        count_element = item.get_element(".count, .quantity")
                        
                        if name_element and count_element:
                            name = name_element.inner_text
                            count = count_element.inner_text or count_element.value
                            print(f"📋 菜品{i+1}: {name} x {count}")
                    except:
                        print(f"⚠️ 无法获取菜品{i+1}信息")
            else:
                print("⚠️ 购物车菜品数量不正确")
        
        except Exception as e:
            print(f"⚠️ 验证购物车内容失败: {e}")
    
    def _verify_order_submission(self):
        """验证订单提交结果"""
        try:
            # 等待提交完成
            time.sleep(3)
            
            # 检查是否有成功提示
            success_elements = self.page.get_elements(".van-toast, .success-toast")
            if success_elements:
                for element in success_elements:
                    text = element.inner_text
                    if "成功" in text:
                        print("✅ 订单提交成功")
                        return
            
            # 检查页面跳转
            current_page = self.app.get_current_page()
            if "order" in current_page.path and "today_order" not in current_page.path:
                print("✅ 订单提交后页面跳转正常")
            else:
                print("⚠️ 订单提交结果不明确")
        
        except Exception as e:
            print(f"⚠️ 验证订单提交结果失败: {e}")
    
    def _verify_order_in_list(self):
        """验证订单是否出现在列表中"""
        try:
            # 等待订单列表加载
            time.sleep(3)
            
            # 检查订单列表
            order_items = self.page.get_elements(".order-card, .order-item")
            if order_items:
                print(f"📋 订单列表中有 {len(order_items)} 个订单")
                
                # 检查第一个订单的信息
                first_order = order_items[0]
                try:
                    order_id_element = first_order.get_element(".order-id")
                    order_time_element = first_order.get_element(".order-time")
                    
                    if order_id_element:
                        order_id = order_id_element.inner_text
                        print(f"📝 最新订单ID: {order_id}")
                    
                    if order_time_element:
                        order_time = order_time_element.inner_text
                        print(f"⏰ 订单时间: {order_time}")
                    
                    print("✅ 订单已出现在列表中")
                except:
                    print("⚠️ 无法获取订单详细信息")
            else:
                print("⚠️ 订单列表为空")
        
        except Exception as e:
            print(f"⚠️ 验证订单列表失败: {e}")
    
    def _test_quantity_modification(self):
        """测试数量修改"""
        try:
            # 查找数量调整按钮
            plus_btns = self.page.get_elements(".plus-btn, .van-stepper__plus")
            minus_btns = self.page.get_elements(".minus-btn, .van-stepper__minus")
            
            if plus_btns:
                plus_btns[0].click()
                time.sleep(1)
                print("✅ 数量增加功能正常")
            
            if minus_btns:
                minus_btns[0].click()
                time.sleep(1)
                print("✅ 数量减少功能正常")
        
        except Exception as e:
            print(f"⚠️ 数量修改测试失败: {e}")
    
    def _test_dish_removal(self):
        """测试菜品删除"""
        try:
            # 查找删除按钮
            delete_btns = self.page.get_elements(".delete-btn, .remove-btn")
            
            if delete_btns:
                delete_btns[0].click()
                time.sleep(1)
                
                # 如果有确认对话框，点击确认
                try:
                    confirm_btn = self.page.get_element(".van-dialog__confirm")
                    if confirm_btn:
                        confirm_btn.click()
                        time.sleep(1)
                except:
                    pass
                
                print("✅ 菜品删除功能正常")
        
        except Exception as e:
            print(f"⚠️ 菜品删除测试失败: {e}")


if __name__ == '__main__':
    unittest.main()
