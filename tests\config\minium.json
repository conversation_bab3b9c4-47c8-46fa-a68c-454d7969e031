{"project_path": "../../", "dev_tool_path": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli", "app_id": "wx82283b353918af82", "test_port": 9420, "assert_capture": true, "close_ide": false, "auto_relaunch": true, "project_name": "楠楠家厨测试", "enable_app_log": true, "test_timeout": 60, "use_push": true, "push_mini_program": true, "test_env": {"API_BASE_URL": "http://8.148.231.104:3000/api", "TEST_USER": {"username": "test_user", "password": "test123456", "phone": "13800138000"}}, "mock_native_modal": {"wx.showModal": {"confirm": true, "cancel": false}, "wx.showToast": {"duration": 1000}}, "outputs": {"screenshot": true, "video": false, "log": true}}