[I 2025-07-30 11:15:47,858 minium minitest#432 _miniSetUp] =========Current case: test_01_user_registration_and_login=========
[I 2025-07-30 11:15:48,092 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.complete_functional_test, case info: CompleteFunctionalTest.test_01_user_registration_and_login
[I 2025-07-30 11:15:48,094 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:15:48,096 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:15:48,097 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:15:48,098 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:15:48,103 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:15:48,113 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:15:48,119 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"c760937b-3e11-47b5-b076-1265ee37aa62","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:15:48,197 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"c760937b-3e11-47b5-b076-1265ee37aa62","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:15:48,200 minium minitest#487 _miniSetUp] =========case: test_01_user_registration_and_login start=========
[I 2025-07-30 11:15:49,201 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 11:15:49,202 minium.App0832 app#891 navigate_to] NavigateTo: /pages/login/index
[D 2025-07-30 11:15:49,203 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"8e5c2931-3189-4136-b85c-7dbeeaf319b7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:15:49,204 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"8e5c2931-3189-4136-b85c-7dbeeaf319b7","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:15:49,205 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:15:49,206 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"a2292249-ef9e-4cc7-a890-af2a7f89c0b1","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:15:49,208 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"a2292249-ef9e-4cc7-a890-af2a7f89c0b1","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:15:49,208 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [2639063586128]{"id":"4cc43246-95ef-4591-9247-c20554244c97","method":"App.callWxMethod","params":{"method":"navigateTo","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 11:15:49,210 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 11:15:59,234 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"4cc43246-95ef-4591-9247-c20554244c97","error":{"message":"timeout"}}
[E 2025-07-30 11:15:59,235 minium.Conn6128 connection#668 __on_message] [4cc43246-95ef-4591-9247-c20554244c97]: timeout
[I 2025-07-30 11:15:59,236 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 4cc43246-95ef-4591-9247-c20554244c97
[W 2025-07-30 11:15:59,237 minium.App0832 app#1172 _change_route_async] 可能因频繁调用navigateTo导致timeout
[I 2025-07-30 11:16:14,252 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:16:14,252 minium minitest#799 _miniTearDown] =========Current case Down: test_01_user_registration_and_login=========
[I 2025-07-30 11:16:14,253 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:16:14,253 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"7c4dff0e-bd33-4d63-88d1-7bd85436d949","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:30,018 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"7c4dff0e-bd33-4d63-88d1-7bd85436d949","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzs3XlcE3f+P/BXEpCgCCgBIiIeWKgHAkq9peuBt67VWls81gO21UXrT9rVFqpfLbiyra5Fqm3BY7HaWpVaL0A8urZeFTlEa6H1Rg2XgIKEI8nvj0kmkwMI1OGI7+ejD5tMJjOfAPPK+/P5zCSCyqpqEEIIb4TN3QBCiJmjlCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL
[I 2025-07-30 11:16:30,023 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:16:30,026 minium basenative#63 wrapper] call BaseNative.get_start_up end 
