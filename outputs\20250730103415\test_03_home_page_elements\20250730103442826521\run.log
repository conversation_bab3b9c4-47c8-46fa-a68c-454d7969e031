[I 2025-07-30 10:34:42,836 minium minitest#432 _miniSetUp] =========Current case: test_03_home_page_elements=========
[I 2025-07-30 10:34:42,836 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_03_home_page_elements
[I 2025-07-30 10:34:42,837 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:42,837 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:42,837 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:42,837 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:42,838 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:42,839 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:42,839 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"e1ed5e10-07d5-41f6-848b-213617cac0bc","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,907 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"e1ed5e10-07d5-41f6-848b-213617cac0bc","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq7a5KbPYGQhCQQgkAIIjjisMcNQUBFdJRt5PlkdGYUR2V4uD9lfMOACjgqoijCIGBgREZQkUVIIIQkQDay3OTm3pu79O2l1vO9P05VdXV1dd++N4lJyPlB6n59+tSpc6r7/Pr7fmcpNG0XAAAAEYlI2tKWtrT3sR2yjISEhMT+AAMARAxfS1va0pb2vrWlLyMhIbF/oYbWforNOOf7tXxpS1vae2kzxg4ZXSYsVEJC4lBHNPDZS6ijZxkNklwkJN58iHole1nU+FlGkouExOGAvaebCMsgQkgcDW3/qk3n9+sKMKb80pa2tPerHR0Kaia/6PiIONZrjU2XacZ/kT6OhMQhjWZ8ljH5Nc1GT
[I 2025-07-30 10:34:42,938 minium minitest#487 _miniSetUp] =========case: test_03_home_page_elements start=========
[I 2025-07-30 10:34:43,939 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:34:43,940 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"88e16322-bfc7-450a-a9f9-b03c33e96a4c","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:43,943 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:43,948 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"88e16322-bfc7-450a-a9f9-b03c33e96a4c","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:43,949 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 88e16322-bfc7-450a-a9f9-b03c33e96a4c
[D 2025-07-30 10:34:58,969 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"218c72de-f4ee-4f7e-b7d5-37b47c505427","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:58,971 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"218c72de-f4ee-4f7e-b7d5-37b47c505427","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:58,971 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:58,971 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"6494d1c0-661c-43b6-bdec-96e26dfd4df9","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:58,973 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"6494d1c0-661c-43b6-bdec-96e26dfd4df9","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:01,974 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"e1b85a8a-dff7-42cd-a497-f1d115c1d46d","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:01,977 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"e1b85a8a-dff7-42cd-a497-f1d115c1d46d","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:01,978 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:01,978 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"fe5a35dc-0c0f-4b61-9a07-49fe403f4ecb","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:01,980 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"fe5a35dc-0c0f-4b61-9a07-49fe403f4ecb","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:01,981 minium page#716 _get_elements_by_css] try to get elements: view
[D 2025-07-30 10:35:01,981 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"ce6e5ded-1038-4a4c-9ee4-84a50dba647d","method":"Page.getElements","params":{"selector":"view","pageId":2}}
[D 2025-07-30 10:35:01,989 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"ce6e5ded-1038-4a4c-9ee4-84a50dba647d","result":{"elements":[{"elementId":"fd02b6bf-ef7b-4b78-ab45-9811dbe58dfc","tagName":"view"},{"elementId":"ad45d734-385f-46db-bdda-2a72792a34ed","tagName":"view"},{"elementId":"309568af-fe3e-4a77-b99f-8477b66e66a6","tagName":"view"},{"elementId":"b0c94023-6875-4aff-9e74-13477fd70b46","tagName":"view"},{"elementId":"d9c25d80-26ab-4061-8c04-cd02fabbc1e4","tagName":"view"},{"elementId":"a446a70b-8b0a-474b-afa5-1fe16c817b8b","tagName":"view"},{"elementId":"d0dec377-86
[I 2025-07-30 10:35:01,990 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A3BE00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A8D6D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A8D950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A33E10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A33820>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD3AD8950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A0B790>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A0B570>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A89850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A89F50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A477A0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EB8500>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A6B5B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A6BAF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A76F70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A1A810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A1A8D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A828E0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A82D00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA53B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA59F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA58B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA56D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA51D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5630>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4EB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5A90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5B30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5BD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5C70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5D10>]
[I 2025-07-30 10:35:01,991 minium page#716 _get_elements_by_css] try to get elements: text
[D 2025-07-30 10:35:01,991 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"b6e3c3a1-7d80-4859-9d6e-aefc02475aba","method":"Page.getElements","params":{"selector":"text","pageId":2}}
[D 2025-07-30 10:35:01,995 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"b6e3c3a1-7d80-4859-9d6e-aefc02475aba","result":{"elements":[{"elementId":"d04d2b00-2ccf-4a31-8999-cadca4aa04c5","tagName":"text"},{"elementId":"9e180d92-a547-497c-90e3-891366461a4e","tagName":"text"},{"elementId":"40f9ac82-afea-44fd-bf2e-a5e3faf83e59","tagName":"text"},{"elementId":"377f48cc-9940-47f3-8173-9dce1e36a44f","tagName":"text"},{"elementId":"4b3c1247-1453-4bc8-9b7c-f9860ac90b36","tagName":"text"}]}}
[I 2025-07-30 10:35:01,995 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5DB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA60D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5EF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA62B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA6170>]
[I 2025-07-30 10:35:01,996 minium page#716 _get_elements_by_css] try to get elements: image
[D 2025-07-30 10:35:01,996 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"e1124f7d-f859-489e-90d7-d53779331844","method":"Page.getElements","params":{"selector":"image","pageId":2}}
[D 2025-07-30 10:35:01,999 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"e1124f7d-f859-489e-90d7-d53779331844","result":{"elements":[{"elementId":"a364fda7-f1ea-4be9-b7bc-8cdf7c84f690","tagName":"image"},{"elementId":"d4d1a591-bdfb-4543-beb6-b6581139b13d","tagName":"image"},{"elementId":"0cc8f2d8-a82c-459f-91dc-50b7dfbf1a75","tagName":"image"}]}}
[I 2025-07-30 10:35:02,001 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA51D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5630>]
[I 2025-07-30 10:35:02,002 minium page#716 _get_elements_by_css] try to get elements: page
[D 2025-07-30 10:35:02,003 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"1e5c7180-5fd2-4d5d-a17e-2b7443e9cafd","method":"Page.getElements","params":{"selector":"page","pageId":2}}
[D 2025-07-30 10:35:02,007 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"1e5c7180-5fd2-4d5d-a17e-2b7443e9cafd","result":{"elements":[{"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","tagName":"body","nodeId":"d143550e"}]}}
[I 2025-07-30 10:35:02,007 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.CustomElement object at 0x000001ECD4A3BE00>]
[D 2025-07-30 10:35:02,008 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"fe650d17-6a22-48a1-ad95-9ff62a12f257","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","pageId":2,"nodeId":"d143550e"}}
[D 2025-07-30 10:35:02,011 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"fe650d17-6a22-48a1-ad95-9ff62a12f257","result":{"properties":["伦少，欢迎\n今日菜单已开放点菜，快来选你喜欢的菜吧～\n小李点了菜品：小菜\n推荐菜单\n热门菜品推荐\n去点菜 \n红烧肉\n小菜\n留言\n（暂无留言）\n更多 \n暂无留言\n点击开始第一条留言"]}}
[D 2025-07-30 10:35:02,011 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"f7e68235-a3e6-46c4-a653-545fecad63cc","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,101 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"f7e68235-a3e6-46c4-a653-545fecad63cc","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXnAHEWdPv75VPUxx/vOe+S+7xNCEAgggoJyR0U5IiiLiKAoX1c89rvLquu64nr8VuS7LOiKeCCKcixyr6CAFwYhISGEBHKR5H1zvO+b95yjr6r6/VHdPT09PfPO+yYhgdQT6PeZmurqqp6pZz71VHU3Wo4HAACAiEIIxRVXXPEDzEOVUVBQUDgYIACAiOFrxRVXXPEDyw9WLBMGSwoKCm8WRAXiAIIkHmCkHABEAIhIzOGjpoorrviwPOy/YV8+MOXvZyyjYhYFhbc89jPG0Uaxj1IWBYUjCtEuPwrFGZnKNKIvSoMUFN7UqK8jsoOPSGvKvgxEd0viQohaeUR0LDdcOYorrvjhzEUNYyaWp/EyG/VlEiOUW
[I 2025-07-30 10:35:02,103 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:02,104 minium minitest#799 _miniTearDown] =========Current case Down: test_03_home_page_elements=========
[I 2025-07-30 10:35:02,104 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:02,105 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"562c151b-ab8e-4686-84a0-a02d087671b7","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,184 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"562c151b-ab8e-4686-84a0-a02d087671b7","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//db1ccc7807ct/3CSHhCEEEFYRwxAsICMIiKgjKz5Vld3+r67Wsoqu/XWSXhVXxRBRFEEGOFRHwQiIEEkISIBc5Xo73Xt4x781MX1X1+6O6e3p6eubNe0lIIPUJ9PtMTXV1Vc/UZ771qeputBwPAAAAEYUQiiuuuOIHmYcqo6CgoHAoQAAAEcPXiiuuuOIHlx+qWCYMlhQUFN4siArEQQRJPMBwOQCIABCRmCNHTRVXXPEhedh/w758cMo/wFhGxSwKCm95HGCMo41gH6UsCgpHFaJdfgSKMzyVaURflAYpKLypUV9HZAcfltaUfRmI7pbEhRC18ojoWG6ochRXXPEjmYsaxkwsT+NlNurLJEYot
[I 2025-07-30 10:35:02,186 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:02,186 minium basenative#63 wrapper] call BaseNative.get_start_up end 
