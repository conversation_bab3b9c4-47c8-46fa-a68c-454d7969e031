#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import unittest
from minium import MiniTest
from utils.base_test import BaseTest


class TestLogin(BaseTest):
    """用户登录功能测试"""
    
    def setUp(self):
        super().setUp()
        # 清理应用数据，确保测试环境干净
        self.clear_app_data()
    
    def test_01_login_page_elements(self):
        """测试登录页面元素"""
        print("🧪 测试登录页面元素")
        
        # 导航到登录页
        self.navigate_to_page("/pages/login/index")
        
        # 验证页面标题
        self.assert_page_contains_text("楠楠家厨")
        self.assert_page_contains_text("家庭厨房管理助手")
        
        # 验证登录方式切换
        self.assert_element_exists(".tab-item[data-type='wechat']")
        self.assert_element_exists(".tab-item[data-type='password']")
        
        # 验证微信登录按钮
        wechat_tab = self.safe_click(".tab-item[data-type='wechat']")
        self.assert_element_exists(".wechat-login-btn")
        
        # 验证账号登录表单
        password_tab = self.safe_click(".tab-item[data-type='password']")
        self.assert_element_exists("input[placeholder*='用户名']")
        self.assert_element_exists("input[placeholder*='密码']")
        self.assert_element_exists(".login-btn")
        
        # 验证注册链接
        self.assert_element_exists(".register-link")
        
        self.take_screenshot("login_page_elements")
        print("✅ 登录页面元素验证完成")
    
    def test_02_account_login_validation(self):
        """测试账号登录表单验证"""
        print("🧪 测试账号登录表单验证")
        
        self.navigate_to_page("/pages/login/index")
        self.safe_click(".tab-item[data-type='password']")
        
        # 测试空表单提交
        self.safe_click(".login-btn")
        time.sleep(1)
        # 应该显示错误提示
        
        # 测试只输入用户名
        self.safe_input("input[placeholder*='用户名']", "testuser")
        self.safe_click(".login-btn")
        time.sleep(1)
        
        # 测试只输入密码
        self.safe_input("input[placeholder*='用户名']", "")
        self.safe_input("input[placeholder*='密码']", "password123")
        self.safe_click(".login-btn")
        time.sleep(1)
        
        self.take_screenshot("login_validation")
        print("✅ 登录表单验证测试完成")
    
    def test_03_account_login_success(self):
        """测试账号登录成功流程"""
        print("🧪 测试账号登录成功流程")
        
        # 使用基类的登录方法
        login_success = self.login_with_account()
        
        if login_success:
            # 验证登录后跳转到首页
            current_page = self.app.get_current_page()
            self.assertIn("home", current_page.path, "登录后应该跳转到首页")
            
            # 验证首页元素
            self.assert_page_contains_text("欢迎")
            self.assert_element_exists(".home-welcome-card")
            
            self.take_screenshot("login_success_home")
            print("✅ 账号登录成功测试完成")
        else:
            self.fail("登录失败")
    
    def test_04_account_login_failure(self):
        """测试账号登录失败流程"""
        print("🧪 测试账号登录失败流程")
        
        self.navigate_to_page("/pages/login/index")
        self.safe_click(".tab-item[data-type='password']")
        
        # 使用错误的用户名密码
        self.safe_input("input[placeholder*='用户名']", "wrong_user")
        self.safe_input("input[placeholder*='密码']", "wrong_password")
        self.safe_click(".login-btn")
        
        # 等待错误提示
        time.sleep(3)
        
        # 验证仍在登录页
        current_page = self.app.get_current_page()
        self.assertIn("login", current_page.path, "登录失败应该仍在登录页")
        
        self.take_screenshot("login_failure")
        print("✅ 账号登录失败测试完成")
    
    def test_05_remember_password(self):
        """测试记住密码功能"""
        print("🧪 测试记住密码功能")
        
        self.navigate_to_page("/pages/login/index")
        self.safe_click(".tab-item[data-type='password']")
        
        # 勾选记住密码
        try:
            remember_checkbox = self.page.get_element(".remember-password")
            if remember_checkbox:
                remember_checkbox.click()
        except:
            print("⚠️ 记住密码选项未找到")
        
        # 输入用户名密码并登录
        username = self.test_config["test_user"]["username"]
        password = self.test_config["test_user"]["password"]
        
        self.safe_input("input[placeholder*='用户名']", username)
        self.safe_input("input[placeholder*='密码']", password)
        self.safe_click(".login-btn")
        
        # 等待登录完成
        time.sleep(3)
        
        # 退出登录
        if "home" in self.app.get_current_page().path:
            self.switch_to_tab("/pages/mine/index")
            try:
                self.safe_click(".logout-btn")
                self.safe_click(".van-dialog__confirm")  # 确认退出
            except:
                print("⚠️ 退出登录按钮未找到")
        
        # 重新进入登录页，验证是否记住了密码
        time.sleep(2)
        self.navigate_to_page("/pages/login/index")
        self.safe_click(".tab-item[data-type='password']")
        
        # 检查用户名是否被记住
        username_input = self.page.get_element("input[placeholder*='用户名']")
        if username_input:
            saved_username = username_input.value
            print(f"保存的用户名: {saved_username}")
        
        self.take_screenshot("remember_password")
        print("✅ 记住密码功能测试完成")
    
    def test_06_register_navigation(self):
        """测试注册页面跳转"""
        print("🧪 测试注册页面跳转")
        
        self.navigate_to_page("/pages/login/index")
        
        # 点击注册链接
        try:
            self.safe_click(".register-link")
            time.sleep(2)
            
            # 验证跳转到注册页
            current_page = self.app.get_current_page()
            self.assertIn("register", current_page.path, "应该跳转到注册页")
            
            # 验证注册页面元素
            self.assert_page_contains_text("注册")
            self.assert_element_exists("input[placeholder*='用户名']")
            self.assert_element_exists("input[placeholder*='手机号']")
            self.assert_element_exists("input[placeholder*='密码']")
            
            self.take_screenshot("register_page")
            print("✅ 注册页面跳转测试完成")
            
        except Exception as e:
            print(f"⚠️ 注册页面跳转测试失败: {e}")


if __name__ == '__main__':
    unittest.main()
