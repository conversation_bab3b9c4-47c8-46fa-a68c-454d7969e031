[I 2025-07-30 10:42:39,750 minium minitest#432 _miniSetUp] =========Current case: test_01_basic_navigation_and_api=========
[I 2025-07-30 10:42:39,752 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_01_basic_navigation_and_api
[I 2025-07-30 10:42:39,752 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:42:39,753 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:42:39,753 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:42:39,753 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:42:39,753 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:42:39,798 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:42:39,799 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"ee3cc36d-a682-49d4-9cf3-b7964d4aabfd","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:39,879 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"ee3cc36d-a682-49d4-9cf3-b7964d4aabfd","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAFdWZN/ycU3frpheWBppm37eALYIQRSejWUAQCSo6TqKYIajAJKNmjO/rgqLzfkm+GJP5BJXhi0syjmJUBBFUIIkiEWmghbAj0A10X7ob6PUudW/V8/5xquqeu3bdprtvL88vM8dfn3vqnFOHqt99zu+cqssCahgAAIAxhojEiRMn3srcUhkCgUBoC3AAYIxZfxMnTpx463KKZQgEQtuCW6zjKB9x4sS7FG+LWMZyfQgEQueCrA6tBRHLyPW2hKMBg7RKncSJE29/3hb38mXFMhSzEAjdBJcT47TQl4nRlw4x9yNOnHibcZDu+jb3ZezHL9EFKeohEDogZF2wfUyacY3DftFm9QURSE0IhE6FyA2bfH4Sd
[I 2025-07-30 10:42:39,880 minium minitest#487 _miniSetUp] =========case: test_01_basic_navigation_and_api start=========
[I 2025-07-30 10:42:40,881 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:42:40,885 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"740ee0f7-788d-486d-b170-2cedebf10e9b","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:40,914 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"740ee0f7-788d-486d-b170-2cedebf10e9b","result":{"pageId":9,"path":"pages/notification_center/index","query":{}}}
[D 2025-07-30 10:42:40,915 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:40,919 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"d4f5c5f7-78b9-4d9c-8e23-882d86de39dc","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:40,929 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"d4f5c5f7-78b9-4d9c-8e23-882d86de39dc","result":{"result":{"pageId":9,"path":"pages/notification_center/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:42:40,930 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"356ae2eb-4243-4c9f-ad91-bd79d57c8bef","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:42:40,936 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:42:41,199 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753843361187,"webviewId":2,"routeEventId":"2_1753843360959","renderer":"webview"},1753843361190]}}
[I 2025-07-30 10:42:41,200 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843361187, 'webviewId': 2, 'routeEventId': '2_1753843360959', 'renderer': 'webview'}, 1753843361190]}
[D 2025-07-30 10:42:41,205 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"356ae2eb-4243-4c9f-ad91-bd79d57c8bef","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:41,206 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 356ae2eb-4243-4c9f-ad91-bd79d57c8bef
[D 2025-07-30 10:42:44,208 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"c97d93ec-ead7-4499-a7cf-a8a5dea7a4b2","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:44,213 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"c97d93ec-ead7-4499-a7cf-a8a5dea7a4b2","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:42:44,215 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:44,219 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"625c6a88-e1bf-41bf-bc0d-91d1c22c125f","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:44,223 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"625c6a88-e1bf-41bf-bc0d-91d1c22c125f","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:42:44,401 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:42:44,401 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"578f1c91-9f78-4a0d-a3d4-b5fb19354630","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:44,476 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"578f1c91-9f78-4a0d-a3d4-b5fb19354630","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAJEWZNv6+kWcdXX3Mfd83w3ANIA4qLvcoityKiAjCyvKp6LeKu+qyeH8r+lsWVgFXRRTlWAQEFBDwQhAGGIaZAeZiZrrn6O7po7qOPCN+f0RmVlRWVnV1z4wzMPHAZL8VFRkZkVXx1Ps+cSRajgcAAICIjDFpS1va0t7HdsQyEhISEvsDBAAQMXotbWlLW9r71pa+jISExP6FGln7KTajlO7X8qUtbWnvpU0IecvoMlGhEhISb3WIgc9eQh0+y3CQ5CIh8faD6JXsZVGjZxlJLhIShwL2nm4ElkGEiDga2sFVm84f1BVgRPmlLW1p71dbHApqJj/v+Ig40muNTJdpxn+RPo6ExFsazfgsI/Jrmo2YGnOHZ
[I 2025-07-30 10:42:44,480 minium page#716 _get_elements_by_css] try to get elements: page
[D 2025-07-30 10:42:44,485 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"e5f09a0d-f140-48c6-ab73-c6ecf1d75fff","method":"Page.getElements","params":{"selector":"page","pageId":2}}
[D 2025-07-30 10:42:44,522 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"e5f09a0d-f140-48c6-ab73-c6ecf1d75fff","result":{"elements":[{"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","tagName":"body","nodeId":"d143550e"}]}}
[I 2025-07-30 10:42:44,522 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.CustomElement object at 0x00000225332EEF90>]
[D 2025-07-30 10:42:44,523 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"f13e9e68-faba-4d62-8d8b-a27cc7c98b09","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","pageId":2,"nodeId":"d143550e"}}
[D 2025-07-30 10:42:44,525 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"f13e9e68-faba-4d62-8d8b-a27cc7c98b09","result":{"properties":["伦少，欢迎\n今日菜单已开放点菜，快来选你喜欢的菜吧～\n小李点了菜品：小菜\n推荐菜单\n热门菜品推荐\n去点菜 \n红烧肉\n小菜\n留言\n（暂无留言）\n更多 \n暂无留言\n点击开始第一条留言"]}}
[I 2025-07-30 10:42:44,526 minium page#716 _get_elements_by_css] try to get elements: view, text, image
[D 2025-07-30 10:42:44,526 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"0600c1fc-77c6-42b7-b4ca-e0089c64c935","method":"Page.getElements","params":{"selector":"view, text, image","pageId":2}}
[D 2025-07-30 10:42:44,530 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"0600c1fc-77c6-42b7-b4ca-e0089c64c935","result":{"elements":[{"elementId":"fd02b6bf-ef7b-4b78-ab45-9811dbe58dfc","tagName":"view"},{"elementId":"ad45d734-385f-46db-bdda-2a72792a34ed","tagName":"view"},{"elementId":"309568af-fe3e-4a77-b99f-8477b66e66a6","tagName":"view"},{"elementId":"b0c94023-6875-4aff-9e74-13477fd70b46","tagName":"view"},{"elementId":"d9c25d80-26ab-4061-8c04-cd02fabbc1e4","tagName":"view"},{"elementId":"a364fda7-f1ea-4be9-b7bc-8cdf7c84f690","tagName":"image"},{"elementId":"a446a70b-8
[I 2025-07-30 10:42:44,531 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332EEF90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533321D10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533321F90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332E35C0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332E36F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253339C950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253329F020>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253329F130>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253333D650>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253333DA50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225333BC230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225333BC320>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253331B690>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253331A350>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533334AE0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332BE2D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332BE210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533331020>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533332830>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337582D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337584B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758370>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758410>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758550>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337585F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758690>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758730>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337587D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758870>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758910>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337589B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758A50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758AF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758B90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758C30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758CD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758D70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758E10>]
[D 2025-07-30 10:42:44,535 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"12e35be9-5e01-4a3e-89ee-039ca220028a","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:42:44,538 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:42:44,795 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843364784,"webviewId":5,"routeEventId":"5_1753843364553","renderer":"webview"},1753843364788]}}
[I 2025-07-30 10:42:44,798 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843364784, 'webviewId': 5, 'routeEventId': '5_1753843364553', 'renderer': 'webview'}, 1753843364788]}
[D 2025-07-30 10:42:44,806 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"12e35be9-5e01-4a3e-89ee-039ca220028a","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:44,807 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 12e35be9-5e01-4a3e-89ee-039ca220028a
[D 2025-07-30 10:42:47,809 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"c4263db3-02f6-4991-b066-ff3ad0f01abe","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:47,817 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"c4263db3-02f6-4991-b066-ff3ad0f01abe","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:42:47,820 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:47,821 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"f721998b-bef1-4360-abea-79e348948aef","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:47,825 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"f721998b-bef1-4360-abea-79e348948aef","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:42:47,830 minium minitest#897 capture] capture assertIn-success_104247830033.png
[D 2025-07-30 10:42:47,830 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"1e6596c7-6135-42d3-a7a2-ee99d828c522","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:47,919 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"1e6596c7-6135-42d3-a7a2-ee99d828c522","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:42:47,921 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item, view[bindtap]
[D 2025-07-30 10:42:47,921 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"2a7bfaf0-82be-4937-b730-83ccd63255fb","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item, view[bindtap]","pageId":5}}
[D 2025-07-30 10:42:47,925 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"2a7bfaf0-82be-4937-b730-83ccd63255fb","result":{"elements":[{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","tagName":"view"},{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","tagName":"view"}]}}
[I 2025-07-30 10:42:47,926 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759130>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759270>]
[D 2025-07-30 10:42:47,926 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"66a0b407-677c-440a-a21f-aeda699675b5","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:47,928 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:48,181 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843368172,"webviewId":3,"routeEventId":"3_1753843367941","renderer":"webview"},1753843368175]}}
[D 2025-07-30 10:42:48,189 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"66a0b407-677c-440a-a21f-aeda699675b5","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:48,189 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843368172, 'webviewId': 3, 'routeEventId': '3_1753843367941', 'renderer': 'webview'}, 1753843368175]}
[I 2025-07-30 10:42:48,191 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 66a0b407-677c-440a-a21f-aeda699675b5
[D 2025-07-30 10:42:51,196 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"a478ce85-a456-48ce-8474-8acf691f90c7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:51,203 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"a478ce85-a456-48ce-8474-8acf691f90c7","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:42:51,206 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:51,208 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"895f5435-1c5b-4caf-b109-4fdceb24e1b2","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:51,214 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"895f5435-1c5b-4caf-b109-4fdceb24e1b2","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:42:51,225 minium minitest#897 capture] capture assertIn-success_104251225533.png
[D 2025-07-30 10:42:51,226 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"31f4f6a0-71f6-4caa-97ee-bf90ca3054b8","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:51,341 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"31f4f6a0-71f6-4caa-97ee-bf90ca3054b8","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:42:51,343 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:42:51,343 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"412478cc-e279-4b53-ace1-247483d4a999","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:42:51,347 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"412478cc-e279-4b53-ace1-247483d4a999","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:42:51,347 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759590>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337580F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759450>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759770>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759A90>]
[I 2025-07-30 10:42:51,348 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:42:51,349 minium minitest#799 _miniTearDown] =========Current case Down: test_01_basic_navigation_and_api=========
[I 2025-07-30 10:42:51,350 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:42:51,350 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"93c7d27b-892c-4380-838d-d468f30accfa","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:51,455 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"93c7d27b-892c-4380-838d-d468f30accfa","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:42:51,456 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:42:51,457 minium basenative#63 wrapper] call BaseNative.get_start_up end 
