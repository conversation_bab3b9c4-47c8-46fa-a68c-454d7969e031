#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接连接测试 - 假设微信开发者工具已经运行
"""

import socket
import time

def test_port_connection(host='localhost', port=25209):
    """测试端口连接"""
    print(f"🔌 测试端口连接: {host}:{port}")
    
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)  # 5秒超时
        
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口 {port} 连接成功")
            return True
        else:
            print(f"❌ 端口 {port} 连接失败 (错误码: {result})")
            return False
            
    except Exception as e:
        print(f"❌ 端口连接异常: {e}")
        return False

def test_websocket_connection():
    """测试WebSocket连接"""
    print("🌐 测试WebSocket连接...")
    
    try:
        import websocket
        
        # 构建WebSocket URL
        ws_url = "ws://localhost:25209"
        print(f"📡 连接URL: {ws_url}")
        
        # 创建WebSocket连接
        ws = websocket.create_connection(ws_url, timeout=10)
        
        # 发送测试消息
        test_message = '{"method":"getSystemInfo","params":{}}'
        ws.send(test_message)
        
        # 接收响应
        response = ws.recv()
        print(f"📨 收到响应: {response[:100]}...")
        
        ws.close()
        print("✅ WebSocket连接成功")
        return True
        
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")
        return False

def main():
    print("🚀 直接连接测试")
    print("📱 目标: 微信开发者工具 (端口25209)")
    print("=" * 50)
    
    # 测试1: 基础端口连接
    port_ok = test_port_connection()
    
    if not port_ok:
        print("\n🔧 端口连接失败，请检查:")
        print("  1. 微信开发者工具是否已启动")
        print("  2. 服务端口25209是否已开启")
        print("  3. 自动化接口是否已启用")
        return False
    
    # 测试2: WebSocket连接
    print("\n" + "-" * 30)
    ws_ok = test_websocket_connection()
    
    if ws_ok:
        print("\n🎉 连接测试全部成功！")
        print("✅ 端口连接正常")
        print("✅ WebSocket通信正常")
        print("✅ 微信开发者工具自动化接口工作正常")
        
        print("\n🎯 现在可以运行minium测试了")
        return True
    else:
        print("\n⚠️ WebSocket连接失败")
        print("端口连接正常，但WebSocket通信失败")
        print("可能的原因:")
        print("  1. 自动化接口未正确启用")
        print("  2. 小程序项目未加载")
        print("  3. WebSocket协议不匹配")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 准备运行完整测试...")
        
        # 如果连接成功，尝试运行简单的minium测试
        try:
            print("正在启动minium测试...")
            import subprocess
            import sys
            
            result = subprocess.run([
                sys.executable, "simple_connection_test.py"
            ], capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("🎉 minium测试也成功了！")
            else:
                print("⚠️ minium测试仍有问题")
                print("但基础连接是正常的")
                
        except Exception as e:
            print(f"⚠️ 无法运行minium测试: {e}")
    
    else:
        print("\n❌ 连接测试失败")
        print("请先解决连接问题再运行minium测试")
