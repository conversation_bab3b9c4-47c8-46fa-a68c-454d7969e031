{"case_name": "test_01_basic_navigation_and_api", "run_time": "20250730 10:42:39", "test_type": "StableFunctionalTest", "case_doc": "测试基础导航和API响应", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843359.880822, "is_failure": false, "is_error": false, "module": "E:.wx-nan.stable_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "", "path": "images\\setup.png", "ts": 1753843359, "datetime": "2025-07-30 10:42:39", "use_region": false}, {"name": "assertIn-success", "url": "/pages/home/<USER>", "path": "images\\assertIn-success.png", "ts": 1753843364, "datetime": "2025-07-30 10:42:44", "use_region": false}, {"name": "assertIn-success", "url": "/pages/order/index", "path": "images\\assertIn-success_104247830033.png", "ts": 1753843367, "datetime": "2025-07-30 10:42:47", "use_region": false}, {"name": "assertIn-success", "url": "/pages/mine/index", "path": "images\\assertIn-success_104251225533.png", "ts": 1753843371, "datetime": "2025-07-30 10:42:51", "use_region": false}, {"name": "teardown", "url": "/pages/mine/index", "path": "images\\teardown.png", "ts": 1753843371, "datetime": "2025-07-30 10:42:51", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [{"name": "assertIn", "ret": true, "msg": null, "img": "assertIn-success", "wxml": ""}, {"name": "assertIn", "ret": true, "msg": null, "img": "assertIn-success", "wxml": ""}, {"name": "assertIn", "ret": true, "msg": null, "img": "assertIn-success", "wxml": ""}], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843371.4586926, "appId": "", "appName": "", "source": {"code": ["    def test_01_basic_navigation_and_api(self):\n", "        \"\"\"测试基础导航和API响应\"\"\"\n", "        print(\"🧪 测试基础导航和API响应\")\n", "        \n", "        try:\n", "            # 测试首页\n", "            self.app.switch_tab(\"/pages/home/<USER>\")\n", "            time.sleep(3)\n", "            \n", "            home_page = self.app.get_current_page()\n", "            self.assertIn(\"home\", home_page.path)\n", "            \n", "            # 检查页面内容\n", "            page_content = home_page.get_element(\"page\").inner_text\n", "            if \"楠楠家厨\" in page_content or \"欢迎\" in page_content or len(page_content) > 50:\n", "                self.log_result(\"首页API数据加载\", True, f\"页面内容长度: {len(page_content)}\")\n", "            else:\n", "                self.log_result(\"首页API数据加载\", False, \"页面内容过少或无关键信息\")\n", "            \n", "            # 检查页面元素\n", "            elements = home_page.get_elements(\"view, text, image\")\n", "            self.log_result(\"首页UI元素\", True, f\"找到{len(elements)}个UI元素\")\n", "            \n", "            # 测试订餐页\n", "            self.app.switch_tab(\"/pages/order/index\")\n", "            time.sleep(3)\n", "            \n", "            order_page = self.app.get_current_page()\n", "            self.assertIn(\"order\", order_page.path)\n", "            \n", "            # 检查菜品数据\n", "            dish_elements = order_page.get_elements(\".dish-card, .food-item, .menu-item, view[bindtap]\")\n", "            if len(dish_elements) > 5:\n", "                self.log_result(\"订餐页菜品数据\", True, f\"找到{len(dish_elements)}个可交互元素\")\n", "            else:\n", "                self.log_result(\"订餐页菜品数据\", False, f\"可交互元素较少: {len(dish_elements)}\")\n", "            \n", "            # 测试个人中心\n", "            self.app.switch_tab(\"/pages/mine/index\")\n", "            time.sleep(3)\n", "            \n", "            mine_page = self.app.get_current_page()\n", "            self.assertIn(\"mine\", mine_page.path)\n", "            \n", "            # 检查菜单项\n", "            menu_elements = mine_page.get_elements(\"view[bindtap], button\")\n", "            self.log_result(\"个人中心菜单\", True, f\"找到{len(menu_elements)}个菜单项\")\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"基础导航测试\", False, f\"异常: {e}\")\n"], "start": 55}, "filename": "result.json"}