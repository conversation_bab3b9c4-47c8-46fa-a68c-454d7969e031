{"case_name": "test_connection", "run_time": "20250730 10:40:01", "test_type": "ConnectionTest", "case_doc": "测试连接", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843201.8954082, "is_failure": false, "is_error": false, "module": "E:.wx-nan.connection_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "", "path": "images\\setup.png", "ts": 1753843201, "datetime": "2025-07-30 10:40:01", "use_region": false}, {"name": "assertIsNotNone-success", "url": "", "path": "images\\assertIsNotNone-success.png", "ts": 1753843202, "datetime": "2025-07-30 10:40:02", "use_region": false}, {"name": "assertIsNotNone-success", "url": "/pages/home/<USER>", "path": "images\\assertIsNotNone-success_104002174196.png", "ts": 1753843202, "datetime": "2025-07-30 10:40:02", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753843202, "datetime": "2025-07-30 10:40:02", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [{"name": "assertIsNotNone", "ret": true, "msg": null, "img": "assertIsNotNone-success", "wxml": ""}, {"name": "assertIsNotNone", "ret": true, "msg": null, "img": "assertIsNotNone-success", "wxml": ""}], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843202.4099946, "appId": "", "appName": "", "source": {"code": ["    def test_connection(self):\n", "        \"\"\"测试连接\"\"\"\n", "        print(\"🧪 测试minium连接\")\n", "        \n", "        # 获取应用实例\n", "        app = self.mini.app\n", "        self.assertIsNotNone(app, \"应用实例应该存在\")\n", "        \n", "        # 获取当前页面\n", "        page = app.get_current_page()\n", "        self.assertIsNotNone(page, \"当前页面应该存在\")\n", "        \n", "        print(f\"📱 当前页面: {page.path}\")\n", "        print(\"✅ 连接测试成功\")\n"], "start": 12}, "filename": "result.json"}