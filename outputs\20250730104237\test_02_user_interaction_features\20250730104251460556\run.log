[I 2025-07-30 10:42:51,461 minium minitest#432 _miniSetUp] =========Current case: test_02_user_interaction_features=========
[I 2025-07-30 10:42:51,462 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_02_user_interaction_features
[I 2025-07-30 10:42:51,462 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:42:51,462 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:42:51,463 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:42:51,463 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:42:51,463 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:42:51,464 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:42:51,464 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"02d91f60-f013-45c0-9e0d-ca7b03e444aa","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:51,552 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"02d91f60-f013-45c0-9e0d-ca7b03e444aa","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:42:51,553 minium minitest#487 _miniSetUp] =========case: test_02_user_interaction_features start=========
[I 2025-07-30 10:42:52,554 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:42:52,555 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"a5d0125c-8cf2-4aa9-a483-78aa87743bec","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:52,557 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:52,559 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"a5d0125c-8cf2-4aa9-a483-78aa87743bec","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:52,560 minium.Conn2448 connection#704 _handle_async_msg] received async msg: a5d0125c-8cf2-4aa9-a483-78aa87743bec
[D 2025-07-30 10:43:07,562 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"920d1bff-e141-4a33-93d4-c32beefcaace","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:07,564 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"920d1bff-e141-4a33-93d4-c32beefcaace","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:43:07,565 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:07,565 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"547589b4-7290-4b99-9eea-74879a8f7b55","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:07,568 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"547589b4-7290-4b99-9eea-74879a8f7b55","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:09,570 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"7775eb89-9f3b-4c9b-a073-d9d4a8303401","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:09,574 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"7775eb89-9f3b-4c9b-a073-d9d4a8303401","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:43:09,576 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:09,577 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"0ade7c10-b799-472a-a209-5125b8570c48","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:09,581 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"0ade7c10-b799-472a-a209-5125b8570c48","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:43:09,586 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:43:09,586 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"647c0279-b480-4179-9b65-203543aa54f4","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:43:09,591 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"647c0279-b480-4179-9b65-203543aa54f4","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:43:09,592 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AAD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375A8F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375A710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AB70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AC10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375ACB0>]
[D 2025-07-30 10:43:09,593 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"3510b5f4-753f-4fcd-8c30-158ecf1db3f6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,597 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"3510b5f4-753f-4fcd-8c30-158ecf1db3f6","result":{"properties":["留言"]}}
[D 2025-07-30 10:43:09,597 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"480d4610-bf7a-4a69-928a-212209e8255c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:43:09,603 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"480d4610-bf7a-4a69-928a-212209e8255c","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:43:09,604 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"894077c9-110a-456d-a486-c23a4beebfd8","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:43:09,607 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"894077c9-110a-456d-a486-c23a4beebfd8","result":{"properties":["我的菜品"]}}
[D 2025-07-30 10:43:09,607 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"166e3f9d-ab24-41bd-a609-a5308d3225b5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:43:09,610 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"166e3f9d-ab24-41bd-a609-a5308d3225b5","result":{"properties":["通知中心"]}}
[D 2025-07-30 10:43:09,611 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"b13bdf58-d99d-4656-8443-e248f7dac59b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","pageId":3}}
[D 2025-07-30 10:43:09,613 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"b13bdf58-d99d-4656-8443-e248f7dac59b","result":{"properties":["我的订单"]}}
[D 2025-07-30 10:43:09,614 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"d61cdbd6-b802-48a2-8ce9-0774565c85f4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","pageId":3}}
[D 2025-07-30 10:43:09,619 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"d61cdbd6-b802-48a2-8ce9-0774565c85f4","result":{"properties":["退出登录"]}}
[D 2025-07-30 10:43:09,620 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"051adefb-7ffe-4397-b377-00a783b82755","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,623 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"051adefb-7ffe-4397-b377-00a783b82755","result":{"properties":["留言"]}}
[D 2025-07-30 10:43:09,623 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"91c436b9-cfa8-4946-96f5-2f5b1310f42b","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,625 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"91c436b9-cfa8-4946-96f5-2f5b1310f42b","result":{"styles":["auto"]}}
[D 2025-07-30 10:43:09,626 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"2cb9fd5f-1185-49c7-a4f4-edac2da114f2","method":"Element.tap","params":{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,700 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/family_message/index"}]}}
[D 2025-07-30 10:43:09,761 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"2cb9fd5f-1185-49c7-a4f4-edac2da114f2","result":{"pageX":187.5,"pageY":192,"clientX":187.5,"clientY":192}}
[D 2025-07-30 10:43:11,518 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/family_message/index","query":{},"openType":"navigateTo","timeStamp":1753843391506,"webviewId":10,"routeEventId":"10_1753843390826","renderer":"webview"},1753843391508]}}
[I 2025-07-30 10:43:11,520 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/family_message/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843391506, 'webviewId': 10, 'routeEventId': '10_1753843390826', 'renderer': 'webview'}, 1753843391508]}
[D 2025-07-30 10:43:12,762 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"eee8d6ef-e5ef-49e7-8d91-f5ca73339d8d","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:12,771 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"eee8d6ef-e5ef-49e7-8d91-f5ca73339d8d","result":{"pageId":10,"path":"pages/family_message/index","query":{}}}
[D 2025-07-30 10:43:12,771 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:12,771 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"b5999990-6f07-4bc8-945a-50daacaac0b1","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:12,774 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"b5999990-6f07-4bc8-945a-50daacaac0b1","result":{"result":{"pageId":10,"path":"pages/family_message/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:12,775 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:12,775 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"484786ed-4007-4e86-8bd8-51521ff9920a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[]}}
[D 2025-07-30 10:43:12,777 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"484786ed-4007-4e86-8bd8-51521ff9920a","result":{"result":[{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"},{"pageId":10,"path":"pages/family_message/index","query":{},"renderer":"webview"}]}}
[I 2025-07-30 10:43:12,777 minium.App7152 app#971 navigate_back] NavigateBack from:/pages/family_message/index
[D 2025-07-30 10:43:12,778 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"f09f3d0a-c8da-41d0-a1a9-c2ca43a84aed","method":"App.callWxMethod","params":{"method":"navigateBack","args":[{"delta":1}]}}
[D 2025-07-30 10:43:12,779 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"navigateBack_before_global","args":[{"delta":1}]}}
[D 2025-07-30 10:43:12,782 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"f09f3d0a-c8da-41d0-a1a9-c2ca43a84aed","result":{"result":{}}}
[D 2025-07-30 10:43:13,065 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"navigateBack","timeStamp":1753843393014,"webviewId":3,"routeEventId":"3_1753843392794","renderer":"webview"},1753843393020]}}
[I 2025-07-30 10:43:13,067 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'navigateBack', 'timeStamp': 1753843393014, 'webviewId': 3, 'routeEventId': '3_1753843392794', 'renderer': 'webview'}, 1753843393020]}
[I 2025-07-30 10:43:14,068 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:43:14,069 minium minitest#799 _miniTearDown] =========Current case Down: test_02_user_interaction_features=========
[I 2025-07-30 10:43:14,070 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:43:14,070 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"4e595f3b-8f9d-4d91-b36f-6cc9d8a378a3","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:14,125 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"4e595f3b-8f9d-4d91-b36f-6cc9d8a378a3","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:43:14,126 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:43:14,126 minium basenative#63 wrapper] call BaseNative.get_start_up end 
