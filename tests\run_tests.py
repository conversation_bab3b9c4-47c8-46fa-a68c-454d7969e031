#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import unittest
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入测试模块
from test_01_login import TestLogin
from test_02_home import TestHome
from test_03_order import TestOrder
from test_04_order_flow import TestOrderFlow
from test_05_user_management import TestUserManagement


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.results = {
            "start_time": None,
            "end_time": None,
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "error_tests": 0,
            "test_details": []
        }
        
        # 创建报告目录
        self.reports_dir = "tests/reports"
        self.screenshots_dir = "tests/reports/screenshots"
        os.makedirs(self.reports_dir, exist_ok=True)
        os.makedirs(self.screenshots_dir, exist_ok=True)
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行楠楠家厨小程序系统测试")
        print("=" * 60)
        
        self.results["start_time"] = datetime.now().isoformat()
        
        # 定义测试套件
        test_suites = [
            ("用户登录功能测试", TestLogin),
            ("首页功能测试", TestHome),
            ("订餐页面测试", TestOrder),
            ("完整订餐流程测试", TestOrderFlow),
            ("用户管理功能测试", TestUserManagement)
        ]
        
        # 运行每个测试套件
        for suite_name, test_class in test_suites:
            print(f"\n📋 运行测试套件: {suite_name}")
            print("-" * 40)
            
            suite_result = self._run_test_suite(test_class)
            self.results["test_details"].append({
                "suite_name": suite_name,
                "class_name": test_class.__name__,
                **suite_result
            })
        
        self.results["end_time"] = datetime.now().isoformat()
        
        # 生成测试报告
        self._generate_report()
        
        # 显示测试总结
        self._print_summary()
        
        return self.results
    
    def _run_test_suite(self, test_class):
        """运行单个测试套件"""
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=2, stream=open(os.devnull, 'w'))
        
        result = runner.run(suite)
        
        suite_result = {
            "total": result.testsRun,
            "passed": result.testsRun - len(result.failures) - len(result.errors),
            "failed": len(result.failures),
            "errors": len(result.errors),
            "failures": [str(failure[1]) for failure in result.failures],
            "error_details": [str(error[1]) for error in result.errors]
        }
        
        # 更新总计
        self.results["total_tests"] += suite_result["total"]
        self.results["passed_tests"] += suite_result["passed"]
        self.results["failed_tests"] += suite_result["failed"]
        self.results["error_tests"] += suite_result["errors"]
        
        # 打印套件结果
        print(f"✅ 通过: {suite_result['passed']}")
        print(f"❌ 失败: {suite_result['failed']}")
        print(f"⚠️ 错误: {suite_result['errors']}")
        
        if suite_result["failures"]:
            print("失败详情:")
            for failure in suite_result["failures"]:
                print(f"  - {failure[:100]}...")
        
        if suite_result["error_details"]:
            print("错误详情:")
            for error in suite_result["error_details"]:
                print(f"  - {error[:100]}...")
        
        return suite_result
    
    def _generate_report(self):
        """生成测试报告"""
        # JSON报告
        json_report_path = os.path.join(self.reports_dir, "test_report.json")
        with open(json_report_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)
        
        # HTML报告
        html_report_path = os.path.join(self.reports_dir, "test_report.html")
        self._generate_html_report(html_report_path)
        
        print(f"\n📊 测试报告已生成:")
        print(f"  JSON报告: {json_report_path}")
        print(f"  HTML报告: {html_report_path}")
    
    def _generate_html_report(self, file_path):
        """生成HTML测试报告"""
        start_time = datetime.fromisoformat(self.results["start_time"])
        end_time = datetime.fromisoformat(self.results["end_time"])
        duration = (end_time - start_time).total_seconds()
        
        success_rate = (self.results["passed_tests"] / self.results["total_tests"] * 100) if self.results["total_tests"] > 0 else 0
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>楠楠家厨小程序测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ text-align: center; margin-bottom: 30px; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }}
        .summary-card {{ background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }}
        .summary-card h3 {{ margin: 0 0 10px 0; color: #333; }}
        .summary-card .number {{ font-size: 2em; font-weight: bold; }}
        .passed {{ color: #28a745; }}
        .failed {{ color: #dc3545; }}
        .error {{ color: #ffc107; }}
        .total {{ color: #007bff; }}
        .test-suite {{ margin-bottom: 30px; border: 1px solid #ddd; border-radius: 8px; overflow: hidden; }}
        .suite-header {{ background: #007bff; color: white; padding: 15px; }}
        .suite-content {{ padding: 15px; }}
        .test-item {{ padding: 10px; border-bottom: 1px solid #eee; }}
        .test-item:last-child {{ border-bottom: none; }}
        .status-passed {{ color: #28a745; }}
        .status-failed {{ color: #dc3545; }}
        .status-error {{ color: #ffc107; }}
        .progress-bar {{ width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }}
        .progress-fill {{ height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏠 楠楠家厨小程序系统测试报告</h1>
            <p>测试时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')} - {end_time.strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>测试耗时: {duration:.2f} 秒</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="number total">{self.results['total_tests']}</div>
            </div>
            <div class="summary-card">
                <h3>通过测试</h3>
                <div class="number passed">{self.results['passed_tests']}</div>
            </div>
            <div class="summary-card">
                <h3>失败测试</h3>
                <div class="number failed">{self.results['failed_tests']}</div>
            </div>
            <div class="summary-card">
                <h3>错误测试</h3>
                <div class="number error">{self.results['error_tests']}</div>
            </div>
        </div>
        
        <div class="summary-card">
            <h3>成功率</h3>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {success_rate}%"></div>
            </div>
            <div class="number">{success_rate:.1f}%</div>
        </div>
        
        <h2>📋 详细测试结果</h2>
"""
        
        for suite in self.results["test_details"]:
            suite_success_rate = (suite["passed"] / suite["total"] * 100) if suite["total"] > 0 else 0
            
            html_content += f"""
        <div class="test-suite">
            <div class="suite-header">
                <h3>{suite['suite_name']} ({suite['class_name']})</h3>
                <p>通过: {suite['passed']} | 失败: {suite['failed']} | 错误: {suite['errors']} | 成功率: {suite_success_rate:.1f}%</p>
            </div>
            <div class="suite-content">
"""
            
            if suite["failures"]:
                html_content += "<h4>失败详情:</h4><ul>"
                for failure in suite["failures"]:
                    html_content += f"<li class='status-failed'>{failure[:200]}...</li>"
                html_content += "</ul>"
            
            if suite["error_details"]:
                html_content += "<h4>错误详情:</h4><ul>"
                for error in suite["error_details"]:
                    html_content += f"<li class='status-error'>{error[:200]}...</li>"
                html_content += "</ul>"
            
            html_content += """
            </div>
        </div>
"""
        
        html_content += """
    </div>
</body>
</html>
"""
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def _print_summary(self):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("🎯 测试总结")
        print("=" * 60)
        print(f"📊 总测试数: {self.results['total_tests']}")
        print(f"✅ 通过测试: {self.results['passed_tests']}")
        print(f"❌ 失败测试: {self.results['failed_tests']}")
        print(f"⚠️ 错误测试: {self.results['error_tests']}")
        
        if self.results['total_tests'] > 0:
            success_rate = self.results['passed_tests'] / self.results['total_tests'] * 100
            print(f"🎯 成功率: {success_rate:.1f}%")
            
            if success_rate >= 90:
                print("🎉 测试结果优秀！")
            elif success_rate >= 70:
                print("👍 测试结果良好！")
            else:
                print("⚠️ 需要关注失败的测试用例")
        
        print("=" * 60)


def main():
    """主函数"""
    print("🔧 初始化测试环境...")
    
    # 检查minium是否安装
    try:
        import minium
        print("✅ minium框架已安装")
    except ImportError:
        print("❌ minium框架未安装，请先安装: pip install minium")
        return
    
    # 运行测试
    runner = TestRunner()
    results = runner.run_all_tests()
    
    # 清理测试数据
    print("\n🧹 清理测试数据...")
    try:
        # 这里可以添加清理逻辑
        print("✅ 测试数据清理完成")
    except Exception as e:
        print(f"⚠️ 清理测试数据失败: {e}")
    
    print("\n🏁 测试完成！")
    
    return results


if __name__ == '__main__':
    main()
