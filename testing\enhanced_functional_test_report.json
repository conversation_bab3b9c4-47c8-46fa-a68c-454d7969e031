{"test_time": "2025-07-30T11:14:11.996342", "duration_seconds": 63.210943, "framework_tests": {"total": 3, "passed": 3, "failed": 0, "errors": 0}, "functional_tests": {"total": 13, "passed": 8, "failed": 5, "critical_failed": 4}, "overall_success": false, "detailed_results": [{"test_name": "页面元素总数", "success": true, "details": "找到49个页面元素", "critical": false, "timestamp": "2025-07-30T11:14:35.993050"}, {"test_name": "菜品容器识别", "success": true, "details": "识别出21个菜品容器", "critical": true, "timestamp": "2025-07-30T11:14:36.174417"}, {"test_name": "菜品内容", "success": true, "details": "菜品信息: 全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小", "critical": false, "timestamp": "2025-07-30T11:14:36.180177"}, {"test_name": "菜品交互按钮", "success": false, "details": "未找到交互按钮", "critical": true, "timestamp": "2025-07-30T11:14:36.197708"}, {"test_name": "购物车相关元素", "success": true, "details": "找到14个购物车相关元素", "critical": true, "timestamp": "2025-07-30T11:14:36.443904"}, {"test_name": "购物车跳转-1", "success": true, "details": "点击'全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘'，当前页面: /pages/order/index", "critical": true, "timestamp": "2025-07-30T11:14:40.535425"}, {"test_name": "购物车跳转-2", "success": true, "details": "点击'全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘'，当前页面: /pages/order/index", "critical": true, "timestamp": "2025-07-30T11:14:44.608055"}, {"test_name": "订单确认信息", "success": false, "details": "未找到订单确认信息", "critical": false, "timestamp": "2025-07-30T11:14:44.885687"}, {"test_name": "我的菜品入口", "success": true, "details": "找到4个菜品管理入口", "critical": true, "timestamp": "2025-07-30T11:14:48.489241"}, {"test_name": "我的菜品页面", "success": false, "details": "未跳转到菜品页面，当前: /pages/mine/index", "critical": true, "timestamp": "2025-07-30T11:14:52.551745"}, {"test_name": "新增菜品直接入口", "success": false, "details": "个人中心未找到新增菜品直接入口", "critical": true, "timestamp": "2025-07-30T11:14:52.691697"}, {"test_name": "用户关联入口", "success": true, "details": "找到4个关联入口", "critical": true, "timestamp": "2025-07-30T11:15:11.040363"}, {"test_name": "用户关联页面", "success": false, "details": "未跳转到关联页面: /pages/mine/index", "critical": true, "timestamp": "2025-07-30T11:15:15.112841"}], "test_focus": {"cart_functionality": "购物车功能深度测试", "dish_management": "菜品管理完整测试", "user_connection": "用户关联增强测试", "order_flow": "订餐流程完整验证"}}