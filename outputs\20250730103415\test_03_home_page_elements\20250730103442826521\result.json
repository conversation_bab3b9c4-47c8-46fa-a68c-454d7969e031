{"case_name": "test_03_home_page_elements", "run_time": "20250730 10:34:42", "test_type": "CompleteMiniProgramTest", "case_doc": "测试首页元素", "success": true, "failures": "", "errors": "", "start_timestamp": 1753842882.9387808, "is_failure": false, "is_error": false, "module": "E:.wx-nan.complete_miniprogram_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/home/<USER>", "path": "images\\setup.png", "ts": 1753842882, "datetime": "2025-07-30 10:34:42", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753842902, "datetime": "2025-07-30 10:35:02", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753842902.221739, "appId": "", "appName": "", "source": {"code": ["    def test_03_home_page_elements(self):\n", "        \"\"\"测试首页元素\"\"\"\n", "        print(\"🧪 测试首页元素\")\n", "        \n", "        try:\n", "            # 导航到首页\n", "            self.app.switch_tab(\"/pages/home/<USER>\")\n", "            time.sleep(3)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 检查页面元素\n", "            elements_to_check = [\n", "                \"view\",  # 基本视图元素\n", "                \"text\",  # 文本元素\n", "                \"image\", # 图片元素\n", "            ]\n", "            \n", "            for element_type in elements_to_check:\n", "                try:\n", "                    elements = current_page.get_elements(element_type)\n", "                    print(f\"📋 找到 {len(elements)} 个 {element_type} 元素\")\n", "                except Exception as e:\n", "                    print(f\"⚠️ 查找{element_type}元素失败: {e}\")\n", "            \n", "            # 检查页面文本内容\n", "            try:\n", "                page_text = current_page.get_element(\"page\").inner_text\n", "                if \"楠楠家厨\" in page_text or \"欢迎\" in page_text:\n", "                    print(\"✅ 找到页面标题文本\")\n", "                else:\n", "                    print(\"⚠️ 未找到预期的页面文本\")\n", "            except Exception as e:\n", "                print(f\"⚠️ 获取页面文本失败: {e}\")\n", "            \n", "            self.take_screenshot(\"03_home_elements\")\n", "            \n", "        except Exception as e:\n", "            print(f\"⚠️ 首页元素测试失败: {e}\")\n", "        \n", "        print(\"✅ 首页元素测试完成\")\n"], "start": 106}, "filename": "result.json"}