{"case_name": "test_04_data_persistence_and_state", "run_time": "20250730 10:43:23", "test_type": "StableFunctionalTest", "case_doc": "测试数据持久化和状态管理", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843403.5945163, "is_failure": false, "is_error": false, "module": "E:.wx-nan.stable_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/detail/index", "path": "images\\setup.png", "ts": 1753843403, "datetime": "2025-07-30 10:43:23", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753843410, "datetime": "2025-07-30 10:43:30", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843410.1002789, "appId": "", "appName": "", "source": {"code": ["    def test_04_data_persistence_and_state(self):\n", "        \"\"\"测试数据持久化和状态管理\"\"\"\n", "        print(\"🧪 测试数据持久化和状态管理\")\n", "        \n", "        try:\n", "            # 测试页面间切换的状态保持\n", "            pages_to_test = [\n", "                \"/pages/home/<USER>\",\n", "                \"/pages/order/index\", \n", "                \"/pages/mine/index\",\n", "                \"/pages/home/<USER>\"\n", "            ]\n", "            \n", "            for i, page_path in enumerate(pages_to_test):\n", "                self.app.switch_tab(page_path)\n", "                time.sleep(1)\n", "                \n", "                current_page = self.app.get_current_page()\n", "                expected_page = page_path.split('/')[-2]\n", "                \n", "                if expected_page in current_page.path:\n", "                    self.log_result(f\"页面切换-{i+1}\", True, f\"成功切换到{expected_page}\")\n", "                else:\n", "                    self.log_result(f\"页面切换-{i+1}\", False, f\"切换失败，当前在{current_page.path}\")\n", "            \n", "            # 测试应用状态\n", "            try:\n", "                # 获取应用信息\n", "                app_info = self.app.evaluate(\"getCurrentPages().length\")\n", "                self.log_result(\"应用状态-页面栈\", True, f\"页面栈深度: {app_info}\")\n", "                \n", "                # 测试存储\n", "                storage_test = self.app.evaluate(\"wx.getStorageInfoSync()\")\n", "                if storage_test:\n", "                    self.log_result(\"应用状态-存储\", True, f\"存储信息: {storage_test}\")\n", "                else:\n", "                    self.log_result(\"应用状态-存储\", False, \"无法获取存储信息\")\n", "                    \n", "            except Exception as e:\n", "                self.log_result(\"应用状态检查\", False, f\"检查失败: {e}\")\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"状态管理测试\", False, f\"异常: {e}\")\n"], "start": 229}, "filename": "result.json"}