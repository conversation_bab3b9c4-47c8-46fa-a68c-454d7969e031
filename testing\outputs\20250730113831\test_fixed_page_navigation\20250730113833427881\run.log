[I 2025-07-30 11:38:33,429 minium minitest#432 _miniSetUp] =========Current case: test_fixed_page_navigation=========
[I 2025-07-30 11:38:33,429 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.fixed_navigation_test, case info: FixedNavigationTest.test_fixed_page_navigation
[I 2025-07-30 11:38:33,430 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:38:33,430 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:38:33,430 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:38:33,431 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:38:33,432 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:38:33,453 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:38:33,455 minium.Conn2368 connection#427 _safely_send] SEND > [1421658412368]{"id":"6dbab9b3-a48f-4deb-9315-e33233ddde89","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:38:33,558 minium.Conn2368 connection#660 __on_message] RECV < [1421658412368]{"id":"6dbab9b3-a48f-4deb-9315-e33233ddde89","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:38:33,560 minium minitest#487 _miniSetUp] =========case: test_fixed_page_navigation start=========
[I 2025-07-30 11:38:34,560 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:38:34,561 minium.Conn2368 connection#427 _safely_send] SEND > [1421658412368]{"id":"46094f5f-bafc-44a7-9047-64671ff00ee5","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:38:34,562 minium.Conn2368 connection#660 __on_message] RECV < [1421658412368]{"id":"46094f5f-bafc-44a7-9047-64671ff00ee5","result":{"pageId":16,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:38:34,563 minium.App7072 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:38:34,564 minium.Conn2368 connection#427 _safely_send] SEND > [1421658412368]{"id":"f5960858-5c51-4d22-8c19-87ced53d5e31","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:38:34,568 minium.Conn2368 connection#660 __on_message] RECV < [1421658412368]{"id":"f5960858-5c51-4d22-8c19-87ced53d5e31","result":{"result":{"pageId":16,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:38:34,569 minium.Conn2368 connection#427 _safely_send] ASYNC_SEND > [1421658412368]{"id":"ad907940-20f4-466d-b5f5-3f12b064d2dc","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:38:34,570 minium.Conn2368 connection#660 __on_message] RECV < [1421658412368]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:38:34,574 minium.Conn2368 connection#660 __on_message] RECV < [1421658412368]{"id":"ad907940-20f4-466d-b5f5-3f12b064d2dc","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:38:34,574 minium.Conn2368 connection#704 _handle_async_msg] received async msg: ad907940-20f4-466d-b5f5-3f12b064d2dc
[E 2025-07-30 11:38:49,576 minium minitest#793 _callTestMethod] test exception
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\framework\minitest.py", line 789, in _callTestMethod
    method()
    ~~~~~~^^
  File "E:\wx-nan\testing\fixed_navigation_test.py", line 75, in test_fixed_page_navigation
    self.app.switch_tab("/pages/mine/index")
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\minium_log.py", line 148, in wrapper
    result = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\utils\utils.py", line 90, in wrapper
    ret = func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\app.py", line 991, in switch_tab
    page = self._change_route_async(
        "switchTab", url, is_wait_url_change, wait_route_done_time
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\app.py", line 1187, in _change_route_async
    if cmd.get_route_result(wait_route_done_time):
       ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\app.py", line 148, in get_route_result
    if get_result(self._route_changed, timeout):
       ~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\utils\utils.py", line 475, in get_result
    return fut.result(timeout)
           ~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\concurrent\futures\_base.py", line 451, in result
    self._condition.wait(timeout)
    ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\threading.py", line 363, in wait
    gotit = waiter.acquire(True, timeout)
KeyboardInterrupt
[I 2025-07-30 11:38:49,585 minium minitest#796 _callTestMethod] end test method
