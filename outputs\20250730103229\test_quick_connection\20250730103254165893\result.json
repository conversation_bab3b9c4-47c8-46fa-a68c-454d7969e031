{"case_name": "test_quick_connection", "run_time": "20250730 10:32:54", "test_type": "QuickTest", "case_doc": null, "success": true, "failures": "", "errors": "", "start_timestamp": 1753842774.3690174, "is_failure": false, "is_error": false, "module": "E:.wx-nan.advanced_connection_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/home/<USER>", "path": "images\\setup.png", "ts": 1753842774, "datetime": "2025-07-30 10:32:54", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753842774, "datetime": "2025-07-30 10:32:54", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753842774.6252635, "appId": "", "appName": "", "source": {"code": ["            def test_quick_connection(self):\n", "                print(\"🧪 快速连接测试\")\n", "                \n", "                # 获取应用实例\n", "                app = self.mini.app\n", "                print(f\"📱 应用实例: {app}\")\n", "                \n", "                # 获取当前页面\n", "                page = app.get_current_page()\n", "                print(f\"📄 当前页面: {page.path}\")\n", "                \n", "                # 简单截图\n", "                app.screen_shot(\"quick_test.png\")\n", "                print(\"📸 截图完成: quick_test.png\")\n", "                \n", "                return True\n"], "start": 115}, "filename": "result.json"}