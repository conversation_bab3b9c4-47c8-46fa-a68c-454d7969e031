# 后台服务器配置设置指南

## 🎯 概述

本文档说明如何配置后台管理系统的服务器地址，参照小程序的配置方式，支持快速切换不同环境。

## 📁 配置文件结构

```
src/config/
├── server.js          # 服务器配置文件
└── env.js             # 环境配置文件
```

## ⚙️ 服务器配置

### 1. 服务器配置文件 (`src/config/server.js`)

```javascript
// 当前使用的服务器（可以快速切换）
const CURRENT_SERVER = 'test'; // dev | test | production

// 服务器配置
const SERVER_CONFIG = {
  // 开发环境
  dev: {
    name: '开发环境',
    baseURL: 'http://localhost:3000/api',
    timeout: 10000,
    description: '本地开发服务器'
  },
  
  // 测试环境
  test: {
    name: '测试环境',
    baseURL: 'http://test-api.nannan-kitchen.com/api',
    timeout: 15000,
    description: '测试服务器'
  },
  
  // 生产环境
  production: {
    name: '生产环境',
    baseURL: 'https://api.nannan-kitchen.com/api',
    timeout: 20000,
    description: '生产服务器'
  }
};
```

### 2. 快速切换环境

只需要修改 `CURRENT_SERVER` 变量即可：

```javascript
// 切换到开发环境
const CURRENT_SERVER = 'dev';

// 切换到测试环境
const CURRENT_SERVER = 'test';

// 切换到生产环境
const CURRENT_SERVER = 'production';
```

## 🔧 配置功能

### 1. 自动配置 Axios

系统会自动根据当前环境配置 Axios 实例：

```javascript
// src/utils/request.js
import {getCurrentServerConfig} from '@/config/server';

const serverConfig = getCurrentServerConfig();

const request = axios.create({
  baseURL: serverConfig.baseURL,
  timeout: serverConfig.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
});
```

### 2. 启动时显示环境信息

应用启动时会在控制台显示当前环境信息：

```
🚀 楠楠厨房后台管理系统 v2.0.0
📡 当前环境: 测试环境
🔗 API地址: http://test-api.nannan-kitchen.com/api
🛠️ 调试模式: 开启
```

### 3. 系统配置页面

访问 `/system/config` 可以查看完整的系统配置信息，包括：

- 服务器配置
- 应用信息
- 功能开关
- 存储配置
- 上传配置
- 安全配置

### 4. 服务器状态监控

系统包含服务器状态监控组件，实时显示：

- 服务器在线状态
- 网络延迟
- 连接质量

## 🚀 使用方法

### 1. 开发环境设置

```javascript
// src/config/server.js
const CURRENT_SERVER = 'dev';
```

适用于：
- 本地开发
- 调试功能
- 测试新特性

### 2. 测试环境设置

```javascript
// src/config/server.js
const CURRENT_SERVER = 'test';
```

适用于：
- 功能测试
- 集成测试
- 预发布验证

### 3. 生产环境设置

```javascript
// src/config/server.js
const CURRENT_SERVER = 'production';
```

适用于：
- 正式发布
- 生产部署
- 用户使用

## 🔍 配置验证

### 1. 检查当前配置

```javascript
import { getCurrentServerConfig } from '@/config/server';

const config = getCurrentServerConfig();
console.log('当前服务器配置:', config);
```

### 2. 环境检测

```javascript
import { isDevelopment, isTest, isProduction } from '@/config/server';

if (isDevelopment()) {
  console.log('当前为开发环境');
}

if (isTest()) {
  console.log('当前为测试环境');
}

if (isProduction()) {
  console.log('当前为生产环境');
}
```

## 🛠️ 高级配置

### 1. 添加新环境

```javascript
// 在 SERVER_CONFIG 中添加新环境
const SERVER_CONFIG = {
  // ... 现有配置
  
  // 新的预发布环境
  staging: {
    name: '预发布环境',
    baseURL: 'https://staging-api.nannan-kitchen.com/api',
    timeout: 18000,
    description: '预发布服务器'
  }
};
```

### 2. 自定义配置项

```javascript
// 扩展服务器配置
const SERVER_CONFIG = {
  dev: {
    name: '开发环境',
    baseURL: 'http://localhost:3000/api',
    timeout: 10000,
    description: '本地开发服务器',
    // 自定义配置
    enableMock: true,
    logLevel: 'debug',
    features: {
      hotReload: true,
      devtools: true
    }
  }
};
```

### 3. 动态配置

```javascript
// 根据域名自动检测环境
function detectEnvironment() {
  const hostname = window.location.hostname;
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'dev';
  } else if (hostname.includes('test')) {
    return 'test';
  } else {
    return 'production';
  }
}

const CURRENT_SERVER = detectEnvironment();
```

## 📋 登录页面优化

### 已删除的测试账号

为了安全考虑，已删除登录页面的测试账号显示：

- ❌ 删除了测试账号区域
- ❌ 删除了自动填充功能
- ❌ 删除了相关样式代码

### 安全改进

- ✅ 移除了硬编码的测试账号
- ✅ 提高了登录安全性
- ✅ 减少了潜在的安全风险

## 🔒 安全注意事项

### 1. 生产环境配置

- 确保生产环境使用 HTTPS
- 设置合适的超时时间
- 禁用调试功能

### 2. 敏感信息保护

- 不要在代码中硬编码密码
- 使用环境变量存储敏感配置
- 定期更新 API 密钥

### 3. 网络安全

- 配置 CORS 策略
- 使用安全的请求头
- 实施请求频率限制

## 📊 监控和日志

### 1. 请求监控

系统会自动记录：
- API 请求状态
- 响应时间
- 错误信息

### 2. 性能监控

- 网络延迟检测
- 服务器响应时间
- 连接稳定性

### 3. 错误处理

- 自动重试机制
- 友好的错误提示
- 详细的错误日志

## 🎯 最佳实践

### 1. 开发流程

1. 开发阶段使用 `dev` 环境
2. 功能完成后切换到 `test` 环境测试
3. 测试通过后部署到 `production` 环境

### 2. 配置管理

1. 使用版本控制管理配置文件
2. 为不同环境创建不同的配置分支
3. 定期备份重要配置

### 3. 部署策略

1. 自动化部署脚本
2. 环境配置验证
3. 回滚机制

---

通过这套配置系统，您可以轻松管理不同环境的服务器配置，确保开发、测试和生产环境的顺畅切换。
