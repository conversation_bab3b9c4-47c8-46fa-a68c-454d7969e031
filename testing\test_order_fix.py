#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试订单创建修复效果
"""

import requests
import json
from datetime import datetime

def test_order_creation_fix():
    """测试订单创建修复效果"""
    api_base = "http://8.148.231.104:3000/api"
    
    print("🧪 测试订单创建修复效果")
    print("=" * 50)
    
    # 登录获取token
    print("🔐 用户登录...")
    try:
        login_response = requests.post(
            f"{api_base}/auth/login",
            json={
                "username": "13800000001",
                "password": "test123456"
            },
            timeout=10
        )
        
        if login_response.status_code == 200:
            result = login_response.json()
            token = result["data"]["token"]
            user_id = result["data"]["user"]["id"]
            print(f"✅ 登录成功: ID={user_id}")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return
    
    # 获取可用菜品
    print("\n🍽️ 获取可用菜品...")
    try:
        dishes_response = requests.get(
            f"{api_base}/dishes",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if dishes_response.status_code == 200:
            dishes_data = dishes_response.json()
            dishes = dishes_data.get("data", [])
            if dishes:
                dish_id = dishes[0]["id"]
                dish_name = dishes[0]["name"]
                print(f"✅ 获取菜品成功: ID={dish_id}, 名称={dish_name}")
            else:
                print("❌ 没有可用菜品")
                return
        else:
            print(f"❌ 获取菜品失败: {dishes_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 获取菜品异常: {e}")
        return
    
    # 测试用例
    test_cases = [
        {
            "name": "基础订单创建",
            "data": {
                "items": [
                    {
                        "dishId": dish_id,
                        "count": 1
                    }
                ]
            }
        },
        {
            "name": "带备注的订单",
            "data": {
                "items": [
                    {
                        "dishId": dish_id,
                        "count": 2
                    }
                ],
                "remark": "修复测试订单"
            }
        },
        {
            "name": "带时间的订单",
            "data": {
                "items": [
                    {
                        "dishId": dish_id,
                        "count": 1
                    }
                ],
                "remark": "带时间的修复测试",
                "diningTime": datetime.now().isoformat()
            }
        },
        {
            "name": "无效菜品ID测试",
            "data": {
                "items": [
                    {
                        "dishId": "invalid_dish_id",
                        "count": 1
                    }
                ]
            },
            "expect_error": True
        }
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {test_case['name']}")
        
        try:
            response = requests.post(
                f"{api_base}/orders",
                json=test_case["data"],
                headers={
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                },
                timeout=15
            )
            
            print(f"   状态码: {response.status_code}")
            
            if test_case.get("expect_error"):
                if response.status_code == 400:
                    print(f"   ✅ 预期错误正确返回")
                    try:
                        error_detail = response.json()
                        print(f"   错误信息: {error_detail.get('message', 'Unknown')}")
                    except:
                        pass
                    success_count += 1
                else:
                    print(f"   ❌ 应该返回400错误，但返回了{response.status_code}")
            else:
                if response.status_code == 201:
                    result = response.json()
                    order_id = result["data"]["order"]["id"]
                    print(f"   ✅ 订单创建成功: ID={order_id}")
                    success_count += 1
                else:
                    print(f"   ❌ 订单创建失败")
                    try:
                        error_detail = response.json()
                        print(f"   错误详情: {error_detail}")
                    except:
                        print(f"   响应内容: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    # 测试总结
    print(f"\n" + "=" * 50)
    print(f"🎯 修复效果测试总结")
    print(f"=" * 50)
    print(f"✅ 成功: {success_count}/{total_count}")
    print(f"📊 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 订单创建API修复成功！")
    elif success_count > 0:
        print("👍 订单创建API有所改善！")
    else:
        print("❌ 订单创建API仍需进一步修复")
    
    return success_count == total_count

if __name__ == "__main__":
    success = test_order_creation_fix()
    
    if success:
        print("\n🎉 所有测试通过，修复成功！")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
