[I 2025-07-30 10:35:07,026 minium minitest#432 _miniSetUp] =========Current case: test_05_mine_page_navigation=========
[I 2025-07-30 10:35:07,026 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_05_mine_page_navigation
[I 2025-07-30 10:35:07,027 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:07,027 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:07,027 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:07,027 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:07,027 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:07,028 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:07,029 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"60d5a184-3a26-43b8-87b7-c97341371e51","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:07,147 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"60d5a184-3a26-43b8-87b7-c97341371e51","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:07,151 minium minitest#487 _miniSetUp] =========case: test_05_mine_page_navigation start=========
[I 2025-07-30 10:35:08,155 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:08,157 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"481c1eb0-2404-4afa-95b1-19bac677955e","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:08,162 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:08,420 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842908410,"webviewId":3,"routeEventId":"3_1753842908195","renderer":"webview"},1753842908413]}}
[I 2025-07-30 10:35:08,425 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842908410, 'webviewId': 3, 'routeEventId': '3_1753842908195', 'renderer': 'webview'}, 1753842908413]}
[D 2025-07-30 10:35:08,438 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"481c1eb0-2404-4afa-95b1-19bac677955e","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:08,441 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 481c1eb0-2404-4afa-95b1-19bac677955e
[D 2025-07-30 10:35:11,444 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"bf9129b0-64e5-4fe0-b963-eb288e458b41","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:11,451 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"bf9129b0-64e5-4fe0-b963-eb288e458b41","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:35:11,453 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:11,455 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"820b3a7c-0b81-4478-8734-41894516b318","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:11,461 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"820b3a7c-0b81-4478-8734-41894516b318","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:11,469 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:35:11,469 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"5cf08652-1511-4ec5-a72f-f43656e0ca06","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,590 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"5cf08652-1511-4ec5-a72f-f43656e0ca06","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,596 minium page#716 _get_elements_by_css] try to get elements: view[bindtap]
[D 2025-07-30 10:35:11,596 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"c45c5267-3b08-4967-9dcd-53acf258782f","method":"Page.getElements","params":{"selector":"view[bindtap]","pageId":3}}
[D 2025-07-30 10:35:11,605 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"c45c5267-3b08-4967-9dcd-53acf258782f","result":{"elements":[]}}
[W 2025-07-30 10:35:11,605 minium page#747 _get_elements_by_css] Could not found any element 'view[bindtap]' you need
[D 2025-07-30 10:35:11,606 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"8bd431bc-bccf-4624-9545-1d3506f4be94","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,755 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"8bd431bc-bccf-4624-9545-1d3506f4be94","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,756 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:11,757 minium minitest#799 _miniTearDown] =========Current case Down: test_05_mine_page_navigation=========
[I 2025-07-30 10:35:11,757 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:11,757 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"8fbe92df-3f41-4ece-b39c-3bddce278a27","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,905 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"8fbe92df-3f41-4ece-b39c-3bddce278a27","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,906 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:11,907 minium basenative#63 wrapper] call BaseNative.get_start_up end 
