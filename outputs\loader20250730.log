[I 2025-07-30 10:12:26,663 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:12:26,665 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:12:26,665 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:12:26,666 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'E:\\wx-nan\\outputs', 'enable_app_log': False, 'enable_network_panel': False, 'project_path': None, 'dev_tool_path': None, 'test_port': 9420, 'mock_native_modal': {}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'framework_capture': True, 'error_capture': False, 'assert_capture': True, 'create_time': '**************'}
[I 2025-07-30 10:12:26,668 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:12:26,825 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:12:26,826 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:12:26,826 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:12:26,826 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[W 2025-07-30 10:12:26,826 minium wx_minium#783 launch_dev_tool] Can not find `project_path` in config, that means you must open dev tool by automation way first
[W 2025-07-30 10:12:26,827 minium wx_minium#787 launch_dev_tool] If you are not running command like [cli auto --project /path/to/project --auto-port 9420], you may config `project_path` or run this command first
[I 2025-07-30 10:12:26,827 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:31,031 minium.Conn0256 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:31,046 minium.Conn0256 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:31,061 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:31,069 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:35,195 minium.Conn7120 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:35,211 minium.Conn7120 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:35,291 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:35,298 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:39,490 minium.Conn0320 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:39,522 minium.Conn0320 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:39,539 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[E 2025-07-30 10:12:39,542 minium wx_minium#797 launch_dev_tool] three times try to connect Dev tool has all fail ..., restart now...
[I 2025-07-30 10:15:09,211 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:15:09,213 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:15:09,214 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:15:09,215 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'E:\\wx-nan\\outputs', 'enable_app_log': False, 'enable_network_panel': False, 'project_path': None, 'dev_tool_path': None, 'test_port': 9420, 'mock_native_modal': {}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'framework_capture': True, 'error_capture': False, 'assert_capture': True, 'create_time': '**************'}
[I 2025-07-30 10:15:09,216 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:15:09,432 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:15:09,432 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:15:09,434 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:15:09,448 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[W 2025-07-30 10:15:09,449 minium wx_minium#783 launch_dev_tool] Can not find `project_path` in config, that means you must open dev tool by automation way first
[W 2025-07-30 10:15:09,498 minium wx_minium#787 launch_dev_tool] If you are not running command like [cli auto --project /path/to/project --auto-port 9420], you may config `project_path` or run this command first
[I 2025-07-30 10:15:09,498 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:13,655 minium.Conn9680 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:13,670 minium.Conn9680 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:13,686 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:13,762 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:17,907 minium.Conn2928 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:17,922 minium.Conn2928 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:17,953 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:17,969 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:22,101 minium.Conn6128 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:22,117 minium.Conn6128 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:22,147 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[E 2025-07-30 10:15:22,195 minium wx_minium#797 launch_dev_tool] three times try to connect Dev tool has all fail ..., restart now...
[I 2025-07-30 10:26:11,253 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:26:11,254 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:26:11,254 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:26:11,255 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': '', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:26:11,255 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:26:11,352 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:26:11,352 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:26:11,353 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
