[I 2025-07-30 10:12:26,663 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:12:26,665 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:12:26,665 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:12:26,666 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'E:\\wx-nan\\outputs', 'enable_app_log': False, 'enable_network_panel': False, 'project_path': None, 'dev_tool_path': None, 'test_port': 9420, 'mock_native_modal': {}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'framework_capture': True, 'error_capture': False, 'assert_capture': True, 'create_time': '**************'}
[I 2025-07-30 10:12:26,668 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:12:26,825 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:12:26,826 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:12:26,826 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:12:26,826 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[W 2025-07-30 10:12:26,826 minium wx_minium#783 launch_dev_tool] Can not find `project_path` in config, that means you must open dev tool by automation way first
[W 2025-07-30 10:12:26,827 minium wx_minium#787 launch_dev_tool] If you are not running command like [cli auto --project /path/to/project --auto-port 9420], you may config `project_path` or run this command first
[I 2025-07-30 10:12:26,827 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:31,031 minium.Conn0256 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:31,046 minium.Conn0256 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:31,061 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:31,069 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:35,195 minium.Conn7120 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:35,211 minium.Conn7120 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:35,291 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:35,298 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:39,490 minium.Conn0320 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:39,522 minium.Conn0320 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:39,539 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[E 2025-07-30 10:12:39,542 minium wx_minium#797 launch_dev_tool] three times try to connect Dev tool has all fail ..., restart now...
[I 2025-07-30 10:15:09,211 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:15:09,213 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:15:09,214 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:15:09,215 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'E:\\wx-nan\\outputs', 'enable_app_log': False, 'enable_network_panel': False, 'project_path': None, 'dev_tool_path': None, 'test_port': 9420, 'mock_native_modal': {}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'framework_capture': True, 'error_capture': False, 'assert_capture': True, 'create_time': '**************'}
[I 2025-07-30 10:15:09,216 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:15:09,432 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:15:09,432 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:15:09,434 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:15:09,448 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[W 2025-07-30 10:15:09,449 minium wx_minium#783 launch_dev_tool] Can not find `project_path` in config, that means you must open dev tool by automation way first
[W 2025-07-30 10:15:09,498 minium wx_minium#787 launch_dev_tool] If you are not running command like [cli auto --project /path/to/project --auto-port 9420], you may config `project_path` or run this command first
[I 2025-07-30 10:15:09,498 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:13,655 minium.Conn9680 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:13,670 minium.Conn9680 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:13,686 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:13,762 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:17,907 minium.Conn2928 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:17,922 minium.Conn2928 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:17,953 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:17,969 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:22,101 minium.Conn6128 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:22,117 minium.Conn6128 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:22,147 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[E 2025-07-30 10:15:22,195 minium wx_minium#797 launch_dev_tool] three times try to connect Dev tool has all fail ..., restart now...
[I 2025-07-30 10:26:11,253 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:26:11,254 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:26:11,254 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:26:11,255 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': '', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:26:11,255 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:26:11,352 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:26:11,352 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:26:11,353 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:29:23,520 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:29:23,522 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:29:23,522 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:29:23,522 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'skip', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:30:52,071 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:30:52,072 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:30:52,072 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:30:52,073 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:32:29,800 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:32:29,802 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:32:29,802 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:32:29,802 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:32:29,803 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 10:32:29,804 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 10:32:33,560 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✔ auto

[I 2025-07-30 10:32:33,561 minium minium_object#102 _do_shell] out:

[I 2025-07-30 10:32:43,578 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 10:32:44,249 minium.Conn4976 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 10:32:44,253 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"d60691b3-6792-491a-ac86-8658ff700444","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:32:44,264 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"d60691b3-6792-491a-ac86-8658ff700444","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 10:32:44,267 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"c2ab2a67-41a7-43e0-8e8c-4a5ff42d68e3","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:32:53,097 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"method":"App.initialized","params":{"from":"devtools"}}
[W 2025-07-30 10:32:53,098 minium.Conn4976 connection#330 notify] no observer listening event App.initialized
[D 2025-07-30 10:32:53,104 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"c2ab2a67-41a7-43e0-8e8c-4a5ff42d68e3","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 10:32:53,105 minium.App8000 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 10:32:53,107 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"ac5c9ba5-8366-4760-b75c-74ce24d872e2","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 10:32:53,111 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"ac5c9ba5-8366-4760-b75c-74ce24d872e2","result":{"result":{"injected":false,"isThirdApp":false}}}
[D 2025-07-30 10:32:53,112 minium.Conn4976 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:32:53,112 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"8b68c1cb-44f0-494d-afe7-6b2dd65d719f","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 10:32:53,114 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"8b68c1cb-44f0-494d-afe7-6b2dd65d719f","result":{}}
[D 2025-07-30 10:32:53,115 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"12b0f553-a68b-4ba3-b58f-af9b100d8aa2","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 10:32:53,119 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"12b0f553-a68b-4ba3-b58f-af9b100d8aa2","result":{}}
[D 2025-07-30 10:32:53,119 minium.App8000 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 10:32:53,120 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"b506cb7c-9425-4e97-ad60-b8937179c901","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 10:32:53,126 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"b506cb7c-9425-4e97-ad60-b8937179c901","result":{}}
[D 2025-07-30 10:32:53,126 minium.App8000 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 10:32:53,128 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"94480d0c-a9d5-4d2c-b34f-0cbba76a7350","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 10:32:53,165 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"94480d0c-a9d5-4d2c-b34f-0cbba76a7350","result":{}}
[D 2025-07-30 10:32:53,192 minium.App8000 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 10:32:53,197 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e5cfea2d-d94b-4926-ad2d-dd68b2a2149f","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 10:32:53,214 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e5cfea2d-d94b-4926-ad2d-dd68b2a2149f","result":{"result":true}}
[D 2025-07-30 10:32:53,229 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e2493705-a28a-45d8-8bdf-fe2fed2b8b3f","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 10:32:53,274 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e2493705-a28a-45d8-8bdf-fe2fed2b8b3f","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 10:32:53,275 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"68315b6b-51f1-4493-b282-3618a11ada55","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 10:32:53,278 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"68315b6b-51f1-4493-b282-3618a11ada55","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 10:32:53,278 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e1d03b43-cc7b-47b5-91ac-358d5accef59","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 10:32:53,280 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e1d03b43-cc7b-47b5-91ac-358d5accef59","result":{}}
[D 2025-07-30 10:32:53,280 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"c96cc170-e726-4898-809f-04d2ee58e0e7","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 10:32:53,286 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"c96cc170-e726-4898-809f-04d2ee58e0e7","result":{}}
[D 2025-07-30 10:32:53,288 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"193399db-9fde-4a62-a299-224ec46f8bd2","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 10:32:53,290 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"193399db-9fde-4a62-a299-224ec46f8bd2","result":{}}
[D 2025-07-30 10:32:53,292 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"f71dad89-0244-466f-9236-bf8a9f4061bb","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 10:32:53,294 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"f71dad89-0244-466f-9236-bf8a9f4061bb","result":{}}
[D 2025-07-30 10:32:53,294 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"bba5bab1-f775-4120-8388-d363d7a7fa66","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 10:32:53,296 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"bba5bab1-f775-4120-8388-d363d7a7fa66","result":{}}
[D 2025-07-30 10:32:53,297 minium.App8000 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 10:32:53,298 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"73e4c126-b11a-46e6-a6b5-975364df9b4c","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 10:32:53,311 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"73e4c126-b11a-46e6-a6b5-975364df9b4c","result":{}}
[D 2025-07-30 10:32:53,327 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9f8f9b42-4405-41d3-ba21-c24940cdd604","method":"App.addBinding","params":{"name":"showModal_before_1753842773327"}}
[D 2025-07-30 10:32:53,329 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9f8f9b42-4405-41d3-ba21-c24940cdd604","result":{}}
[D 2025-07-30 10:32:53,330 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"611aa935-1b86-472c-b5c0-a4af8a50ec16","method":"App.addBinding","params":{"name":"showModal_callback_1753842773327"}}
[D 2025-07-30 10:32:53,338 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"611aa935-1b86-472c-b5c0-a4af8a50ec16","result":{}}
[D 2025-07-30 10:32:53,341 minium.App8000 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:32:53,342 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"f2eeba0a-8f89-436b-ad5a-f0ae168dcdbc","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753842773327,after:undefined,callback:showModal_callback_1753842773327},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753842773327,true]}}
[D 2025-07-30 10:32:53,346 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"f2eeba0a-8f89-436b-ad5a-f0ae168dcdbc","result":{}}
[D 2025-07-30 10:32:53,348 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9227c941-2db0-4185-9a59-ca0b183d1156","method":"App.addBinding","params":{"name":"showToast_before_1753842773348"}}
[D 2025-07-30 10:32:53,351 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9227c941-2db0-4185-9a59-ca0b183d1156","result":{}}
[D 2025-07-30 10:32:53,353 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"5beec953-b5f7-4c66-b38c-d0a38f1a89ed","method":"App.addBinding","params":{"name":"showToast_callback_1753842773348"}}
[D 2025-07-30 10:32:53,356 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"5beec953-b5f7-4c66-b38c-d0a38f1a89ed","result":{}}
[D 2025-07-30 10:32:53,358 minium.App8000 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:32:53,359 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"7cbac137-654a-4ce7-939b-62ba862ff7c7","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753842773348,after:undefined,callback:showToast_callback_1753842773348},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753842773348,true]}}
[D 2025-07-30 10:32:53,362 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"7cbac137-654a-4ce7-939b-62ba862ff7c7","result":{}}
[I 2025-07-30 10:32:53,362 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x0000020E12A430E0>, whether should relaunch: False
[D 2025-07-30 10:32:53,363 minium.Conn4976 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:32:53,363 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"373f435b-75ca-4e4b-853a-172680324bfd","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:32:53,365 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"373f435b-75ca-4e4b-853a-172680324bfd","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 10:32:53,365 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 10:32:53,368 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"621829f4-77c1-4cf0-b359-39279ca0f558","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 10:32:53,372 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"621829f4-77c1-4cf0-b359-39279ca0f558","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 10:32:53,372 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 10:32:53,373 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 10:32:53,373 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"2556ebdd-086a-43a6-a7d3-352980e72975","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 10:32:53,780 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"2556ebdd-086a-43a6-a7d3-352980e72975","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 10:32:53,786 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"839d6898-a93d-449d-995f-a0527b0190f1","method":"App.callFunction","params":{"functionDeclaration":"function ideMockModal(){return!!global.__minium__.mock_native_modal||(global.__minium__.auth_setting={},global.__minium__.mock_native_modal_list=[],global.__minium__.handle_mock_modal=function(_,l){let m=[];for(;global.__minium__.mock_native_modal_list.length>0;){let i=global.__minium__.mock_native_modal_list.pop();if(-1===l.indexOf(i.type)||!(_ in i)){m.unshift(i);continue}let o=i[_];if(global.__minium__.mock_native_modal_list.push(...m),\"function\"!=typeof o)throw`${o} is not callable`;return o(),!0}return console.warn(`can't find [${l}][${_}]`),global.__minium__.mock_native_modal_list.push(...m),!1},global.__minium__.get_all_modal=function(){let _=[];global.__minium__.mock_native_modal_list.forEach((l=>{_.push({type:l.type,texts:Object.keys(l).filter((_=>\"type\"!==_))})}))},global.__minium__.handle_mock_native_modal=function(_){return global.__minium__.handle_mock_modal(_,[\"modal\",\"auth\"])},global.__minium__.handle_mock_map_modal=function(_){return global.__minium__.handle_mock_modal(_,[\"map\"])},global.__minium__.handle_mock_auth_modal=function(_){return global.__minium__.handle_mock_modal(_,[\"auth\"])},global.__minium__.mock_native_modal=function(_){_.type=\"modal\",global.__minium__.mock_native_modal_list.push(_)},global.__minium__.mock_map_modal=function(_){_.type=\"map\",global.__minium__.mock_native_modal_list.push(_)},global.__minium__.mock_auth_modal=function(_){_.type=\"auth\",global.__minium__.mock_native_modal_list.push(_)},!1)}","args":[]}}
[D 2025-07-30 10:32:53,794 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"839d6898-a93d-449d-995f-a0527b0190f1","result":{"result":false}}
[D 2025-07-30 10:32:53,797 minium.App8000 app#618 _evaluate_js] evaluate js file mockWxMethod [ALL]
[D 2025-07-30 10:32:53,799 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"91cd971a-440e-43c8-806c-c1b467082fd3","method":"App.callFunction","params":{"functionDeclaration":"function mockWxMethod(o,_){if(!global.__minium__.canMock)throw new Error(\"mock\u65b9\u6cd5\u672a\u5b9e\u73b0\");const n=function miniActionSheet(e){return new Promise((n=>{let t={},o=e.itemList;for(let e=0;e<o.length;e++)t[o[e]]=function(){let t={errMsg:\"showActionSheet:ok\",tapIndex:e};return n(t),t};return t[\"\u53d6\u6d88\"]=function(){let e={errMsg:\"showActionSheet:fail cancel\"};return n(e),e},console.warn(\"[minium] mock wx.showActionSheet, you can use `native.handle_action_sheet` to handle it\"),global.__minium__.mock_native_modal(t)}))};n?global.__minium__.setMock(o,n,_):global.__minium__.delMock(o)}","args":["showActionSheet",null]}}
[D 2025-07-30 10:32:53,805 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"91cd971a-440e-43c8-806c-c1b467082fd3","result":{}}
[D 2025-07-30 10:32:53,806 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"0ff96aa8-930b-4a26-90f1-8256b4746510","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:32:53,810 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"0ff96aa8-930b-4a26-90f1-8256b4746510","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:32:53,810 minium.App8000 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:32:53,811 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9ed2206d-36d4-4953-8558-f63c4e21e71e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:32:53,815 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9ed2206d-36d4-4953-8558-f63c4e21e71e","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:32:53,815 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"db4cef96-1436-41c4-bbfd-71e329aab409","method":"App.callWxMethod","params":{"method":"getSetting","args":[]}}
[D 2025-07-30 10:32:54,141 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"db4cef96-1436-41c4-bbfd-71e329aab409","result":{"result":{"errMsg":"getSetting:ok","authSetting":{"scope.address":true,"scope.invoice":true,"scope.invoiceTitle":true,"scope.userInfo":true}}}}
[D 2025-07-30 10:32:54,143 minium.App8000 app#618 _evaluate_js] evaluate js file mockWxMethod [ALL]
[D 2025-07-30 10:32:54,144 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"b9a6fd0f-41d6-4f58-bdd6-b96c46dfb7b9","method":"App.callFunction","params":{"functionDeclaration":"function mockWxMethod(o,_){if(!global.__minium__.canMock)throw new Error(\"mock\u65b9\u6cd5\u672a\u5b9e\u73b0\");const n=function miniGetLocation(o,n,e){return new Promise((o=>{let t={errMsg:\"chooseLocation:ok\",name:\"\u817e\u8baf\u5fae\u4fe1\u603b\u90e8\",address:\"\u5e7f\u4e1c\u7701\u5e7f\u5dde\u5e02\",latitude:23.12463,longitude:113.36199},i={errMsg:\"chooseLocation:fail cancel\"},c={errMsg:\"chooseLocation:fail auth deny\"},a={};for(let o in t)void 0!==n[o]&&(t[o]=n[o]);for(let o in e)a[o]=Object.assign({},t,e[o]);wx.authorize({scope:\"scope.userLocation\",success:()=>function(){let n={\"\u786e\u5b9a\":function(){return o(t),t}};n[t.name]=function(){return o(t),t};for(let e in a)n[e]=function(){return o(a[e]),a[e]};n[\"\u53d6\u6d88\"]=function(){return o(i),i},global.__minium__.mock_map_modal(n)}(),fail:()=>(o(c),c)}),console.warn(\"[minium] mock wx.chooseLocation, you can use `native.allow_get_location` to handle it\")}))};n?global.__minium__.setMock(o,n,_):global.__minium__.delMock(o)}","args":["chooseLocation",[{},{}]]}}
[D 2025-07-30 10:32:54,146 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"b9a6fd0f-41d6-4f58-bdd6-b96c46dfb7b9","result":{}}
[D 2025-07-30 10:32:54,147 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"000c05df-b483-4568-bcd1-f9b01de1e1af","method":"App.callFunction","params":{"functionDeclaration":"function mockNetwork(e){if(!global.__minium__[`${e}_network_mocked`]){Object.defineProperty(global.__minium__,`${e}_network_mocked`,{value:!0,writable:!1});class s{constructor(e,s,c){this.success=e,this.fail=s,this.callback=c,this.chunkCallback=[],this.headersReceivedCallback=[],this.progressUpdateCallback=[],this.fd=setTimeout((()=>{this.headersReceivedCallback.forEach((e=>{this.success&&e({header:this.success.header})})),this.chunkCallback.forEach((e=>{this.success&&e(this.success.data)})),this.progressUpdateCallback.forEach((e=>{this.success&&e({progress:100,totalBytesWritten:100,totalBytesExpectedToWrite:100})})),c(this.success||this.fail)}))}abort(){clearTimeout(this.fd)}offChunkReceived(e){var s=this.chunkCallback.indexOf(e);-1!==s&&this.chunkCallback.splice(s,1)}offHeadersReceived(e){var s=this.headersReceivedCallback.indexOf(e);-1!==s&&this.headersReceivedCallback.splice(s,1)}onChunkReceived(e){this.chunkCallback.push(e)}onHeadersReceived(e){this.headersReceivedCallback.push(e)}onProgressUpdate(e){this.progressUpdateCallback.push(e)}}global.__minium__[`${e}Task`]=s;const c=new global.__minium__.MockRule(`${e}_network_mock_rule`);global.__minium__.setMock(e,(function(e){const a=(e.method||\"GET\").toUpperCase(),t=e.url,[i,l]=t.split(\"?\"),o=function(e){if(!(e=e.split(\"#\")[0]))return{};const s={},c=e.split(\"&\");for(let e=0;e<c.length;e++){const[a,t]=c[e].split(\"=\");s[a]=decodeURIComponent(t)}return s}(l||\"\");\"GET\"===a&&Object.assign(o,e.data);const r=Object.assign({},e,{url:i,params:o});\"GET\"===a&&Object.assign(r,{data:o});let h=c.search(e);if(!h)if(r.params){const e=Object.assign({},r);e.url=`${r.url}?${Object.keys(r.params).map((e=>`${e}=${r.params[e]}`)).join(\"&\")}`,delete e.params,h=c.search(r,e)}else h=c.search(r);return h?(console.log(\"@@@@rule match\",h),e.__miniumMocked=!0,h.success?new s(h.success,void 0,(s=>{e.success&&e.success(s),e.complete&&e.complete(s)})):h.fail?new s(void 0,h.fail,(s=>{e.fail&&e.fail(s),e.complete&&e.complete(s)})):void 0):this.origin(e)}))}}","args":["request"]}}
[D 2025-07-30 10:32:54,151 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"000c05df-b483-4568-bcd1-f9b01de1e1af","result":{}}
[D 2025-07-30 10:32:54,153 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e6538534-82ef-4522-94b3-72037c5a5b15","method":"App.enableLog","params":{}}
[D 2025-07-30 10:32:54,157 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e6538534-82ef-4522-94b3-72037c5a5b15","result":{}}
[D 2025-07-30 10:32:54,158 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"fbff503e-e6fc-4cf5-8451-54d5f9cffed0","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 10:32:54,160 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"fbff503e-e6fc-4cf5-8451-54d5f9cffed0","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 10:32:54,161 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e1d2b4bd-2dac-40eb-83df-0810289bed49","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:32:54,164 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e1d2b4bd-2dac-40eb-83df-0810289bed49","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 10:32:54,165 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 10:32:54,168 minium minitest#432 _miniSetUp] =========Current case: test_quick_connection=========
[I 2025-07-30 10:32:54,169 minium minitest#435 _miniSetUp] package info: E:.wx-nan.advanced_connection_test, case info: QuickTest.test_quick_connection
[I 2025-07-30 10:32:54,169 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:32:54,169 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:32:54,169 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:32:54,170 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:32:54,170 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:32:54,172 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:32:54,173 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"8108cd55-a64c-4354-ad38-353abc06abeb","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,366 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"8108cd55-a64c-4354-ad38-353abc06abeb","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWV/jm3tl7fln2HkIQQCEEggAijMOxxQxZRETcQRsYRHec3gzPoMLjPyDjDwCjLqIiiLCJIYEQGUBFBSCCEJEA2kryX5b2Xt/TrpdZ7fn/cqurq6up+/V4SE8j9IPVO3751697qvl+f892l0LRdAAAARCQiaUtb2tLey3bIMhISEhL7AgwAEDF8LW1pS1vae9eWvoyEhMS+hRpa+yg245zv0/KlLW1p76HNGHvT6DJhoRISEm92RAOfPYQ6epbRIMlFQuKth6hXsodFjZ9lJLlISBwM2HO6ibAMIoTE0dT2r9pyfr+uAGPKL21pS3uf2tGhoFbyi46PiGO91th0mVb8F+njSEi8qdGKzzImv6bViKk5d
[I 2025-07-30 10:32:54,369 minium minitest#487 _miniSetUp] =========case: test_quick_connection start=========
[I 2025-07-30 10:32:54,369 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:32:54,370 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"29187262-9304-47f3-8aff-97a6c3d908d6","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:32:54,372 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"29187262-9304-47f3-8aff-97a6c3d908d6","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:32:54,373 minium.App8000 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:32:54,374 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9577513a-1e08-4d52-9e16-b3cbf32d7e0c","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:32:54,376 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9577513a-1e08-4d52-9e16-b3cbf32d7e0c","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:32:54,377 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"81f65f54-526b-434d-91ad-16196fcc2952","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,517 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"81f65f54-526b-434d-91ad-16196fcc2952","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgHFWZNv6+p7be7pp9JyE7hCAQQAQVhz1uCCIqg6hsIx+fyzjfiOMyDO4zMvyGERVwVEQRhEHQwIiOgCKCkEAISYBsJLk3ubn35i59e6n1vL8/TlV1dXV13743iUnIeSB13z596tQ51X2eft/nLIWm7QIAACAiEUlb2tKW9n62Q5aRkJCQOBBgAICI4WtpS1va0t6/tvRlJCQkDizU0DpAsRnn/ICWL21pS3sfbcbYYaPLhIVKSEgc7ogGPvsIdfQso0GSi4TEGw9Rr2Qfixo/y0hykZA4ErDvdBNhGUQIiaOh7V+16fx+XQHGlF/a0pb2AbWjQ0HN5BcdHxHHeq2x6TLN+C/Sx5GQOKzRjM8yJr+m2YipM
[I 2025-07-30 10:32:54,536 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:32:54,536 minium minitest#799 _miniTearDown] =========Current case Down: test_quick_connection=========
[I 2025-07-30 10:32:54,538 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:32:54,538 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"30904b09-3e5f-4e93-924e-3578a0dfc97f","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,622 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"30904b09-3e5f-4e93-924e-3578a0dfc97f","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq5e5KbfSEkIQmEIBBABBWHPW4oICqDjoIwMD63eTODIzI83MY3Mr5hZJRlVERRlkHQwIgOoCKCkEAIWchKknuTu+UufXup9Xzvj1NVXV1d3bfvTWIScn6Qul+fPnXqnOo+v/6+31kKTdsFAABARCKStrSlLe0DbIcsIyEhIXEwwAAAEcPX0pa2tKV9YG3py0hISBxcqKF1kGIzzvlBLV/a0pb2ftqMsSNGlwkLlZCQONIRDXz2E+rYWcaCJBcJiTcfol7JfhY1cZaR5CIhcTRg/+kmwjKIEBJHXdu/asP5/boCjCu/tKUt7YNqR4eCGskvOj4ijvda49NlGvFfpI8jIXFEoxGfZVx+TaMRU
[I 2025-07-30 10:32:54,623 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:32:54,624 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:34:15,406 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:34:15,408 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:34:15,408 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:34:15,409 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:34:15,409 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 10:34:15,410 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 10:34:16,815 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 10:34:16,817 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 10:34:16,818 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 10:34:17,310 minium.Conn9680 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 10:34:17,310 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9642798a-d26a-4a80-937e-b7e314fbf932","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:34:17,312 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9642798a-d26a-4a80-937e-b7e314fbf932","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 10:34:17,313 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"96253e35-8eb9-4b01-bca5-cc581ec89494","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:34:17,321 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"96253e35-8eb9-4b01-bca5-cc581ec89494","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 10:34:17,322 minium.App4384 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 10:34:17,323 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"41521ce8-87c6-4d58-848d-442d3dd2c195","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 10:34:17,325 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"41521ce8-87c6-4d58-848d-442d3dd2c195","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 10:34:17,325 minium.Conn9680 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:34:17,326 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c27f1e82-1ff2-46aa-af73-49ea928f4709","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 10:34:17,328 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c27f1e82-1ff2-46aa-af73-49ea928f4709","result":{}}
[D 2025-07-30 10:34:17,329 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e0cbd8cc-6114-46a2-b513-bb3a2b123d1d","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 10:34:17,331 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e0cbd8cc-6114-46a2-b513-bb3a2b123d1d","result":{}}
[D 2025-07-30 10:34:17,331 minium.App4384 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 10:34:17,332 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"00fd547d-d282-4ca9-bbc5-f559e9000bbc","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 10:34:17,337 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"00fd547d-d282-4ca9-bbc5-f559e9000bbc","result":{}}
[D 2025-07-30 10:34:17,338 minium.App4384 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 10:34:17,338 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"f7db0b42-a422-4923-8817-d92e739c92b4","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 10:34:17,342 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f7db0b42-a422-4923-8817-d92e739c92b4","result":{}}
[D 2025-07-30 10:34:17,342 minium.App4384 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 10:34:17,343 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"50810028-a6d6-4a9e-865a-ba95c2a9526e","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 10:34:17,345 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"50810028-a6d6-4a9e-865a-ba95c2a9526e","result":{"result":false}}
[D 2025-07-30 10:34:17,346 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c12c1600-9e59-400d-9d1c-00e5c7bf0895","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 10:34:17,348 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c12c1600-9e59-400d-9d1c-00e5c7bf0895","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 10:34:17,348 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9873cd7f-ecd1-43f6-9cdc-fbbfeaa9d0d0","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 10:34:17,354 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9873cd7f-ecd1-43f6-9cdc-fbbfeaa9d0d0","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 10:34:17,355 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fb496891-37de-4b40-98d7-cdc963adea26","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 10:34:17,358 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fb496891-37de-4b40-98d7-cdc963adea26","result":{}}
[D 2025-07-30 10:34:17,359 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9f86e726-28d7-4b42-8008-d1e410e8b1df","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 10:34:17,361 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9f86e726-28d7-4b42-8008-d1e410e8b1df","result":{}}
[D 2025-07-30 10:34:17,362 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1ba18b64-146d-4451-b360-c7c934685f3a","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 10:34:17,364 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1ba18b64-146d-4451-b360-c7c934685f3a","result":{}}
[D 2025-07-30 10:34:17,367 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"65a8258e-da69-43ba-8d0e-1e302db0bcd4","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 10:34:17,375 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"65a8258e-da69-43ba-8d0e-1e302db0bcd4","result":{}}
[D 2025-07-30 10:34:17,381 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"6b5c5096-40bc-4297-821c-000332948ff0","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 10:34:17,391 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"6b5c5096-40bc-4297-821c-000332948ff0","result":{}}
[D 2025-07-30 10:34:17,392 minium.App4384 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 10:34:17,393 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"35af95a5-3c2f-4d6c-810e-a5dc0e9606fd","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 10:34:17,395 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"35af95a5-3c2f-4d6c-810e-a5dc0e9606fd","result":{"result":true}}
[D 2025-07-30 10:34:17,396 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"134d73f4-470c-428a-b95f-a5f82fba1459","method":"App.addBinding","params":{"name":"showModal_before_1753842857396"}}
[D 2025-07-30 10:34:17,397 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"134d73f4-470c-428a-b95f-a5f82fba1459","result":{}}
[D 2025-07-30 10:34:17,398 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"45f1284f-1af4-480b-9e0e-7b7cefb72ec2","method":"App.addBinding","params":{"name":"showModal_callback_1753842857396"}}
[D 2025-07-30 10:34:17,401 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"45f1284f-1af4-480b-9e0e-7b7cefb72ec2","result":{}}
[D 2025-07-30 10:34:17,401 minium.App4384 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:34:17,402 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b6d3ebb1-d031-4934-a832-806ddeb2b2d5","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753842857396,after:undefined,callback:showModal_callback_1753842857396},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753842857396,true]}}
[D 2025-07-30 10:34:17,405 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b6d3ebb1-d031-4934-a832-806ddeb2b2d5","result":{}}
[D 2025-07-30 10:34:17,406 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1f8f243f-a4ff-41ef-b55e-cbf94e787751","method":"App.addBinding","params":{"name":"showToast_before_1753842857406"}}
[D 2025-07-30 10:34:17,409 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1f8f243f-a4ff-41ef-b55e-cbf94e787751","result":{}}
[D 2025-07-30 10:34:17,414 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"162e2a9c-6818-4d58-930c-c1985abb03ff","method":"App.addBinding","params":{"name":"showToast_callback_1753842857406"}}
[D 2025-07-30 10:34:17,418 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"162e2a9c-6818-4d58-930c-c1985abb03ff","result":{}}
[D 2025-07-30 10:34:17,420 minium.App4384 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:34:17,420 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3a0ce6b6-ac90-446b-8d90-a8b16d04d427","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753842857406,after:undefined,callback:showToast_callback_1753842857406},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753842857406,true]}}
[D 2025-07-30 10:34:17,423 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3a0ce6b6-ac90-446b-8d90-a8b16d04d427","result":{}}
[I 2025-07-30 10:34:17,424 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x000001ECD4A3A660>, whether should relaunch: True
[D 2025-07-30 10:34:17,425 minium.Conn9680 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:34:17,425 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8908f0e3-5c7b-43d2-87b1-ba38ca338d2b","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:34:17,428 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8908f0e3-5c7b-43d2-87b1-ba38ca338d2b","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 10:34:17,428 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 10:34:17,428 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"936d85cd-4cf6-48e1-af03-7d99fc5cd85e","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 10:34:17,430 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"936d85cd-4cf6-48e1-af03-7d99fc5cd85e","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 10:34:17,430 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 10:34:17,430 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 10:34:17,431 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"f6715e3d-8764-4be9-acf6-3e06f84c9b6f","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 10:34:17,437 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f6715e3d-8764-4be9-acf6-3e06f84c9b6f","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 10:34:17,440 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"cfc8d872-b308-4a66-81de-003a3c00c68c","method":"App.enableLog","params":{}}
[D 2025-07-30 10:34:17,442 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"cfc8d872-b308-4a66-81de-003a3c00c68c","result":{}}
[D 2025-07-30 10:34:17,444 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"044ab45e-159d-42af-88a1-c6b028a602ac","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 10:34:17,447 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"044ab45e-159d-42af-88a1-c6b028a602ac","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 10:34:17,449 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"d210f37b-9b8b-47c7-a7ae-eada141a5022","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:34:17,463 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"d210f37b-9b8b-47c7-a7ae-eada141a5022","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 10:34:17,463 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 10:34:17,472 minium minitest#432 _miniSetUp] =========Current case: test_01_app_launch_and_basic_info=========
[I 2025-07-30 10:34:17,472 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_01_app_launch_and_basic_info
[I 2025-07-30 10:34:17,473 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:17,473 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:17,473 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:17,474 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:17,474 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:17,489 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:17,489 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3951dc8c-38a8-481b-b30f-fa78c80eb42c","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:17,590 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3951dc8c-38a8-481b-b30f-fa78c80eb42c","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXeAHMWZNv6+1WnSzgZJqxxRBiEMCDAWZ+MjywZjgsHmMGeTDj5/xuH33TlwPg4cf2eO7ziwTXDA2NiEw4AFZ/ABtjEmSUgIBVBC0q602l1tmJ3Qser7o7p7anp6ZmdXkpXqAfW+U1NdXdUz9cz7PhUaTdsFAABARMaYtKUtbWnvYztkGQkJCYn9AQIAiBi+lra0pS3tfWtLX0ZCQmL/Qg2t/RSbUUr3a/nSlra099ImhBwyukxYqISExKEOMfDZS6jDZxkOklwkJA4/iF7JXhY1epaR5CIhcSRg7+lGYBlECImjru1fteH8fl0BRpRf2tKW9n61xaGgRvLzjo+II73WyHSZRvwX6eNISBzSaMRnGZFf02jEV
[I 2025-07-30 10:34:17,592 minium minitest#487 _miniSetUp] =========case: test_01_app_launch_and_basic_info start=========
[I 2025-07-30 10:34:18,593 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 10:34:18,802 minium minitest#897 capture] capture assertIsNotNone-success.png
[D 2025-07-30 10:34:18,802 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"61e870da-065e-4466-abf8-a2dd46775918","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:18,879 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"61e870da-065e-4466-abf8-a2dd46775918","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq5t++S5GZPICQhBELYQQQVZY8bCgjKICoIA8/nNm9mcGMY3MY3MrxhRGUZFVGUZRA0MIIjqIhsCYSQBbKR5SZ3y1369lLr+d4fp6q6urq6b9+bxARyfpC6X58+deqc6j6//r7fWQpN2wUAAEBEIpK2tKUt7b1shywjISEhsS/AAAARw9fSlra0pb13benLSEhI7FuoobWPYjPO+T4tX9rSlvYe2oyxN40uExYqISHxZkc08NlDqGNnGQuSXCQk3nqIeiV7WNTEWUaSi4TEwYA9p5sIyyBCSBwNbf+qTef36wowrvzSlra096kdHQpqJr/o+Ig43muNT5dpxn+RPo6ExJsazfgs4/Jrmo2YG
[D 2025-07-30 10:34:18,881 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"12291ce3-67cf-4803-a6cc-817cf2237153","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:18,883 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"12291ce3-67cf-4803-a6cc-817cf2237153","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:18,885 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:18,886 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b25b0aa1-d3ea-4b47-899d-c50d1f30cb56","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:18,888 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b25b0aa1-d3ea-4b47-899d-c50d1f30cb56","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:34:18,890 minium minitest#897 capture] capture assertIsNotNone-success_103418890827.png
[D 2025-07-30 10:34:18,891 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"7e6b3491-6ef6-412f-9231-36b79a9538de","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:18,963 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"7e6b3491-6ef6-412f-9231-36b79a9538de","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6+5261dFcvSTp7AiEJSSCEHURQUfa4oYCoDKKCIHx+brOIyzAMbuNvhuE3jKgsoyKKsgyCBkZwBBWRLYEQkgDZSNKddLo7vVTXctfzfn+ce2/dunWrurqTkEDOA7n91qlzzz3nVp2n3vc5y0XTdgEAABCRiKQtbWlLey/bIctISEhI7AswAEDE8LW0pS1tae9dW/oyEhIS+xZqaO2j2Ixzvk/Ll7a0pb2HNmPsTaPLhIVKSEi82RENfPYQ6thZxoIkFwmJtx6iXskeFjVxlpHkIiFxMGDP6SbCMogQEkdD279q0/n9ugKMK7+0pS3tfWpHh4KayS86PiKO91rj02Wa8V+kjyMh8aZGMz7LuPyaZ
[D 2025-07-30 10:34:18,988 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"8ee7fc8d-669c-4e27-95d9-ddb264b410e1","method":"App.callFunction","params":{"functionDeclaration":"wx.getSystemInfoSync()","args":[]}}
[D 2025-07-30 10:34:18,989 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"ea1730fb-be61-4eb4-84d9-bec5a31a4c36","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,004 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8ee7fc8d-669c-4e27-95d9-ddb264b410e1","error":{"message":"Arg string terminates parameters early"}}
[E 2025-07-30 10:34:19,004 minium.Conn9680 connection#668 __on_message] [8ee7fc8d-669c-4e27-95d9-ddb264b410e1]: Arg string terminates parameters early
[I 2025-07-30 10:34:19,005 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 8ee7fc8d-669c-4e27-95d9-ddb264b410e1
[D 2025-07-30 10:34:19,087 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"ea1730fb-be61-4eb4-84d9-bec5a31a4c36","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6+5661dPWSpLMnJCQhCYSwBRBBRdnjhgKCMogKwsDn5/p9M7iMMriNv5HhNwyoLKMiirIMggRGcAQVkS2BEJIA2UjSnXS6O71U13LXc74/zr23Tt26VV3dSUhCzgO5/dapc88951adp973OctFy/EAAAAQkTEmbWlLW9p72Y5YRkJCQmJfgAAAIkavpS1taUt779rSl5GQkNi3UCNrH8VmlNJ9Wr60pS3tPbQJIQeNLhMVKiEhcbBDDHz2EOroWUaDJBcJibcfRK9kD4saP8tIcpGQOBSw53QjsAwiRMTR0A6u2nT+oK4AY8ovbWlLe5/a4lBQM/l5x0fEsV5rbLpMM/6L9HEkJA5qNOOzjMmva
[I 2025-07-30 10:34:19,089 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:34:19,089 minium minitest#799 _miniTearDown] =========Current case Down: test_01_app_launch_and_basic_info=========
[I 2025-07-30 10:34:19,089 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:34:19,090 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"10cce0aa-fb5f-4589-a758-93c39eecdc7d","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,164 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"10cce0aa-fb5f-4589-a758-93c39eecdc7d","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXeAHMWV93vVadLOBkmrnAOSQIgkwFjYgMlywibaHMY2GM6cP4fz3Rmfw3E4nP3dYb7j4GzC2cbY2ARjMIIzYINtjEkSCKEASkjalTZpw+yEjlXfH9XdU9PTMzu7kpCE6gfqfVNTXV3VM/Wb934VGk3bBQAAQETGmLSlLW1p72M7ZBkJCQmJ/QECAIgYvpa2tKUt7X1rS19GQkJi/0INrf0Um1FK92v50pa2tPfSJoQcMrpMWKiEhMShDjHw2UuoI2cZCZJcJCTeeRC9kr0sauwsI8lFQuJwwN7TjcAyiBASR13bv2rD+f26Aowqv7SlLe39aotDQY3k5x0fEUd7rdHpMo34L9LHkZA4pNGIzzIqv6bRiKk+d
[I 2025-07-30 10:34:19,167 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:34:19,167 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:34:19,210 minium minitest#432 _miniSetUp] =========Current case: test_02_navigate_all_main_pages=========
[I 2025-07-30 10:34:19,211 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_02_navigate_all_main_pages
[I 2025-07-30 10:34:19,211 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:19,211 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:19,212 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:19,212 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:19,212 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:19,213 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:19,213 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"04ea56e9-38c4-4884-bf62-4ad021363733","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,301 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"04ea56e9-38c4-4884-bf62-4ad021363733","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAnEWZP/489V59Tc+RO5kc5CIJhHAFEEFBuaMicgjIIioIC8t67oHKui54rL9d5bcIKseiiKIQFgEDK7AGFTkkgYSQg5wkmckxM5mjp4/3rPr+Ue/7dvXbb/f0TBKSkPpA3nm6ut56q97u+vTzfOp40bRdAAAARGSMSVva0pb2PrZDlpGQkJDYHyAAgIjha2lLW9rS3re29GUkJCT2L9TQ2k+xGaV0v5YvbWlLey9tQsgho8uEhUpISBzqEAOfvYQ6dJahIMlFQuK9B9Er2cuiRs4yklwkJA4H7D3dCCyDCCFx1LX9qzac368rwLDyS1va0t6vtjgU1Eh+3vERcbjXGp4u04j/In0cCYlDGo34LMPyaxqNm
[I 2025-07-30 10:34:19,304 minium minitest#487 _miniSetUp] =========case: test_02_navigate_all_main_pages start=========
[I 2025-07-30 10:34:20,306 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:34:20,307 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"eb696679-b195-4a08-9178-974ab1751002","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:20,350 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:20,352 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"eb696679-b195-4a08-9178-974ab1751002","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:20,353 minium.Conn9680 connection#704 _handle_async_msg] received async msg: eb696679-b195-4a08-9178-974ab1751002
[D 2025-07-30 10:34:35,313 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842875310,"webviewId":3,"routeEventId":"3_1753842874825","renderer":"webview"},1753842875311]}}
[D 2025-07-30 10:34:35,314 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:34:35,315 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842875310, 'webviewId': 3, 'routeEventId': '3_1753842874825', 'renderer': 'webview'}, 1753842875311]}
[W 2025-07-30 10:34:35,315 minium.App4384 app#1013 switch_tab] Switch tab(/pages/home/<USER>/pages/mine/index)
[D 2025-07-30 10:34:35,316 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:34:35,421 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842875418,"webviewId":2,"routeEventId":"2_1753842875314","renderer":"webview"},1753842875419]}}
[D 2025-07-30 10:34:35,422 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:34:35,422 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842875418, 'webviewId': 2, 'routeEventId': '2_1753842875314', 'renderer': 'webview'}, 1753842875419]}
[D 2025-07-30 10:34:35,423 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:34:37,316 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fbad7f58-546f-4df4-b2c3-e063777a9aa0","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:37,318 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fbad7f58-546f-4df4-b2c3-e063777a9aa0","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:37,319 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:37,319 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fedd1d89-0dba-46c9-8493-d244f4b9b388","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:37,322 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fedd1d89-0dba-46c9-8493-d244f4b9b388","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:37,323 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"cee572a4-eea6-4c33-a509-86e46faf6bba","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:37,395 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"cee572a4-eea6-4c33-a509-86e46faf6bba","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6ec9daunrJvoeQHUIQCCAGFYc9biggKqOOgjAyjOtvZlDR4cP9U8ZvGFBZRkUUZBFZAiM6gIgsQgIhJAGykaQ7S3enl+pa7nbO+/vj3Hvr1q1b1dWdhE7IeQK33zp17rnn3Krz1Ps+Z7nEcjwAAABCCCJKW9rSlvZ+tkOWkZCQkDgQoABACAlfS1va0pb2/rWlLyMhIXFgoYbWAYrNOOcHtHxpS1va+2hTSg8ZXSYsVEJC4lBHNPDZR6jDZxkOklwkJN56iHol+1jU6FlGkouExOGAfaebCMsQAiFxNLT9qzad368rwIjyS1va0j6gdnQoqJn8ouMTQkZ6rZHpMs34L9LHkZA4pNGMzzIiv6bZi
[D 2025-07-30 10:34:37,426 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"001a7371-6428-4512-90c7-5b16a1496dd5","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:34:37,427 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:34:37,824 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,827 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,831 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,850 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,882 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,884 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,885 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,886 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,895 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🖼️ 开始预加载 2 张菜品图片"]}}
[D 2025-07-30 10:34:37,896 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🖼️ 开始预加载 2 张菜品图片"]}}
[D 2025-07-30 10:34:37,907 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,909 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,914 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,915 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,927 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,927 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,928 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,929 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,943 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,944 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,951 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,954 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:38,028 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,030 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,039 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,041 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,046 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,047 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,051 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,060 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,292 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842878275,"webviewId":5,"routeEventId":"5_1753842877723","renderer":"webview"},1753842878279]}}
[D 2025-07-30 10:34:38,294 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:34:38,295 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842878275, 'webviewId': 5, 'routeEventId': '5_1753842877723', 'renderer': 'webview'}, 1753842878279]}
[D 2025-07-30 10:34:38,296 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:34:38,297 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"001a7371-6428-4512-90c7-5b16a1496dd5","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:38,297 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 001a7371-6428-4512-90c7-5b16a1496dd5
[D 2025-07-30 10:34:40,299 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1e7a69f0-e2d8-4e24-9ece-35da7c71bce8","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:40,303 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1e7a69f0-e2d8-4e24-9ece-35da7c71bce8","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:34:40,304 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:40,305 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"625f23c7-7a2e-4554-b76b-eeee125a631a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:40,308 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"625f23c7-7a2e-4554-b76b-eeee125a631a","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:40,309 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"870a2e9d-273f-4149-9453-e16171e1b7a3","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:40,389 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"870a2e9d-273f-4149-9453-e16171e1b7a3","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXm8ZVV1J77W2vsMd3rzVFUUUBSCSoEMEY0TnQiKIr+0SUw0BjQdkeAv0e60RdpfNGow7S9CD7ETacRWxCmJSfx0EA0G/QXtJE5QSFFiFVQVVcWb53enM+y91u+Pc+599431qnivxvP9VN23z3D32Xffe77nu9Zee20MIgMAAICIIpKV11LOkCHD2oFNlsmQIUOGjQABACI2t7Py6uUMGTIcKzItkyFDho0FNUunglI4XcoZMmRYOzItkyFDho0FHf2UDBkyZHgeyFgmQ4YMG4sWlmn1O2Tl1csZMmRYMzK/TIYMGTYWmcWUIUOGjUXGMhkyZNhY6JYyAkhWXlt5eSQTnVpfEUApUkREmEXcZDhNISLMYpmt5
[D 2025-07-30 10:34:40,392 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"34c24b2d-fbb8-4e05-b6d7-800d2d94a488","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:34:40,393 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:34:40,626 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842880620,"webviewId":3,"routeEventId":"3_1753842880425","renderer":"webview"},1753842880621]}}
[D 2025-07-30 10:34:40,627 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"34c24b2d-fbb8-4e05-b6d7-800d2d94a488","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:40,628 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842880620, 'webviewId': 3, 'routeEventId': '3_1753842880425', 'renderer': 'webview'}, 1753842880621]}
[I 2025-07-30 10:34:40,628 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 34c24b2d-fbb8-4e05-b6d7-800d2d94a488
[D 2025-07-30 10:34:40,905 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842880902,"webviewId":2,"routeEventId":"2_1753842880689","renderer":"webview"},1753842880903]}}
[I 2025-07-30 10:34:40,907 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842880902, 'webviewId': 2, 'routeEventId': '2_1753842880689', 'renderer': 'webview'}, 1753842880903]}
[D 2025-07-30 10:34:42,630 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"6bdbee72-8272-4481-ab03-61112283db18","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:42,633 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"6bdbee72-8272-4481-ab03-61112283db18","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:42,635 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:42,636 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e9df6139-6091-4c4f-a34d-c5252e32c173","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:42,638 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e9df6139-6091-4c4f-a34d-c5252e32c173","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:42,638 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1cb5f109-a622-4ad1-bfdc-da5b81e8fc64","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,711 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1cb5f109-a622-4ad1-bfdc-da5b81e8fc64","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//dbfc35rtw3hCSEQAgCAcSwissdL+QQFW8QVtYVXfe3iyu6LLoeu7LusrDKsSqiKIcIEljRFVREEBIIIQmQiyTv5Xjv5R3z5uirqn5/VHdPTU/PvHkviUlIfSD9vlNTXV3VM/WZ7/dTR6Pt+gAAAIjIOVe2spWt7H1sRyyjoKCgsD9AAAARo9fKVraylb1vbeXLKCgo7F/okbWfYjPG2H4tX9nKVvZe2oSQQ0aXiQpVUFA41CEHPnsJffQso0GRi4LCGw+yV7KXRY2fZRS5KCgcDth7upFYBhEi4mhqB1dtOX9QV4Ax5Ve2spW9X215KKiV/KLjI+JYrzU2XaYV/0X5OAoKhzRa8VnG5Ne0GjE15
[I 2025-07-30 10:34:42,713 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:34:42,713 minium minitest#799 _miniTearDown] =========Current case Down: test_02_navigate_all_main_pages=========
[I 2025-07-30 10:34:42,714 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:34:42,714 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8e7fabc4-8cef-4d67-8d2e-c728e908d998","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,791 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8e7fabc4-8cef-4d67-8d2e-c728e908d998","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgHFWZNv6+p7be7pp9JyF7CEEgBBFUHPa4IciiiIwDwsjnJ+r4KY7LMLiPMv6GARVwVEQRhEHQwIiMgCKLkEAISYBsZLlZ7r25S99eaj3v749TVV1dXd23701iEnIeSN23T586dU51n6ff9zlLoWm7AAAAiEhE0pa2tKW9n+2QZSQkJCQOBBgAIGL4WtrSlra0968tfRkJCYkDCzW0DlBsxjk/oOVLW9rS3kebMXbY6DJhoRISEoc7ooHPPkIdPstwkOQiIfHmQ9Qr2ceiRs8yklwkJI4E7DvdRFgGEULiaGj7V206v19XgBHll7a0pX1A7ehQUDP5RcdHxJFea2S6TDP+i/RxJCQOazTjs4zIr2k2YmrMH
[I 2025-07-30 10:34:42,793 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:34:42,793 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:34:42,836 minium minitest#432 _miniSetUp] =========Current case: test_03_home_page_elements=========
[I 2025-07-30 10:34:42,836 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_03_home_page_elements
[I 2025-07-30 10:34:42,837 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:42,837 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:42,837 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:42,837 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:42,838 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:42,839 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:42,839 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e1ed5e10-07d5-41f6-848b-213617cac0bc","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,907 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e1ed5e10-07d5-41f6-848b-213617cac0bc","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq7a5KbPYGQhCQQgkAIIjjisMcNQUBFdJRt5PlkdGYUR2V4uD9lfMOACjgqoijCIGBgREZQkUVIIIQkQDay3OTm3pu79O2l1vO9P05VdXV1dd++N4lJyPlB6n59+tSpc6r7/Pr7fmcpNG0XAAAAEYlI2tKWtrT3sR2yjISEhMT+AAMARAxfS1va0pb2vrWlLyMhIbF/oYbWforNOOf7tXxpS1vae2kzxg4ZXSYsVEJC4lBHNPDZS6ijZxkNklwkJN58iHole1nU+FlGkouExOGAvaebCMsgQkgcDW3/qk3n9+sKMKb80pa2tPerHR0Kaia/6PiIONZrjU2XacZ/kT6OhMQhjWZ8ljH5Nc1GT
[I 2025-07-30 10:34:42,938 minium minitest#487 _miniSetUp] =========case: test_03_home_page_elements start=========
[D 2025-07-30 10:34:43,176 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:43,177 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[I 2025-07-30 10:34:43,939 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:34:43,940 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"88e16322-bfc7-450a-a9f9-b03c33e96a4c","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:43,943 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:43,948 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"88e16322-bfc7-450a-a9f9-b03c33e96a4c","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:43,949 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 88e16322-bfc7-450a-a9f9-b03c33e96a4c
[D 2025-07-30 10:34:48,486 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:48,486 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:58,969 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"218c72de-f4ee-4f7e-b7d5-37b47c505427","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:58,971 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"218c72de-f4ee-4f7e-b7d5-37b47c505427","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:58,971 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:58,971 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"6494d1c0-661c-43b6-bdec-96e26dfd4df9","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:58,973 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"6494d1c0-661c-43b6-bdec-96e26dfd4df9","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:01,974 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e1b85a8a-dff7-42cd-a497-f1d115c1d46d","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:01,977 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e1b85a8a-dff7-42cd-a497-f1d115c1d46d","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:01,978 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:01,978 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fe5a35dc-0c0f-4b61-9a07-49fe403f4ecb","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:01,980 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fe5a35dc-0c0f-4b61-9a07-49fe403f4ecb","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:01,981 minium page#716 _get_elements_by_css] try to get elements: view
[D 2025-07-30 10:35:01,981 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"ce6e5ded-1038-4a4c-9ee4-84a50dba647d","method":"Page.getElements","params":{"selector":"view","pageId":2}}
[D 2025-07-30 10:35:01,989 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"ce6e5ded-1038-4a4c-9ee4-84a50dba647d","result":{"elements":[{"elementId":"fd02b6bf-ef7b-4b78-ab45-9811dbe58dfc","tagName":"view"},{"elementId":"ad45d734-385f-46db-bdda-2a72792a34ed","tagName":"view"},{"elementId":"309568af-fe3e-4a77-b99f-8477b66e66a6","tagName":"view"},{"elementId":"b0c94023-6875-4aff-9e74-13477fd70b46","tagName":"view"},{"elementId":"d9c25d80-26ab-4061-8c04-cd02fabbc1e4","tagName":"view"},{"elementId":"a446a70b-8b0a-474b-afa5-1fe16c817b8b","tagName":"view"},{"elementId":"d0dec377-86
[I 2025-07-30 10:35:01,990 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A3BE00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A8D6D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A8D950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A33E10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A33820>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD3AD8950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A0B790>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A0B570>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A89850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A89F50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A477A0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EB8500>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A6B5B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A6BAF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A76F70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A1A810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A1A8D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A828E0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A82D00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA53B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA59F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA58B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA56D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA51D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5630>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4EB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5A90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5B30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5BD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5C70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5D10>]
[I 2025-07-30 10:35:01,991 minium page#716 _get_elements_by_css] try to get elements: text
[D 2025-07-30 10:35:01,991 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b6e3c3a1-7d80-4859-9d6e-aefc02475aba","method":"Page.getElements","params":{"selector":"text","pageId":2}}
[D 2025-07-30 10:35:01,995 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b6e3c3a1-7d80-4859-9d6e-aefc02475aba","result":{"elements":[{"elementId":"d04d2b00-2ccf-4a31-8999-cadca4aa04c5","tagName":"text"},{"elementId":"9e180d92-a547-497c-90e3-891366461a4e","tagName":"text"},{"elementId":"40f9ac82-afea-44fd-bf2e-a5e3faf83e59","tagName":"text"},{"elementId":"377f48cc-9940-47f3-8173-9dce1e36a44f","tagName":"text"},{"elementId":"4b3c1247-1453-4bc8-9b7c-f9860ac90b36","tagName":"text"}]}}
[I 2025-07-30 10:35:01,995 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5DB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA60D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5EF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA62B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA6170>]
[I 2025-07-30 10:35:01,996 minium page#716 _get_elements_by_css] try to get elements: image
[D 2025-07-30 10:35:01,996 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e1124f7d-f859-489e-90d7-d53779331844","method":"Page.getElements","params":{"selector":"image","pageId":2}}
[D 2025-07-30 10:35:01,999 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e1124f7d-f859-489e-90d7-d53779331844","result":{"elements":[{"elementId":"a364fda7-f1ea-4be9-b7bc-8cdf7c84f690","tagName":"image"},{"elementId":"d4d1a591-bdfb-4543-beb6-b6581139b13d","tagName":"image"},{"elementId":"0cc8f2d8-a82c-459f-91dc-50b7dfbf1a75","tagName":"image"}]}}
[I 2025-07-30 10:35:02,001 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA51D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5630>]
[I 2025-07-30 10:35:02,002 minium page#716 _get_elements_by_css] try to get elements: page
[D 2025-07-30 10:35:02,003 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1e5c7180-5fd2-4d5d-a17e-2b7443e9cafd","method":"Page.getElements","params":{"selector":"page","pageId":2}}
[D 2025-07-30 10:35:02,007 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1e5c7180-5fd2-4d5d-a17e-2b7443e9cafd","result":{"elements":[{"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","tagName":"body","nodeId":"d143550e"}]}}
[I 2025-07-30 10:35:02,007 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.CustomElement object at 0x000001ECD4A3BE00>]
[D 2025-07-30 10:35:02,008 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fe650d17-6a22-48a1-ad95-9ff62a12f257","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","pageId":2,"nodeId":"d143550e"}}
[D 2025-07-30 10:35:02,011 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fe650d17-6a22-48a1-ad95-9ff62a12f257","result":{"properties":["伦少，欢迎\n今日菜单已开放点菜，快来选你喜欢的菜吧～\n小李点了菜品：小菜\n推荐菜单\n热门菜品推荐\n去点菜 \n红烧肉\n小菜\n留言\n（暂无留言）\n更多 \n暂无留言\n点击开始第一条留言"]}}
[D 2025-07-30 10:35:02,011 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"f7e68235-a3e6-46c4-a653-545fecad63cc","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,101 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f7e68235-a3e6-46c4-a653-545fecad63cc","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXnAHEWdPv75VPUxx/vOe+S+7xNCEAgggoJyR0U5IiiLiKAoX1c89rvLquu64nr8VuS7LOiKeCCKcixyr6CAFwYhISGEBHKR5H1zvO+b95yjr6r6/VHdPT09PfPO+yYhgdQT6PeZmurqqp6pZz71VHU3Wo4HAACAiEIIxRVXXPEDzEOVUVBQUDgYIACAiOFrxRVXXPEDyw9WLBMGSwoKCm8WRAXiAIIkHmCkHABEAIhIzOGjpoorrviwPOy/YV8+MOXvZyyjYhYFhbc89jPG0Uaxj1IWBYUjCtEuPwrFGZnKNKIvSoMUFN7UqK8jsoOPSGvKvgxEd0viQohaeUR0LDdcOYorrvjhzEUNYyaWp/EyG/VlEiOUW
[I 2025-07-30 10:35:02,103 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:02,104 minium minitest#799 _miniTearDown] =========Current case Down: test_03_home_page_elements=========
[I 2025-07-30 10:35:02,104 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:02,105 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"562c151b-ab8e-4686-84a0-a02d087671b7","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,184 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"562c151b-ab8e-4686-84a0-a02d087671b7","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//db1ccc7807ct/3CSHhCEEEFYRwxAsICMIiKgjKz5Vld3+r67Wsoqu/XWSXhVXxRBRFEEGOFRHwQiIEEkISIBc5Xo73Xt4x781MX1X1+6O6e3p6eubNe0lIIPUJ9PtMTXV1Vc/UZ771qeputBwPAAAAEYUQiiuuuOIHmYcqo6CgoHAoQAAAEcPXiiuuuOIHlx+qWCYMlhQUFN4siArEQQRJPMBwOQCIABCRmCNHTRVXXPEhedh/w758cMo/wFhGxSwKCm95HGCMo41gH6UsCgpHFaJdfgSKMzyVaURflAYpKLypUV9HZAcfltaUfRmI7pbEhRC18ojoWG6ochRXXPEjmYsaxkwsT+NlNurLJEYot
[I 2025-07-30 10:35:02,186 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:02,186 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:35:02,228 minium minitest#432 _miniSetUp] =========Current case: test_04_order_page_functionality=========
[I 2025-07-30 10:35:02,228 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_04_order_page_functionality
[I 2025-07-30 10:35:02,228 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:02,229 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:02,229 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:02,229 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:02,229 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:02,230 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:02,231 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"0e479a64-7780-4a4a-a529-a0f6f1dc9d07","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,325 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"0e479a64-7780-4a4a-a529-a0f6f1dc9d07","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXeAHMWZNv6+VR0m7M4G5SyUEQiRBBgL23BkOWGyjTHGYDhzfMbhvjO+s30c9jl8Z86/4+Bsg882xsYmHAYMnAEDThgMAoSQBCghaVdhd7VhdkKnqvr9Ud09PT09s7MrCQlUj6D3mZrq6qqeqWfeeqq6Gy3HAwAAQEQhhOKKK674XuahyigoKCjsCxAAQMTwteKKK6743uX7KpYJgyUFBYW3C6ICsRdBEg8wWg4AIgBEJObAUVPFFVd8RB7237Av753y9zCWUTGLgsI7HnsY42hj2Ecpi4LCQYVolx+D4oxOZZrRF6VBCgpvazTWEdnBR6U1FV8GorslcSFEvTwiOpYbqRzFFVf8QOaijjETy9N8mc36MokRS
[I 2025-07-30 10:35:02,327 minium minitest#487 _miniSetUp] =========case: test_04_order_page_functionality start=========
[I 2025-07-30 10:35:03,328 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:03,329 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"5e3156cc-52a7-48a6-bd37-da34223d0f24","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:03,330 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:03,492 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:03,495 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:03,508 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:03,510 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:03,524 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:03,525 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:03,526 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:03,526 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:03,562 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842903557,"webviewId":5,"routeEventId":"5_1753842903353","renderer":"webview"},1753842903558]}}
[D 2025-07-30 10:35:03,563 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5e3156cc-52a7-48a6-bd37-da34223d0f24","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:03,564 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842903557, 'webviewId': 5, 'routeEventId': '5_1753842903353', 'renderer': 'webview'}, 1753842903558]}
[I 2025-07-30 10:35:03,564 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 5e3156cc-52a7-48a6-bd37-da34223d0f24
[D 2025-07-30 10:35:06,565 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9ca79f2b-12db-4e3c-aca4-81ab375f8cc4","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:06,569 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9ca79f2b-12db-4e3c-aca4-81ab375f8cc4","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:35:06,569 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:06,570 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"80e4da19-88e9-4530-a237-a17c29eb4458","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:06,572 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"80e4da19-88e9-4530-a237-a17c29eb4458","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:06,575 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:35:06,576 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5cfbe52e-7d58-47e2-905a-90b21e4cce75","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:06,701 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5cfbe52e-7d58-47e2-905a-90b21e4cce75","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:06,704 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:35:06,704 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5d6c96d6-9662-4b7d-a77b-07c156f8ec36","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":5}}
[D 2025-07-30 10:35:06,710 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5d6c96d6-9662-4b7d-a77b-07c156f8ec36","result":{"elements":[]}}
[W 2025-07-30 10:35:06,711 minium page#747 _get_elements_by_css] Could not found any element 'view[bindtap], button' you need
[D 2025-07-30 10:35:06,711 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5acaaf3c-7669-4a66-9de4-7c565bd58de5","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:06,882 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5acaaf3c-7669-4a66-9de4-7c565bd58de5","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:06,888 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:06,888 minium minitest#799 _miniTearDown] =========Current case Down: test_04_order_page_functionality=========
[I 2025-07-30 10:35:06,889 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:06,890 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"98eca09b-55c4-4b0b-97c7-4092099a6a43","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:07,016 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"98eca09b-55c4-4b0b-97c7-4092099a6a43","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:07,020 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:07,021 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:35:07,026 minium minitest#432 _miniSetUp] =========Current case: test_05_mine_page_navigation=========
[I 2025-07-30 10:35:07,026 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_05_mine_page_navigation
[I 2025-07-30 10:35:07,027 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:07,027 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:07,027 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:07,027 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:07,027 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:07,028 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:07,029 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"60d5a184-3a26-43b8-87b7-c97341371e51","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:07,147 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"60d5a184-3a26-43b8-87b7-c97341371e51","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:07,151 minium minitest#487 _miniSetUp] =========case: test_05_mine_page_navigation start=========
[I 2025-07-30 10:35:08,155 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:08,157 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"481c1eb0-2404-4afa-95b1-19bac677955e","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:08,162 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:08,420 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842908410,"webviewId":3,"routeEventId":"3_1753842908195","renderer":"webview"},1753842908413]}}
[I 2025-07-30 10:35:08,425 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842908410, 'webviewId': 3, 'routeEventId': '3_1753842908195', 'renderer': 'webview'}, 1753842908413]}
[D 2025-07-30 10:35:08,438 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"481c1eb0-2404-4afa-95b1-19bac677955e","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:08,441 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 481c1eb0-2404-4afa-95b1-19bac677955e
[D 2025-07-30 10:35:11,444 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"bf9129b0-64e5-4fe0-b963-eb288e458b41","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:11,451 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"bf9129b0-64e5-4fe0-b963-eb288e458b41","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:35:11,453 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:11,455 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"820b3a7c-0b81-4478-8734-41894516b318","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:11,461 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"820b3a7c-0b81-4478-8734-41894516b318","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:11,469 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:35:11,469 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5cf08652-1511-4ec5-a72f-f43656e0ca06","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,590 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5cf08652-1511-4ec5-a72f-f43656e0ca06","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,596 minium page#716 _get_elements_by_css] try to get elements: view[bindtap]
[D 2025-07-30 10:35:11,596 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c45c5267-3b08-4967-9dcd-53acf258782f","method":"Page.getElements","params":{"selector":"view[bindtap]","pageId":3}}
[D 2025-07-30 10:35:11,605 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c45c5267-3b08-4967-9dcd-53acf258782f","result":{"elements":[]}}
[W 2025-07-30 10:35:11,605 minium page#747 _get_elements_by_css] Could not found any element 'view[bindtap]' you need
[D 2025-07-30 10:35:11,606 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8bd431bc-bccf-4624-9545-1d3506f4be94","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,755 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8bd431bc-bccf-4624-9545-1d3506f4be94","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,756 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:11,757 minium minitest#799 _miniTearDown] =========Current case Down: test_05_mine_page_navigation=========
[I 2025-07-30 10:35:11,757 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:11,757 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8fbe92df-3f41-4ece-b39c-3bddce278a27","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,905 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8fbe92df-3f41-4ece-b39c-3bddce278a27","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,906 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:11,907 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:35:11,911 minium minitest#432 _miniSetUp] =========Current case: test_06_app_performance_and_stability=========
[I 2025-07-30 10:35:11,911 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_06_app_performance_and_stability
[I 2025-07-30 10:35:11,912 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:11,912 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:11,912 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:11,913 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:11,913 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:11,914 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:11,914 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"139cf7b4-6003-44e2-b1dc-428360f171d1","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:12,004 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"139cf7b4-6003-44e2-b1dc-428360f171d1","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:12,036 minium minitest#487 _miniSetUp] =========case: test_06_app_performance_and_stability start=========
[I 2025-07-30 10:35:13,037 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:13,038 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"f75cce4b-c65d-4d88-a4d9-bc4b523e0687","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:13,041 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:13,262 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842913259,"webviewId":2,"routeEventId":"2_1753842913060","renderer":"webview"},1753842913260]}}
[I 2025-07-30 10:35:13,264 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842913259, 'webviewId': 2, 'routeEventId': '2_1753842913060', 'renderer': 'webview'}, 1753842913260]}
[D 2025-07-30 10:35:13,268 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f75cce4b-c65d-4d88-a4d9-bc4b523e0687","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:13,269 minium.Conn9680 connection#704 _handle_async_msg] received async msg: f75cce4b-c65d-4d88-a4d9-bc4b523e0687
[D 2025-07-30 10:35:13,772 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"a0dad50a-12d1-4e7a-8b90-e02e83a10a45","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:13,777 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"a0dad50a-12d1-4e7a-8b90-e02e83a10a45","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:13,778 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:13,779 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"49fde259-fa64-4705-b11a-de2e5ce5c5c7","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:13,783 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"49fde259-fa64-4705-b11a-de2e5ce5c5c7","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:13,789 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"524ed504-f378-44e5-ab0c-61a023871133","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:13,791 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:13,962 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:13,977 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:13,994 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:14,005 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:14,010 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:14,011 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:14,014 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:14,015 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:14,091 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842914023,"webviewId":5,"routeEventId":"5_1753842913809","renderer":"webview"},1753842914077]}}
[I 2025-07-30 10:35:14,096 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842914023, 'webviewId': 5, 'routeEventId': '5_1753842913809', 'renderer': 'webview'}, 1753842914077]}
[D 2025-07-30 10:35:14,118 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"524ed504-f378-44e5-ab0c-61a023871133","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:14,120 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 524ed504-f378-44e5-ab0c-61a023871133
[D 2025-07-30 10:35:14,622 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"03cae24d-cb2e-4295-83af-e3b203cb1327","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:14,627 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"03cae24d-cb2e-4295-83af-e3b203cb1327","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:35:14,629 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:14,630 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"4cd40a8b-f56a-42d5-862e-f3f844caea64","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:14,641 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"4cd40a8b-f56a-42d5-862e-f3f844caea64","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:14,644 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:14,649 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:14,943 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842914927,"webviewId":3,"routeEventId":"3_1753842914693","renderer":"webview"},1753842914936]}}
[I 2025-07-30 10:35:14,946 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842914927, 'webviewId': 3, 'routeEventId': '3_1753842914693', 'renderer': 'webview'}, 1753842914936]}
[D 2025-07-30 10:35:14,954 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:14,956 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec
[D 2025-07-30 10:35:15,458 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3d88f374-0f09-4b44-97ed-fe88e97ba6b5","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:15,461 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3d88f374-0f09-4b44-97ed-fe88e97ba6b5","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:35:15,461 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:15,461 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c36293c5-c5e9-4911-aa65-c23aad12da51","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:15,464 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c36293c5-c5e9-4911-aa65-c23aad12da51","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:15,465 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"eb9eb9dc-f338-4304-8fe7-5af06daca455","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:15,470 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:15,731 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842915713,"webviewId":2,"routeEventId":"2_1753842915485","renderer":"webview"},1753842915722]}}
[D 2025-07-30 10:35:15,733 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"eb9eb9dc-f338-4304-8fe7-5af06daca455","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:15,735 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842915713, 'webviewId': 2, 'routeEventId': '2_1753842915485', 'renderer': 'webview'}, 1753842915722]}
[I 2025-07-30 10:35:15,736 minium.Conn9680 connection#704 _handle_async_msg] received async msg: eb9eb9dc-f338-4304-8fe7-5af06daca455
[D 2025-07-30 10:35:16,239 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b8ad26fd-7683-4bd9-ae11-148fb7517282","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:16,240 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b8ad26fd-7683-4bd9-ae11-148fb7517282","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:16,241 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:16,241 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5a6df102-2237-4898-bf13-c17fe4e61b71","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:16,242 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5a6df102-2237-4898-bf13-c17fe4e61b71","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:16,243 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"ea07110b-41a1-466e-b74b-e884c0971dca","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:16,339 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"ea07110b-41a1-466e-b74b-e884c0971dca","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAHkWZP/48VX2817wzk/u+7xDCFYIYVFzu4MGNisqiKCtfVkR3V3Y91kVXZVeW37K4KrgqoigEFRBQRA4RDEKAEJIAuUgyk2NmMsc779FXVf3+qO5+++*****************************+Vd2NluMBAAAgohBCccUVV/wA81BlFBQUFA4GCAAgYvhaccUVV/zA8oMVy4TBkoKCwlsFUYE4gCA1DzBcDgAiAEQk5vBRU8UVV3xIHo7fcCwfmPr3M5ZRMYuCwtse+xnjaCP4jFIWBYUjCtEhPwLFGZ7KNKMvSoMUFN7SaKwjcoAPS2vKvgxEP1aLCyHq7SOiudxQ9SiuuOKHMxd1jJnYPs3X2awvUzNCq
[I 2025-07-30 10:35:16,340 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:16,341 minium minitest#799 _miniTearDown] =========Current case Down: test_06_app_performance_and_stability=========
[I 2025-07-30 10:35:16,341 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:16,341 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3cca82ac-8611-452d-8173-2de58527adf6","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:16,409 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3cca82ac-8611-452d-8173-2de58527adf6","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//db1cccb96V+yb3ASFcAcSwCnLHixsVb1FWlvXc366ux7rgeuyK7LK4KuKBKMqhghyKyKGIQQgQQhIgF0ney/HeyzvmzdFXVf3+qO6enp6eefNeEhJIfdDOZ2p6qqv6TX3m+/1UdTdajgcAAICIQgjFFVdc8f3MQ5VRUFBQOBAgAICI4WvFFVdc8f3LD1QsEwZLCgoKrxVEBWI/giQeYLQcAEQAiEjMoaOmiiuu+Ig8HL/hWN4/9e9jLKNiFgWF1z32McbRxvAZpSwKCocVokN+DIozOpVpRl+UBikovKbRWEfkAB+V1lR8GYh+LIkLIertI6K53Ej1KK644ocyF3WMmdg+zdfZrC+TGKHUC1tUO
[I 2025-07-30 10:35:16,410 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:16,411 minium basenative#63 wrapper] call BaseNative.get_start_up end 
