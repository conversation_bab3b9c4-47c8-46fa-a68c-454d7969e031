[I 2025-07-30 10:12:26,663 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:12:26,665 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:12:26,665 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:12:26,666 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'E:\\wx-nan\\outputs', 'enable_app_log': False, 'enable_network_panel': False, 'project_path': None, 'dev_tool_path': None, 'test_port': 9420, 'mock_native_modal': {}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'framework_capture': True, 'error_capture': False, 'assert_capture': True, 'create_time': '**************'}
[I 2025-07-30 10:12:26,668 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:12:26,825 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:12:26,826 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:12:26,826 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:12:26,826 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[W 2025-07-30 10:12:26,826 minium wx_minium#783 launch_dev_tool] Can not find `project_path` in config, that means you must open dev tool by automation way first
[W 2025-07-30 10:12:26,827 minium wx_minium#787 launch_dev_tool] If you are not running command like [cli auto --project /path/to/project --auto-port 9420], you may config `project_path` or run this command first
[I 2025-07-30 10:12:26,827 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:31,031 minium.Conn0256 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:31,046 minium.Conn0256 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:31,061 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:31,069 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:35,195 minium.Conn7120 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:35,211 minium.Conn7120 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:35,291 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:35,298 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:12:39,490 minium.Conn0320 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:12:39,522 minium.Conn0320 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:12:39,539 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[E 2025-07-30 10:12:39,542 minium wx_minium#797 launch_dev_tool] three times try to connect Dev tool has all fail ..., restart now...
[I 2025-07-30 10:15:09,211 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:15:09,213 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:15:09,214 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:15:09,215 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'E:\\wx-nan\\outputs', 'enable_app_log': False, 'enable_network_panel': False, 'project_path': None, 'dev_tool_path': None, 'test_port': 9420, 'mock_native_modal': {}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'framework_capture': True, 'error_capture': False, 'assert_capture': True, 'create_time': '**************'}
[I 2025-07-30 10:15:09,216 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:15:09,432 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:15:09,432 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:15:09,434 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:15:09,448 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[W 2025-07-30 10:15:09,449 minium wx_minium#783 launch_dev_tool] Can not find `project_path` in config, that means you must open dev tool by automation way first
[W 2025-07-30 10:15:09,498 minium wx_minium#787 launch_dev_tool] If you are not running command like [cli auto --project /path/to/project --auto-port 9420], you may config `project_path` or run this command first
[I 2025-07-30 10:15:09,498 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:13,655 minium.Conn9680 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:13,670 minium.Conn9680 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:13,686 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:13,762 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:17,907 minium.Conn2928 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:17,922 minium.Conn2928 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:17,953 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:17,969 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:15:22,101 minium.Conn6128 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:15:22,117 minium.Conn6128 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:15:22,147 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[E 2025-07-30 10:15:22,195 minium wx_minium#797 launch_dev_tool] three times try to connect Dev tool has all fail ..., restart now...
[I 2025-07-30 10:26:11,253 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:26:11,254 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:26:11,254 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:26:11,255 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': '', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:26:11,255 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:26:11,352 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:26:11,352 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:26:11,353 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:29:23,520 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:29:23,522 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:29:23,522 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:29:23,522 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'skip', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:30:52,071 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:30:52,072 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:30:52,072 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:30:52,073 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:32:29,800 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:32:29,802 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:32:29,802 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:32:29,802 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:32:29,803 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 10:32:29,804 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 10:32:33,560 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✔ auto

[I 2025-07-30 10:32:33,561 minium minium_object#102 _do_shell] out:

[I 2025-07-30 10:32:43,578 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 10:32:44,249 minium.Conn4976 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 10:32:44,253 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"d60691b3-6792-491a-ac86-8658ff700444","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:32:44,264 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"d60691b3-6792-491a-ac86-8658ff700444","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 10:32:44,267 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"c2ab2a67-41a7-43e0-8e8c-4a5ff42d68e3","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:32:53,097 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"method":"App.initialized","params":{"from":"devtools"}}
[W 2025-07-30 10:32:53,098 minium.Conn4976 connection#330 notify] no observer listening event App.initialized
[D 2025-07-30 10:32:53,104 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"c2ab2a67-41a7-43e0-8e8c-4a5ff42d68e3","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 10:32:53,105 minium.App8000 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 10:32:53,107 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"ac5c9ba5-8366-4760-b75c-74ce24d872e2","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 10:32:53,111 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"ac5c9ba5-8366-4760-b75c-74ce24d872e2","result":{"result":{"injected":false,"isThirdApp":false}}}
[D 2025-07-30 10:32:53,112 minium.Conn4976 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:32:53,112 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"8b68c1cb-44f0-494d-afe7-6b2dd65d719f","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 10:32:53,114 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"8b68c1cb-44f0-494d-afe7-6b2dd65d719f","result":{}}
[D 2025-07-30 10:32:53,115 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"12b0f553-a68b-4ba3-b58f-af9b100d8aa2","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 10:32:53,119 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"12b0f553-a68b-4ba3-b58f-af9b100d8aa2","result":{}}
[D 2025-07-30 10:32:53,119 minium.App8000 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 10:32:53,120 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"b506cb7c-9425-4e97-ad60-b8937179c901","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 10:32:53,126 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"b506cb7c-9425-4e97-ad60-b8937179c901","result":{}}
[D 2025-07-30 10:32:53,126 minium.App8000 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 10:32:53,128 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"94480d0c-a9d5-4d2c-b34f-0cbba76a7350","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 10:32:53,165 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"94480d0c-a9d5-4d2c-b34f-0cbba76a7350","result":{}}
[D 2025-07-30 10:32:53,192 minium.App8000 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 10:32:53,197 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e5cfea2d-d94b-4926-ad2d-dd68b2a2149f","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 10:32:53,214 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e5cfea2d-d94b-4926-ad2d-dd68b2a2149f","result":{"result":true}}
[D 2025-07-30 10:32:53,229 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e2493705-a28a-45d8-8bdf-fe2fed2b8b3f","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 10:32:53,274 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e2493705-a28a-45d8-8bdf-fe2fed2b8b3f","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 10:32:53,275 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"68315b6b-51f1-4493-b282-3618a11ada55","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 10:32:53,278 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"68315b6b-51f1-4493-b282-3618a11ada55","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 10:32:53,278 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e1d03b43-cc7b-47b5-91ac-358d5accef59","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 10:32:53,280 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e1d03b43-cc7b-47b5-91ac-358d5accef59","result":{}}
[D 2025-07-30 10:32:53,280 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"c96cc170-e726-4898-809f-04d2ee58e0e7","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 10:32:53,286 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"c96cc170-e726-4898-809f-04d2ee58e0e7","result":{}}
[D 2025-07-30 10:32:53,288 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"193399db-9fde-4a62-a299-224ec46f8bd2","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 10:32:53,290 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"193399db-9fde-4a62-a299-224ec46f8bd2","result":{}}
[D 2025-07-30 10:32:53,292 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"f71dad89-0244-466f-9236-bf8a9f4061bb","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 10:32:53,294 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"f71dad89-0244-466f-9236-bf8a9f4061bb","result":{}}
[D 2025-07-30 10:32:53,294 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"bba5bab1-f775-4120-8388-d363d7a7fa66","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 10:32:53,296 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"bba5bab1-f775-4120-8388-d363d7a7fa66","result":{}}
[D 2025-07-30 10:32:53,297 minium.App8000 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 10:32:53,298 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"73e4c126-b11a-46e6-a6b5-975364df9b4c","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 10:32:53,311 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"73e4c126-b11a-46e6-a6b5-975364df9b4c","result":{}}
[D 2025-07-30 10:32:53,327 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9f8f9b42-4405-41d3-ba21-c24940cdd604","method":"App.addBinding","params":{"name":"showModal_before_1753842773327"}}
[D 2025-07-30 10:32:53,329 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9f8f9b42-4405-41d3-ba21-c24940cdd604","result":{}}
[D 2025-07-30 10:32:53,330 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"611aa935-1b86-472c-b5c0-a4af8a50ec16","method":"App.addBinding","params":{"name":"showModal_callback_1753842773327"}}
[D 2025-07-30 10:32:53,338 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"611aa935-1b86-472c-b5c0-a4af8a50ec16","result":{}}
[D 2025-07-30 10:32:53,341 minium.App8000 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:32:53,342 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"f2eeba0a-8f89-436b-ad5a-f0ae168dcdbc","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753842773327,after:undefined,callback:showModal_callback_1753842773327},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753842773327,true]}}
[D 2025-07-30 10:32:53,346 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"f2eeba0a-8f89-436b-ad5a-f0ae168dcdbc","result":{}}
[D 2025-07-30 10:32:53,348 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9227c941-2db0-4185-9a59-ca0b183d1156","method":"App.addBinding","params":{"name":"showToast_before_1753842773348"}}
[D 2025-07-30 10:32:53,351 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9227c941-2db0-4185-9a59-ca0b183d1156","result":{}}
[D 2025-07-30 10:32:53,353 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"5beec953-b5f7-4c66-b38c-d0a38f1a89ed","method":"App.addBinding","params":{"name":"showToast_callback_1753842773348"}}
[D 2025-07-30 10:32:53,356 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"5beec953-b5f7-4c66-b38c-d0a38f1a89ed","result":{}}
[D 2025-07-30 10:32:53,358 minium.App8000 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:32:53,359 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"7cbac137-654a-4ce7-939b-62ba862ff7c7","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753842773348,after:undefined,callback:showToast_callback_1753842773348},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753842773348,true]}}
[D 2025-07-30 10:32:53,362 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"7cbac137-654a-4ce7-939b-62ba862ff7c7","result":{}}
[I 2025-07-30 10:32:53,362 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x0000020E12A430E0>, whether should relaunch: False
[D 2025-07-30 10:32:53,363 minium.Conn4976 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:32:53,363 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"373f435b-75ca-4e4b-853a-172680324bfd","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:32:53,365 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"373f435b-75ca-4e4b-853a-172680324bfd","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 10:32:53,365 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 10:32:53,368 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"621829f4-77c1-4cf0-b359-39279ca0f558","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 10:32:53,372 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"621829f4-77c1-4cf0-b359-39279ca0f558","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 10:32:53,372 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 10:32:53,373 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 10:32:53,373 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"2556ebdd-086a-43a6-a7d3-352980e72975","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 10:32:53,780 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"2556ebdd-086a-43a6-a7d3-352980e72975","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 10:32:53,786 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"839d6898-a93d-449d-995f-a0527b0190f1","method":"App.callFunction","params":{"functionDeclaration":"function ideMockModal(){return!!global.__minium__.mock_native_modal||(global.__minium__.auth_setting={},global.__minium__.mock_native_modal_list=[],global.__minium__.handle_mock_modal=function(_,l){let m=[];for(;global.__minium__.mock_native_modal_list.length>0;){let i=global.__minium__.mock_native_modal_list.pop();if(-1===l.indexOf(i.type)||!(_ in i)){m.unshift(i);continue}let o=i[_];if(global.__minium__.mock_native_modal_list.push(...m),\"function\"!=typeof o)throw`${o} is not callable`;return o(),!0}return console.warn(`can't find [${l}][${_}]`),global.__minium__.mock_native_modal_list.push(...m),!1},global.__minium__.get_all_modal=function(){let _=[];global.__minium__.mock_native_modal_list.forEach((l=>{_.push({type:l.type,texts:Object.keys(l).filter((_=>\"type\"!==_))})}))},global.__minium__.handle_mock_native_modal=function(_){return global.__minium__.handle_mock_modal(_,[\"modal\",\"auth\"])},global.__minium__.handle_mock_map_modal=function(_){return global.__minium__.handle_mock_modal(_,[\"map\"])},global.__minium__.handle_mock_auth_modal=function(_){return global.__minium__.handle_mock_modal(_,[\"auth\"])},global.__minium__.mock_native_modal=function(_){_.type=\"modal\",global.__minium__.mock_native_modal_list.push(_)},global.__minium__.mock_map_modal=function(_){_.type=\"map\",global.__minium__.mock_native_modal_list.push(_)},global.__minium__.mock_auth_modal=function(_){_.type=\"auth\",global.__minium__.mock_native_modal_list.push(_)},!1)}","args":[]}}
[D 2025-07-30 10:32:53,794 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"839d6898-a93d-449d-995f-a0527b0190f1","result":{"result":false}}
[D 2025-07-30 10:32:53,797 minium.App8000 app#618 _evaluate_js] evaluate js file mockWxMethod [ALL]
[D 2025-07-30 10:32:53,799 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"91cd971a-440e-43c8-806c-c1b467082fd3","method":"App.callFunction","params":{"functionDeclaration":"function mockWxMethod(o,_){if(!global.__minium__.canMock)throw new Error(\"mock\u65b9\u6cd5\u672a\u5b9e\u73b0\");const n=function miniActionSheet(e){return new Promise((n=>{let t={},o=e.itemList;for(let e=0;e<o.length;e++)t[o[e]]=function(){let t={errMsg:\"showActionSheet:ok\",tapIndex:e};return n(t),t};return t[\"\u53d6\u6d88\"]=function(){let e={errMsg:\"showActionSheet:fail cancel\"};return n(e),e},console.warn(\"[minium] mock wx.showActionSheet, you can use `native.handle_action_sheet` to handle it\"),global.__minium__.mock_native_modal(t)}))};n?global.__minium__.setMock(o,n,_):global.__minium__.delMock(o)}","args":["showActionSheet",null]}}
[D 2025-07-30 10:32:53,805 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"91cd971a-440e-43c8-806c-c1b467082fd3","result":{}}
[D 2025-07-30 10:32:53,806 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"0ff96aa8-930b-4a26-90f1-8256b4746510","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:32:53,810 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"0ff96aa8-930b-4a26-90f1-8256b4746510","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:32:53,810 minium.App8000 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:32:53,811 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9ed2206d-36d4-4953-8558-f63c4e21e71e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:32:53,815 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9ed2206d-36d4-4953-8558-f63c4e21e71e","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:32:53,815 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"db4cef96-1436-41c4-bbfd-71e329aab409","method":"App.callWxMethod","params":{"method":"getSetting","args":[]}}
[D 2025-07-30 10:32:54,141 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"db4cef96-1436-41c4-bbfd-71e329aab409","result":{"result":{"errMsg":"getSetting:ok","authSetting":{"scope.address":true,"scope.invoice":true,"scope.invoiceTitle":true,"scope.userInfo":true}}}}
[D 2025-07-30 10:32:54,143 minium.App8000 app#618 _evaluate_js] evaluate js file mockWxMethod [ALL]
[D 2025-07-30 10:32:54,144 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"b9a6fd0f-41d6-4f58-bdd6-b96c46dfb7b9","method":"App.callFunction","params":{"functionDeclaration":"function mockWxMethod(o,_){if(!global.__minium__.canMock)throw new Error(\"mock\u65b9\u6cd5\u672a\u5b9e\u73b0\");const n=function miniGetLocation(o,n,e){return new Promise((o=>{let t={errMsg:\"chooseLocation:ok\",name:\"\u817e\u8baf\u5fae\u4fe1\u603b\u90e8\",address:\"\u5e7f\u4e1c\u7701\u5e7f\u5dde\u5e02\",latitude:23.12463,longitude:113.36199},i={errMsg:\"chooseLocation:fail cancel\"},c={errMsg:\"chooseLocation:fail auth deny\"},a={};for(let o in t)void 0!==n[o]&&(t[o]=n[o]);for(let o in e)a[o]=Object.assign({},t,e[o]);wx.authorize({scope:\"scope.userLocation\",success:()=>function(){let n={\"\u786e\u5b9a\":function(){return o(t),t}};n[t.name]=function(){return o(t),t};for(let e in a)n[e]=function(){return o(a[e]),a[e]};n[\"\u53d6\u6d88\"]=function(){return o(i),i},global.__minium__.mock_map_modal(n)}(),fail:()=>(o(c),c)}),console.warn(\"[minium] mock wx.chooseLocation, you can use `native.allow_get_location` to handle it\")}))};n?global.__minium__.setMock(o,n,_):global.__minium__.delMock(o)}","args":["chooseLocation",[{},{}]]}}
[D 2025-07-30 10:32:54,146 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"b9a6fd0f-41d6-4f58-bdd6-b96c46dfb7b9","result":{}}
[D 2025-07-30 10:32:54,147 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"000c05df-b483-4568-bcd1-f9b01de1e1af","method":"App.callFunction","params":{"functionDeclaration":"function mockNetwork(e){if(!global.__minium__[`${e}_network_mocked`]){Object.defineProperty(global.__minium__,`${e}_network_mocked`,{value:!0,writable:!1});class s{constructor(e,s,c){this.success=e,this.fail=s,this.callback=c,this.chunkCallback=[],this.headersReceivedCallback=[],this.progressUpdateCallback=[],this.fd=setTimeout((()=>{this.headersReceivedCallback.forEach((e=>{this.success&&e({header:this.success.header})})),this.chunkCallback.forEach((e=>{this.success&&e(this.success.data)})),this.progressUpdateCallback.forEach((e=>{this.success&&e({progress:100,totalBytesWritten:100,totalBytesExpectedToWrite:100})})),c(this.success||this.fail)}))}abort(){clearTimeout(this.fd)}offChunkReceived(e){var s=this.chunkCallback.indexOf(e);-1!==s&&this.chunkCallback.splice(s,1)}offHeadersReceived(e){var s=this.headersReceivedCallback.indexOf(e);-1!==s&&this.headersReceivedCallback.splice(s,1)}onChunkReceived(e){this.chunkCallback.push(e)}onHeadersReceived(e){this.headersReceivedCallback.push(e)}onProgressUpdate(e){this.progressUpdateCallback.push(e)}}global.__minium__[`${e}Task`]=s;const c=new global.__minium__.MockRule(`${e}_network_mock_rule`);global.__minium__.setMock(e,(function(e){const a=(e.method||\"GET\").toUpperCase(),t=e.url,[i,l]=t.split(\"?\"),o=function(e){if(!(e=e.split(\"#\")[0]))return{};const s={},c=e.split(\"&\");for(let e=0;e<c.length;e++){const[a,t]=c[e].split(\"=\");s[a]=decodeURIComponent(t)}return s}(l||\"\");\"GET\"===a&&Object.assign(o,e.data);const r=Object.assign({},e,{url:i,params:o});\"GET\"===a&&Object.assign(r,{data:o});let h=c.search(e);if(!h)if(r.params){const e=Object.assign({},r);e.url=`${r.url}?${Object.keys(r.params).map((e=>`${e}=${r.params[e]}`)).join(\"&\")}`,delete e.params,h=c.search(r,e)}else h=c.search(r);return h?(console.log(\"@@@@rule match\",h),e.__miniumMocked=!0,h.success?new s(h.success,void 0,(s=>{e.success&&e.success(s),e.complete&&e.complete(s)})):h.fail?new s(void 0,h.fail,(s=>{e.fail&&e.fail(s),e.complete&&e.complete(s)})):void 0):this.origin(e)}))}}","args":["request"]}}
[D 2025-07-30 10:32:54,151 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"000c05df-b483-4568-bcd1-f9b01de1e1af","result":{}}
[D 2025-07-30 10:32:54,153 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e6538534-82ef-4522-94b3-72037c5a5b15","method":"App.enableLog","params":{}}
[D 2025-07-30 10:32:54,157 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e6538534-82ef-4522-94b3-72037c5a5b15","result":{}}
[D 2025-07-30 10:32:54,158 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"fbff503e-e6fc-4cf5-8451-54d5f9cffed0","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 10:32:54,160 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"fbff503e-e6fc-4cf5-8451-54d5f9cffed0","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 10:32:54,161 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"e1d2b4bd-2dac-40eb-83df-0810289bed49","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:32:54,164 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"e1d2b4bd-2dac-40eb-83df-0810289bed49","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 10:32:54,165 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 10:32:54,168 minium minitest#432 _miniSetUp] =========Current case: test_quick_connection=========
[I 2025-07-30 10:32:54,169 minium minitest#435 _miniSetUp] package info: E:.wx-nan.advanced_connection_test, case info: QuickTest.test_quick_connection
[I 2025-07-30 10:32:54,169 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:32:54,169 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:32:54,169 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:32:54,170 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:32:54,170 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:32:54,172 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:32:54,173 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"8108cd55-a64c-4354-ad38-353abc06abeb","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,366 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"8108cd55-a64c-4354-ad38-353abc06abeb","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWV/jm3tl7fln2HkIQQCEEggAijMOxxQxZRETcQRsYRHec3gzPoMLjPyDjDwCjLqIiiLCJIYEQGUBFBSCCEJEA2kryX5b2Xt/TrpdZ7fn/cqurq6up+/V4SE8j9IPVO3751697qvl+f892l0LRdAAAARCQiaUtb2tLey3bIMhISEhL7AgwAEDF8LW1pS1vae9eWvoyEhMS+hRpa+yg245zv0/KlLW1p76HNGHvT6DJhoRISEm92RAOfPYQ6epbRIMlFQuKth6hXsodFjZ9lJLlISBwM2HO6ibAMIoTE0dT2r9pyfr+uAGPKL21pS3uf2tGhoFbyi46PiGO91th0mVb8F+njSEi8qdGKzzImv6bViKk5d
[I 2025-07-30 10:32:54,369 minium minitest#487 _miniSetUp] =========case: test_quick_connection start=========
[I 2025-07-30 10:32:54,369 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:32:54,370 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"29187262-9304-47f3-8aff-97a6c3d908d6","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:32:54,372 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"29187262-9304-47f3-8aff-97a6c3d908d6","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:32:54,373 minium.App8000 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:32:54,374 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"9577513a-1e08-4d52-9e16-b3cbf32d7e0c","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:32:54,376 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"9577513a-1e08-4d52-9e16-b3cbf32d7e0c","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:32:54,377 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"81f65f54-526b-434d-91ad-16196fcc2952","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,517 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"81f65f54-526b-434d-91ad-16196fcc2952","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgHFWZNv6+p7be7pp9JyE7hCAQQAQVhz1uCCIqg6hsIx+fyzjfiOMyDO4zMvyGERVwVEQRhEHQwIiOgCKCkEAISYBsJLk3ubn35i59e6n1vL8/TlV1dXV13743iUnIeSB13z596tQ51X2eft/nLIWm7QIAACAiEUlb2tKW9n62Q5aRkJCQOBBgAICI4WtpS1va0t6/tvRlJCQkDizU0DpAsRnn/ICWL21pS3sfbcbYYaPLhIVKSEgc7ogGPvsIdfQso0GSi4TEGw9Rr2Qfixo/y0hykZA4ErDvdBNhGUQIiaOh7V+16fx+XQHGlF/a0pb2AbWjQ0HN5BcdHxHHeq2x6TLN+C/Sx5GQOKzRjM8yJr+m2YipM
[I 2025-07-30 10:32:54,536 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:32:54,536 minium minitest#799 _miniTearDown] =========Current case Down: test_quick_connection=========
[I 2025-07-30 10:32:54,538 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:32:54,538 minium.Conn4976 connection#427 _safely_send] SEND > [*************]{"id":"30904b09-3e5f-4e93-924e-3578a0dfc97f","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,622 minium.Conn4976 connection#660 __on_message] RECV < [*************]{"id":"30904b09-3e5f-4e93-924e-3578a0dfc97f","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq5e5KbfSEkIQmEIBBABBWHPW4oICqDjoIwMD63eTODIzI83MY3Mr5hZJRlVERRlkHQwIgOoCKCkEAIWchKknuTu+UufXup9Xzvj1NVXV1d3bfvTWIScn6Qul+fPnXqnOo+v/6+31kKTdsFAABARCKStrSlLe0DbIcsIyEhIXEwwAAAEcPX0pa2tKV9YG3py0hISBxcqKF1kGIzzvlBLV/a0pb2ftqMsSNGlwkLlZCQONIRDXz2E+rYWcaCJBcJiTcfol7JfhY1cZaR5CIhcTRg/+kmwjKIEBJHXdu/asP5/boCjCu/tKUt7YNqR4eCGskvOj4ijvda49NlGvFfpI8jIXFEoxGfZVx+TaMRU
[I 2025-07-30 10:32:54,623 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:32:54,624 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:34:15,406 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:34:15,408 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:34:15,408 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:34:15,409 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:34:15,409 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 10:34:15,410 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 10:34:16,815 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 10:34:16,817 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 10:34:16,818 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 10:34:17,310 minium.Conn9680 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 10:34:17,310 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9642798a-d26a-4a80-937e-b7e314fbf932","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:34:17,312 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9642798a-d26a-4a80-937e-b7e314fbf932","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 10:34:17,313 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"96253e35-8eb9-4b01-bca5-cc581ec89494","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:34:17,321 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"96253e35-8eb9-4b01-bca5-cc581ec89494","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 10:34:17,322 minium.App4384 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 10:34:17,323 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"41521ce8-87c6-4d58-848d-442d3dd2c195","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 10:34:17,325 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"41521ce8-87c6-4d58-848d-442d3dd2c195","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 10:34:17,325 minium.Conn9680 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:34:17,326 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c27f1e82-1ff2-46aa-af73-49ea928f4709","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 10:34:17,328 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c27f1e82-1ff2-46aa-af73-49ea928f4709","result":{}}
[D 2025-07-30 10:34:17,329 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e0cbd8cc-6114-46a2-b513-bb3a2b123d1d","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 10:34:17,331 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e0cbd8cc-6114-46a2-b513-bb3a2b123d1d","result":{}}
[D 2025-07-30 10:34:17,331 minium.App4384 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 10:34:17,332 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"00fd547d-d282-4ca9-bbc5-f559e9000bbc","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 10:34:17,337 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"00fd547d-d282-4ca9-bbc5-f559e9000bbc","result":{}}
[D 2025-07-30 10:34:17,338 minium.App4384 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 10:34:17,338 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"f7db0b42-a422-4923-8817-d92e739c92b4","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 10:34:17,342 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f7db0b42-a422-4923-8817-d92e739c92b4","result":{}}
[D 2025-07-30 10:34:17,342 minium.App4384 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 10:34:17,343 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"50810028-a6d6-4a9e-865a-ba95c2a9526e","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 10:34:17,345 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"50810028-a6d6-4a9e-865a-ba95c2a9526e","result":{"result":false}}
[D 2025-07-30 10:34:17,346 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c12c1600-9e59-400d-9d1c-00e5c7bf0895","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 10:34:17,348 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c12c1600-9e59-400d-9d1c-00e5c7bf0895","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 10:34:17,348 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9873cd7f-ecd1-43f6-9cdc-fbbfeaa9d0d0","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 10:34:17,354 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9873cd7f-ecd1-43f6-9cdc-fbbfeaa9d0d0","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 10:34:17,355 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fb496891-37de-4b40-98d7-cdc963adea26","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 10:34:17,358 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fb496891-37de-4b40-98d7-cdc963adea26","result":{}}
[D 2025-07-30 10:34:17,359 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9f86e726-28d7-4b42-8008-d1e410e8b1df","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 10:34:17,361 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9f86e726-28d7-4b42-8008-d1e410e8b1df","result":{}}
[D 2025-07-30 10:34:17,362 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1ba18b64-146d-4451-b360-c7c934685f3a","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 10:34:17,364 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1ba18b64-146d-4451-b360-c7c934685f3a","result":{}}
[D 2025-07-30 10:34:17,367 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"65a8258e-da69-43ba-8d0e-1e302db0bcd4","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 10:34:17,375 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"65a8258e-da69-43ba-8d0e-1e302db0bcd4","result":{}}
[D 2025-07-30 10:34:17,381 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"6b5c5096-40bc-4297-821c-000332948ff0","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 10:34:17,391 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"6b5c5096-40bc-4297-821c-000332948ff0","result":{}}
[D 2025-07-30 10:34:17,392 minium.App4384 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 10:34:17,393 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"35af95a5-3c2f-4d6c-810e-a5dc0e9606fd","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 10:34:17,395 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"35af95a5-3c2f-4d6c-810e-a5dc0e9606fd","result":{"result":true}}
[D 2025-07-30 10:34:17,396 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"134d73f4-470c-428a-b95f-a5f82fba1459","method":"App.addBinding","params":{"name":"showModal_before_1753842857396"}}
[D 2025-07-30 10:34:17,397 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"134d73f4-470c-428a-b95f-a5f82fba1459","result":{}}
[D 2025-07-30 10:34:17,398 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"45f1284f-1af4-480b-9e0e-7b7cefb72ec2","method":"App.addBinding","params":{"name":"showModal_callback_1753842857396"}}
[D 2025-07-30 10:34:17,401 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"45f1284f-1af4-480b-9e0e-7b7cefb72ec2","result":{}}
[D 2025-07-30 10:34:17,401 minium.App4384 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:34:17,402 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b6d3ebb1-d031-4934-a832-806ddeb2b2d5","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753842857396,after:undefined,callback:showModal_callback_1753842857396},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753842857396,true]}}
[D 2025-07-30 10:34:17,405 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b6d3ebb1-d031-4934-a832-806ddeb2b2d5","result":{}}
[D 2025-07-30 10:34:17,406 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1f8f243f-a4ff-41ef-b55e-cbf94e787751","method":"App.addBinding","params":{"name":"showToast_before_1753842857406"}}
[D 2025-07-30 10:34:17,409 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1f8f243f-a4ff-41ef-b55e-cbf94e787751","result":{}}
[D 2025-07-30 10:34:17,414 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"162e2a9c-6818-4d58-930c-c1985abb03ff","method":"App.addBinding","params":{"name":"showToast_callback_1753842857406"}}
[D 2025-07-30 10:34:17,418 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"162e2a9c-6818-4d58-930c-c1985abb03ff","result":{}}
[D 2025-07-30 10:34:17,420 minium.App4384 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:34:17,420 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3a0ce6b6-ac90-446b-8d90-a8b16d04d427","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753842857406,after:undefined,callback:showToast_callback_1753842857406},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753842857406,true]}}
[D 2025-07-30 10:34:17,423 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3a0ce6b6-ac90-446b-8d90-a8b16d04d427","result":{}}
[I 2025-07-30 10:34:17,424 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x000001ECD4A3A660>, whether should relaunch: True
[D 2025-07-30 10:34:17,425 minium.Conn9680 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:34:17,425 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8908f0e3-5c7b-43d2-87b1-ba38ca338d2b","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:34:17,428 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8908f0e3-5c7b-43d2-87b1-ba38ca338d2b","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 10:34:17,428 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 10:34:17,428 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"936d85cd-4cf6-48e1-af03-7d99fc5cd85e","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 10:34:17,430 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"936d85cd-4cf6-48e1-af03-7d99fc5cd85e","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 10:34:17,430 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 10:34:17,430 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 10:34:17,431 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"f6715e3d-8764-4be9-acf6-3e06f84c9b6f","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 10:34:17,437 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f6715e3d-8764-4be9-acf6-3e06f84c9b6f","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 10:34:17,440 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"cfc8d872-b308-4a66-81de-003a3c00c68c","method":"App.enableLog","params":{}}
[D 2025-07-30 10:34:17,442 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"cfc8d872-b308-4a66-81de-003a3c00c68c","result":{}}
[D 2025-07-30 10:34:17,444 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"044ab45e-159d-42af-88a1-c6b028a602ac","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 10:34:17,447 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"044ab45e-159d-42af-88a1-c6b028a602ac","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 10:34:17,449 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"d210f37b-9b8b-47c7-a7ae-eada141a5022","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:34:17,463 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"d210f37b-9b8b-47c7-a7ae-eada141a5022","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 10:34:17,463 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 10:34:17,472 minium minitest#432 _miniSetUp] =========Current case: test_01_app_launch_and_basic_info=========
[I 2025-07-30 10:34:17,472 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_01_app_launch_and_basic_info
[I 2025-07-30 10:34:17,473 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:17,473 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:17,473 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:17,474 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:17,474 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:17,489 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:17,489 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3951dc8c-38a8-481b-b30f-fa78c80eb42c","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:17,590 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3951dc8c-38a8-481b-b30f-fa78c80eb42c","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXeAHMWZNv6+1WnSzgZJqxxRBiEMCDAWZ+MjywZjgsHmMGeTDj5/xuH33TlwPg4cf2eO7ziwTXDA2NiEw4AFZ/ABtjEmSUgIBVBC0q602l1tmJ3Qser7o7p7anp6ZmdXkpXqAfW+U1NdXdUz9cz7PhUaTdsFAABARMaYtKUtbWnvYztkGQkJCYn9AQIAiBi+lra0pS3tfWtLX0ZCQmL/Qg2t/RSbUUr3a/nSlra099ImhBwyukxYqISExKEOMfDZS6jDZxkOklwkJA4/iF7JXhY1epaR5CIhcSRg7+lGYBlECImjru1fteH8fl0BRpRf2tKW9n61xaGgRvLzjo+II73WyHSZRvwX6eNISBzSaMRnGZFf02jEV
[I 2025-07-30 10:34:17,592 minium minitest#487 _miniSetUp] =========case: test_01_app_launch_and_basic_info start=========
[I 2025-07-30 10:34:18,593 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 10:34:18,802 minium minitest#897 capture] capture assertIsNotNone-success.png
[D 2025-07-30 10:34:18,802 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"61e870da-065e-4466-abf8-a2dd46775918","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:18,879 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"61e870da-065e-4466-abf8-a2dd46775918","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq5t++S5GZPICQhBELYQQQVZY8bCgjKICoIA8/nNm9mcGMY3MY3MrxhRGUZFVGUZRA0MIIjqIhsCYSQBbKR5SZ3y1369lLr+d4fp6q6urq6b9+bxARyfpC6X58+deqc6j6//r7fWQpN2wUAAEBEIpK2tKUt7b1shywjISEhsS/AAAARw9fSlra0pb13benLSEhI7FuoobWPYjPO+T4tX9rSlvYe2oyxN40uExYqISHxZkc08NlDqGNnGQuSXCQk3nqIeiV7WNTEWUaSi4TEwYA9p5sIyyBCSBwNbf+qTef36wowrvzSlra096kdHQpqJr/o+Ig43muNT5dpxn+RPo6ExJsazfgs4/Jrmo2YG
[D 2025-07-30 10:34:18,881 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"12291ce3-67cf-4803-a6cc-817cf2237153","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:18,883 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"12291ce3-67cf-4803-a6cc-817cf2237153","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:18,885 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:18,886 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b25b0aa1-d3ea-4b47-899d-c50d1f30cb56","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:18,888 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b25b0aa1-d3ea-4b47-899d-c50d1f30cb56","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:34:18,890 minium minitest#897 capture] capture assertIsNotNone-success_103418890827.png
[D 2025-07-30 10:34:18,891 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"7e6b3491-6ef6-412f-9231-36b79a9538de","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:18,963 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"7e6b3491-6ef6-412f-9231-36b79a9538de","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6+5261dFcvSTp7AiEJSSCEHURQUfa4oYCoDKKCIHx+brOIyzAMbuNvhuE3jKgsoyKKsgyCBkZwBBWRLYEQkgDZSNKddLo7vVTXctfzfn+ce2/dunWrurqTkEDOA7n91qlzzz3nVp2n3vc5y0XTdgEAABCRiKQtbWlLey/bIctISEhI7AswAEDE8LW0pS1tae9dW/oyEhIS+xZqaO2j2Ixzvk/Ll7a0pb2HNmPsTaPLhIVKSEi82RENfPYQ6thZxoIkFwmJtx6iXskeFjVxlpHkIiFxMGDP6SbCMogQEkdD279q0/n9ugKMK7+0pS3tfWpHh4KayS86PiKO91rj02Wa8V+kjyMh8aZGMz7LuPyaZ
[D 2025-07-30 10:34:18,988 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"8ee7fc8d-669c-4e27-95d9-ddb264b410e1","method":"App.callFunction","params":{"functionDeclaration":"wx.getSystemInfoSync()","args":[]}}
[D 2025-07-30 10:34:18,989 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"ea1730fb-be61-4eb4-84d9-bec5a31a4c36","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,004 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8ee7fc8d-669c-4e27-95d9-ddb264b410e1","error":{"message":"Arg string terminates parameters early"}}
[E 2025-07-30 10:34:19,004 minium.Conn9680 connection#668 __on_message] [8ee7fc8d-669c-4e27-95d9-ddb264b410e1]: Arg string terminates parameters early
[I 2025-07-30 10:34:19,005 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 8ee7fc8d-669c-4e27-95d9-ddb264b410e1
[D 2025-07-30 10:34:19,087 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"ea1730fb-be61-4eb4-84d9-bec5a31a4c36","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6+5661dPWSpLMnJCQhCYSwBRBBRdnjhgKCMogKwsDn5/p9M7iMMriNv5HhNwyoLKMiirIMggRGcAQVkS2BEJIA2UjSnXS6O71U13LXc74/zr23Tt26VV3dSUhCzgO5/dapc88951adp973OctFy/EAAAAQkTEmbWlLW9p72Y5YRkJCQmJfgAAAIkavpS1taUt779rSl5GQkNi3UCNrH8VmlNJ9Wr60pS3tPbQJIQeNLhMVKiEhcbBDDHz2EOroWUaDJBcJibcfRK9kD4saP8tIcpGQOBSw53QjsAwiRMTR0A6u2nT+oK4AY8ovbWlLe5/a4lBQM/l5x0fEsV5rbLpMM/6L9HEkJA5qNOOzjMmva
[I 2025-07-30 10:34:19,089 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:34:19,089 minium minitest#799 _miniTearDown] =========Current case Down: test_01_app_launch_and_basic_info=========
[I 2025-07-30 10:34:19,089 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:34:19,090 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"10cce0aa-fb5f-4589-a758-93c39eecdc7d","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,164 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"10cce0aa-fb5f-4589-a758-93c39eecdc7d","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXeAHMWV93vVadLOBkmrnAOSQIgkwFjYgMlywibaHMY2GM6cP4fz3Rmfw3E4nP3dYb7j4GzC2cbY2ARjMIIzYINtjEkSCKEASkjalTZpw+yEjlXfH9XdU9PTMzu7kpCE6gfqfVNTXV3VM/Wb934VGk3bBQAAQETGmLSlLW1p72M7ZBkJCQmJ/QECAIgYvpa2tKUt7X1rS19GQkJi/0INrf0Um1FK92v50pa2tPfSJoQcMrpMWKiEhMShDjHw2UuoI2cZCZJcJCTeeRC9kr0sauwsI8lFQuJwwN7TjcAyiBASR13bv2rD+f26Aowqv7SlLe39aotDQY3k5x0fEUd7rdHpMo34L9LHkZA4pNGIzzIqv6bRiKk+d
[I 2025-07-30 10:34:19,167 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:34:19,167 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:34:19,210 minium minitest#432 _miniSetUp] =========Current case: test_02_navigate_all_main_pages=========
[I 2025-07-30 10:34:19,211 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_02_navigate_all_main_pages
[I 2025-07-30 10:34:19,211 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:19,211 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:19,212 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:19,212 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:19,212 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:19,213 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:19,213 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"04ea56e9-38c4-4884-bf62-4ad021363733","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:19,301 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"04ea56e9-38c4-4884-bf62-4ad021363733","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAnEWZP/489V59Tc+RO5kc5CIJhHAFEEFBuaMicgjIIioIC8t67oHKui54rL9d5bcIKseiiKIQFgEDK7AGFTkkgYSQg5wkmckxM5mjp4/3rPr+Ue/7dvXbb/f0TBKSkPpA3nm6ut56q97u+vTzfOp40bRdAAAARGSMSVva0pb2PrZDlpGQkJDYHyAAgIjha2lLW9rS3re29GUkJCT2L9TQ2k+xGaV0v5YvbWlLey9tQsgho8uEhUpISBzqEAOfvYQ6dJahIMlFQuK9B9Er2cuiRs4yklwkJA4H7D3dCCyDCCFx1LX9qzac368rwLDyS1va0t6vtjgU1Eh+3vERcbjXGp4u04j/In0cCYlDGo34LMPyaxqNm
[I 2025-07-30 10:34:19,304 minium minitest#487 _miniSetUp] =========case: test_02_navigate_all_main_pages start=========
[I 2025-07-30 10:34:20,306 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:34:20,307 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"eb696679-b195-4a08-9178-974ab1751002","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:20,350 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:20,352 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"eb696679-b195-4a08-9178-974ab1751002","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:20,353 minium.Conn9680 connection#704 _handle_async_msg] received async msg: eb696679-b195-4a08-9178-974ab1751002
[D 2025-07-30 10:34:35,313 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842875310,"webviewId":3,"routeEventId":"3_1753842874825","renderer":"webview"},1753842875311]}}
[D 2025-07-30 10:34:35,314 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:34:35,315 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842875310, 'webviewId': 3, 'routeEventId': '3_1753842874825', 'renderer': 'webview'}, 1753842875311]}
[W 2025-07-30 10:34:35,315 minium.App4384 app#1013 switch_tab] Switch tab(/pages/home/<USER>/pages/mine/index)
[D 2025-07-30 10:34:35,316 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:34:35,421 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842875418,"webviewId":2,"routeEventId":"2_1753842875314","renderer":"webview"},1753842875419]}}
[D 2025-07-30 10:34:35,422 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:34:35,422 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842875418, 'webviewId': 2, 'routeEventId': '2_1753842875314', 'renderer': 'webview'}, 1753842875419]}
[D 2025-07-30 10:34:35,423 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:34:37,316 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fbad7f58-546f-4df4-b2c3-e063777a9aa0","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:37,318 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fbad7f58-546f-4df4-b2c3-e063777a9aa0","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:37,319 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:37,319 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fedd1d89-0dba-46c9-8493-d244f4b9b388","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:37,322 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fedd1d89-0dba-46c9-8493-d244f4b9b388","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:37,323 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"cee572a4-eea6-4c33-a509-86e46faf6bba","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:37,395 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"cee572a4-eea6-4c33-a509-86e46faf6bba","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAXFWZNv6ec9daunrJvoeQHUIQCCAGFYc9biggKqOOgjAyjOtvZlDR4cP9U8ZvGFBZRkUUZBFZAiM6gIgsQgIhJAGykaQ7S3enl+pa7nbO+/vj3Hvr1q1b1dWdhE7IeQK33zp17rnn3Krz1Ps+Z7nEcjwAAABCCCJKW9rSlvZ+tkOWkZCQkDgQoABACAlfS1va0pb2/rWlLyMhIXFgoYbWAYrNOOcHtHxpS1va+2hTSg8ZXSYsVEJC4lBHNPDZR6jDZxkOklwkJN56iHol+1jU6FlGkouExOGAfaebCMsQAiFxNLT9qzad368rwIjyS1va0j6gdnQoqJn8ouMTQkZ6rZHpMs34L9LHkZA4pNGMzzIiv6bZi
[D 2025-07-30 10:34:37,426 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"001a7371-6428-4512-90c7-5b16a1496dd5","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:34:37,427 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:34:37,824 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,827 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,831 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,850 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,882 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,884 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,885 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,886 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,895 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🖼️ 开始预加载 2 张菜品图片"]}}
[D 2025-07-30 10:34:37,896 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🖼️ 开始预加载 2 张菜品图片"]}}
[D 2025-07-30 10:34:37,907 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,909 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:34:37,914 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,915 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:34:37,927 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,927 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,928 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,929 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,943 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,944 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:37,951 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:37,954 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:38,028 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,030 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,039 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,041 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,046 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,047 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,051 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,060 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:34:38,292 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842878275,"webviewId":5,"routeEventId":"5_1753842877723","renderer":"webview"},1753842878279]}}
[D 2025-07-30 10:34:38,294 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:34:38,295 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842878275, 'webviewId': 5, 'routeEventId': '5_1753842877723', 'renderer': 'webview'}, 1753842878279]}
[D 2025-07-30 10:34:38,296 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:34:38,297 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"001a7371-6428-4512-90c7-5b16a1496dd5","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:38,297 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 001a7371-6428-4512-90c7-5b16a1496dd5
[D 2025-07-30 10:34:40,299 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1e7a69f0-e2d8-4e24-9ece-35da7c71bce8","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:40,303 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1e7a69f0-e2d8-4e24-9ece-35da7c71bce8","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:34:40,304 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:40,305 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"625f23c7-7a2e-4554-b76b-eeee125a631a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:40,308 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"625f23c7-7a2e-4554-b76b-eeee125a631a","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:40,309 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"870a2e9d-273f-4149-9453-e16171e1b7a3","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:40,389 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"870a2e9d-273f-4149-9453-e16171e1b7a3","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXm8ZVV1J77W2vsMd3rzVFUUUBSCSoEMEY0TnQiKIr+0SUw0BjQdkeAv0e60RdpfNGow7S9CD7ETacRWxCmJSfx0EA0G/QXtJE5QSFFiFVQVVcWb53enM+y91u+Pc+599431qnivxvP9VN23z3D32Xffe77nu9Zee20MIgMAAICIIpKV11LOkCHD2oFNlsmQIUOGjQABACI2t7Py6uUMGTIcKzItkyFDho0FNUunglI4XcoZMmRYOzItkyFDho0FHf2UDBkyZHgeyFgmQ4YMG4sWlmn1O2Tl1csZMmRYMzK/TIYMGTYWmcWUIUOGjUXGMhkyZNhY6JYyAkhWXlt5eSQTnVpfEUApUkREmEXcZDhNISLMYpmt5
[D 2025-07-30 10:34:40,392 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"34c24b2d-fbb8-4e05-b6d7-800d2d94a488","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:34:40,393 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:34:40,626 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842880620,"webviewId":3,"routeEventId":"3_1753842880425","renderer":"webview"},1753842880621]}}
[D 2025-07-30 10:34:40,627 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"34c24b2d-fbb8-4e05-b6d7-800d2d94a488","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:40,628 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842880620, 'webviewId': 3, 'routeEventId': '3_1753842880425', 'renderer': 'webview'}, 1753842880621]}
[I 2025-07-30 10:34:40,628 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 34c24b2d-fbb8-4e05-b6d7-800d2d94a488
[D 2025-07-30 10:34:40,905 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842880902,"webviewId":2,"routeEventId":"2_1753842880689","renderer":"webview"},1753842880903]}}
[I 2025-07-30 10:34:40,907 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842880902, 'webviewId': 2, 'routeEventId': '2_1753842880689', 'renderer': 'webview'}, 1753842880903]}
[D 2025-07-30 10:34:42,630 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"6bdbee72-8272-4481-ab03-61112283db18","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:42,633 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"6bdbee72-8272-4481-ab03-61112283db18","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:42,635 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:42,636 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e9df6139-6091-4c4f-a34d-c5252e32c173","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:42,638 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e9df6139-6091-4c4f-a34d-c5252e32c173","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:34:42,638 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1cb5f109-a622-4ad1-bfdc-da5b81e8fc64","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,711 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1cb5f109-a622-4ad1-bfdc-da5b81e8fc64","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//dbfc35rtw3hCSEQAgCAcSwissdL+QQFW8QVtYVXfe3iyu6LLoeu7LusrDKsSqiKIcIEljRFVREEBIIIQmQiyTv5Xjv5R3z5uirqn5/VHdPTU/PvHkviUlIfSD9vlNTXV3VM/WZ7/dTR6Pt+gAAAIjIOVe2spWt7H1sRyyjoKCgsD9AAAARo9fKVraylb1vbeXLKCgo7F/okbWfYjPG2H4tX9nKVvZe2oSQQ0aXiQpVUFA41CEHPnsJffQso0GRi4LCGw+yV7KXRY2fZRS5KCgcDth7upFYBhEi4mhqB1dtOX9QV4Ax5Ve2spW9X215KKiV/KLjI+JYrzU2XaYV/0X5OAoKhzRa8VnG5Ne0GjE15
[I 2025-07-30 10:34:42,713 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:34:42,713 minium minitest#799 _miniTearDown] =========Current case Down: test_02_navigate_all_main_pages=========
[I 2025-07-30 10:34:42,714 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:34:42,714 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8e7fabc4-8cef-4d67-8d2e-c728e908d998","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,791 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8e7fabc4-8cef-4d67-8d2e-c728e908d998","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgHFWZNv6+p7be7pp9JyF7CEEgBBFUHPa4IciiiIwDwsjnJ+r4KY7LMLiPMv6GARVwVEQRhEHQwIiMgCKLkEAISYBsZLlZ7r25S99eaj3v749TVV1dXd23701iEnIeSN23T586dU51n6ff9zlLoWm7AAAAiEhE0pa2tKW9n+2QZSQkJCQOBBgAIGL4WtrSlra0968tfRkJCYkDCzW0DlBsxjk/oOVLW9rS3kebMXbY6DJhoRISEoc7ooHPPkIdPstwkOQiIfHmQ9Qr2ceiRs8yklwkJI4E7DvdRFgGEULiaGj7V206v19XgBHll7a0pX1A7ehQUDP5RcdHxJFea2S6TDP+i/RxJCQOazTjs4zIr2k2YmrMH
[I 2025-07-30 10:34:42,793 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:34:42,793 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:34:42,836 minium minitest#432 _miniSetUp] =========Current case: test_03_home_page_elements=========
[I 2025-07-30 10:34:42,836 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_03_home_page_elements
[I 2025-07-30 10:34:42,837 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:34:42,837 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:34:42,837 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:34:42,837 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:34:42,838 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:34:42,839 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:34:42,839 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e1ed5e10-07d5-41f6-848b-213617cac0bc","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:34:42,907 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e1ed5e10-07d5-41f6-848b-213617cac0bc","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq7a5KbPYGQhCQQgkAIIjjisMcNQUBFdJRt5PlkdGYUR2V4uD9lfMOACjgqoijCIGBgREZQkUVIIIQkQDay3OTm3pu79O2l1vO9P05VdXV1dd++N4lJyPlB6n59+tSpc6r7/Pr7fmcpNG0XAAAAEYlI2tKWtrT3sR2yjISEhMT+AAMARAxfS1va0pb2vrWlLyMhIbF/oYbWforNOOf7tXxpS1vae2kzxg4ZXSYsVEJC4lBHNPDZS6ijZxkNklwkJN58iHole1nU+FlGkouExOGAvaebCMsgQkgcDW3/qk3n9+sKMKb80pa2tPerHR0Kaia/6PiIONZrjU2XacZ/kT6OhMQhjWZ8ljH5Nc1GT
[I 2025-07-30 10:34:42,938 minium minitest#487 _miniSetUp] =========case: test_03_home_page_elements start=========
[D 2025-07-30 10:34:43,176 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:34:43,177 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[I 2025-07-30 10:34:43,939 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:34:43,940 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"88e16322-bfc7-450a-a9f9-b03c33e96a4c","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:43,943 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:34:43,948 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"88e16322-bfc7-450a-a9f9-b03c33e96a4c","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:34:43,949 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 88e16322-bfc7-450a-a9f9-b03c33e96a4c
[D 2025-07-30 10:34:48,486 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:48,486 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片预加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:34:58,969 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"218c72de-f4ee-4f7e-b7d5-37b47c505427","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:34:58,971 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"218c72de-f4ee-4f7e-b7d5-37b47c505427","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:34:58,971 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:34:58,971 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"6494d1c0-661c-43b6-bdec-96e26dfd4df9","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:34:58,973 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"6494d1c0-661c-43b6-bdec-96e26dfd4df9","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:01,974 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e1b85a8a-dff7-42cd-a497-f1d115c1d46d","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:01,977 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e1b85a8a-dff7-42cd-a497-f1d115c1d46d","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:01,978 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:01,978 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fe5a35dc-0c0f-4b61-9a07-49fe403f4ecb","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:01,980 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fe5a35dc-0c0f-4b61-9a07-49fe403f4ecb","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:01,981 minium page#716 _get_elements_by_css] try to get elements: view
[D 2025-07-30 10:35:01,981 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"ce6e5ded-1038-4a4c-9ee4-84a50dba647d","method":"Page.getElements","params":{"selector":"view","pageId":2}}
[D 2025-07-30 10:35:01,989 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"ce6e5ded-1038-4a4c-9ee4-84a50dba647d","result":{"elements":[{"elementId":"fd02b6bf-ef7b-4b78-ab45-9811dbe58dfc","tagName":"view"},{"elementId":"ad45d734-385f-46db-bdda-2a72792a34ed","tagName":"view"},{"elementId":"309568af-fe3e-4a77-b99f-8477b66e66a6","tagName":"view"},{"elementId":"b0c94023-6875-4aff-9e74-13477fd70b46","tagName":"view"},{"elementId":"d9c25d80-26ab-4061-8c04-cd02fabbc1e4","tagName":"view"},{"elementId":"a446a70b-8b0a-474b-afa5-1fe16c817b8b","tagName":"view"},{"elementId":"d0dec377-86
[I 2025-07-30 10:35:01,990 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A3BE00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A8D6D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A8D950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A33E10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A33820>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD3AD8950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A0B790>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A0B570>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A89850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A89F50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A477A0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EB8500>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A6B5B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A6BAF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A76F70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A1A810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A1A8D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A828E0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4A82D00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA53B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA59F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA58B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA56D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA51D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5630>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4EB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5A90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5B30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5BD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5C70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5D10>]
[I 2025-07-30 10:35:01,991 minium page#716 _get_elements_by_css] try to get elements: text
[D 2025-07-30 10:35:01,991 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b6e3c3a1-7d80-4859-9d6e-aefc02475aba","method":"Page.getElements","params":{"selector":"text","pageId":2}}
[D 2025-07-30 10:35:01,995 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b6e3c3a1-7d80-4859-9d6e-aefc02475aba","result":{"elements":[{"elementId":"d04d2b00-2ccf-4a31-8999-cadca4aa04c5","tagName":"text"},{"elementId":"9e180d92-a547-497c-90e3-891366461a4e","tagName":"text"},{"elementId":"40f9ac82-afea-44fd-bf2e-a5e3faf83e59","tagName":"text"},{"elementId":"377f48cc-9940-47f3-8173-9dce1e36a44f","tagName":"text"},{"elementId":"4b3c1247-1453-4bc8-9b7c-f9860ac90b36","tagName":"text"}]}}
[I 2025-07-30 10:35:01,995 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5DB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA60D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5EF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA62B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA6170>]
[I 2025-07-30 10:35:01,996 minium page#716 _get_elements_by_css] try to get elements: image
[D 2025-07-30 10:35:01,996 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"e1124f7d-f859-489e-90d7-d53779331844","method":"Page.getElements","params":{"selector":"image","pageId":2}}
[D 2025-07-30 10:35:01,999 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"e1124f7d-f859-489e-90d7-d53779331844","result":{"elements":[{"elementId":"a364fda7-f1ea-4be9-b7bc-8cdf7c84f690","tagName":"image"},{"elementId":"d4d1a591-bdfb-4543-beb6-b6581139b13d","tagName":"image"},{"elementId":"0cc8f2d8-a82c-459f-91dc-50b7dfbf1a75","tagName":"image"}]}}
[I 2025-07-30 10:35:02,001 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA51D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA4190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000001ECD4EA5630>]
[I 2025-07-30 10:35:02,002 minium page#716 _get_elements_by_css] try to get elements: page
[D 2025-07-30 10:35:02,003 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"1e5c7180-5fd2-4d5d-a17e-2b7443e9cafd","method":"Page.getElements","params":{"selector":"page","pageId":2}}
[D 2025-07-30 10:35:02,007 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"1e5c7180-5fd2-4d5d-a17e-2b7443e9cafd","result":{"elements":[{"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","tagName":"body","nodeId":"d143550e"}]}}
[I 2025-07-30 10:35:02,007 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.CustomElement object at 0x000001ECD4A3BE00>]
[D 2025-07-30 10:35:02,008 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"fe650d17-6a22-48a1-ad95-9ff62a12f257","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","pageId":2,"nodeId":"d143550e"}}
[D 2025-07-30 10:35:02,011 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"fe650d17-6a22-48a1-ad95-9ff62a12f257","result":{"properties":["伦少，欢迎\n今日菜单已开放点菜，快来选你喜欢的菜吧～\n小李点了菜品：小菜\n推荐菜单\n热门菜品推荐\n去点菜 \n红烧肉\n小菜\n留言\n（暂无留言）\n更多 \n暂无留言\n点击开始第一条留言"]}}
[D 2025-07-30 10:35:02,011 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"f7e68235-a3e6-46c4-a653-545fecad63cc","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,101 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f7e68235-a3e6-46c4-a653-545fecad63cc","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXnAHEWdPv75VPUxx/vOe+S+7xNCEAgggoJyR0U5IiiLiKAoX1c89rvLquu64nr8VuS7LOiKeCCKcixyr6CAFwYhISGEBHKR5H1zvO+b95yjr6r6/VHdPT09PfPO+yYhgdQT6PeZmurqqp6pZz71VHU3Wo4HAACAiEIIxRVXXPEDzEOVUVBQUDgYIACAiOFrxRVXXPEDyw9WLBMGSwoKCm8WRAXiAIIkHmCkHABEAIhIzOGjpoorrviwPOy/YV8+MOXvZyyjYhYFhbc89jPG0Uaxj1IWBYUjCtEuPwrFGZnKNKIvSoMUFN7UqK8jsoOPSGvKvgxEd0viQohaeUR0LDdcOYorrvjhzEUNYyaWp/EyG/VlEiOUW
[I 2025-07-30 10:35:02,103 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:02,104 minium minitest#799 _miniTearDown] =========Current case Down: test_03_home_page_elements=========
[I 2025-07-30 10:35:02,104 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:02,105 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"562c151b-ab8e-4686-84a0-a02d087671b7","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,184 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"562c151b-ab8e-4686-84a0-a02d087671b7","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//db1ccc7807ct/3CSHhCEEEFYRwxAsICMIiKgjKz5Vld3+r67Wsoqu/XWSXhVXxRBRFEEGOFRHwQiIEEkISIBc5Xo73Xt4x781MX1X1+6O6e3p6eubNe0lIIPUJ9PtMTXV1Vc/UZ771qeputBwPAAAAEYUQiiuuuOIHmYcqo6CgoHAoQAAAEcPXiiuuuOIHlx+qWCYMlhQUFN4siArEQQRJPMBwOQCIABCRmCNHTRVXXPEhedh/w758cMo/wFhGxSwKCm95HGCMo41gH6UsCgpHFaJdfgSKMzyVaURflAYpKLypUV9HZAcfltaUfRmI7pbEhRC18ojoWG6ochRXXPEjmYsaxkwsT+NlNurLJEYot
[I 2025-07-30 10:35:02,186 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:02,186 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:35:02,228 minium minitest#432 _miniSetUp] =========Current case: test_04_order_page_functionality=========
[I 2025-07-30 10:35:02,228 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_04_order_page_functionality
[I 2025-07-30 10:35:02,228 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:02,229 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:02,229 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:02,229 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:02,229 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:02,230 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:02,231 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"0e479a64-7780-4a4a-a529-a0f6f1dc9d07","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,325 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"0e479a64-7780-4a4a-a529-a0f6f1dc9d07","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXeAHMWZNv6+VR0m7M4G5SyUEQiRBBgL23BkOWGyjTHGYDhzfMbhvjO+s30c9jl8Z86/4+Bsg882xsYmHAYMnAEDThgMAoSQBCghaVdhd7VhdkKnqvr9Ud09PT09s7MrCQlUj6D3mZrq6qqeqWfeeqq6Gy3HAwAAQEQhhOKKK674XuahyigoKCjsCxAAQMTwteKKK6743uX7KpYJgyUFBYW3C6ICsRdBEg8wWg4AIgBEJObAUVPFFVd8RB7237Av753y9zCWUTGLgsI7HnsY42hj2Ecpi4LCQYVolx+D4oxOZZrRF6VBCgpvazTWEdnBR6U1FV8GorslcSFEvTwiOpYbqRzFFVf8QOaijjETy9N8mc36MokRS
[I 2025-07-30 10:35:02,327 minium minitest#487 _miniSetUp] =========case: test_04_order_page_functionality start=========
[I 2025-07-30 10:35:03,328 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:03,329 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"5e3156cc-52a7-48a6-bd37-da34223d0f24","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:03,330 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:03,492 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:03,495 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:03,508 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:03,510 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:03,524 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:03,525 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:03,526 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:03,526 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:03,562 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842903557,"webviewId":5,"routeEventId":"5_1753842903353","renderer":"webview"},1753842903558]}}
[D 2025-07-30 10:35:03,563 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5e3156cc-52a7-48a6-bd37-da34223d0f24","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:03,564 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842903557, 'webviewId': 5, 'routeEventId': '5_1753842903353', 'renderer': 'webview'}, 1753842903558]}
[I 2025-07-30 10:35:03,564 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 5e3156cc-52a7-48a6-bd37-da34223d0f24
[D 2025-07-30 10:35:06,565 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"9ca79f2b-12db-4e3c-aca4-81ab375f8cc4","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:06,569 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"9ca79f2b-12db-4e3c-aca4-81ab375f8cc4","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:35:06,569 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:06,570 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"80e4da19-88e9-4530-a237-a17c29eb4458","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:06,572 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"80e4da19-88e9-4530-a237-a17c29eb4458","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:06,575 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:35:06,576 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5cfbe52e-7d58-47e2-905a-90b21e4cce75","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:06,701 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5cfbe52e-7d58-47e2-905a-90b21e4cce75","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:06,704 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:35:06,704 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5d6c96d6-9662-4b7d-a77b-07c156f8ec36","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":5}}
[D 2025-07-30 10:35:06,710 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5d6c96d6-9662-4b7d-a77b-07c156f8ec36","result":{"elements":[]}}
[W 2025-07-30 10:35:06,711 minium page#747 _get_elements_by_css] Could not found any element 'view[bindtap], button' you need
[D 2025-07-30 10:35:06,711 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5acaaf3c-7669-4a66-9de4-7c565bd58de5","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:06,882 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5acaaf3c-7669-4a66-9de4-7c565bd58de5","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:06,888 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:06,888 minium minitest#799 _miniTearDown] =========Current case Down: test_04_order_page_functionality=========
[I 2025-07-30 10:35:06,889 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:06,890 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"98eca09b-55c4-4b0b-97c7-4092099a6a43","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:07,016 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"98eca09b-55c4-4b0b-97c7-4092099a6a43","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:07,020 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:07,021 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:35:07,026 minium minitest#432 _miniSetUp] =========Current case: test_05_mine_page_navigation=========
[I 2025-07-30 10:35:07,026 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_05_mine_page_navigation
[I 2025-07-30 10:35:07,027 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:07,027 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:07,027 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:07,027 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:07,027 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:07,028 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:07,029 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"60d5a184-3a26-43b8-87b7-c97341371e51","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:07,147 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"60d5a184-3a26-43b8-87b7-c97341371e51","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:07,151 minium minitest#487 _miniSetUp] =========case: test_05_mine_page_navigation start=========
[I 2025-07-30 10:35:08,155 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:08,157 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"481c1eb0-2404-4afa-95b1-19bac677955e","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:08,162 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:08,420 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842908410,"webviewId":3,"routeEventId":"3_1753842908195","renderer":"webview"},1753842908413]}}
[I 2025-07-30 10:35:08,425 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842908410, 'webviewId': 3, 'routeEventId': '3_1753842908195', 'renderer': 'webview'}, 1753842908413]}
[D 2025-07-30 10:35:08,438 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"481c1eb0-2404-4afa-95b1-19bac677955e","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:08,441 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 481c1eb0-2404-4afa-95b1-19bac677955e
[D 2025-07-30 10:35:11,444 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"bf9129b0-64e5-4fe0-b963-eb288e458b41","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:11,451 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"bf9129b0-64e5-4fe0-b963-eb288e458b41","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:35:11,453 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:11,455 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"820b3a7c-0b81-4478-8734-41894516b318","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:11,461 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"820b3a7c-0b81-4478-8734-41894516b318","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:11,469 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:35:11,469 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5cf08652-1511-4ec5-a72f-f43656e0ca06","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,590 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5cf08652-1511-4ec5-a72f-f43656e0ca06","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,596 minium page#716 _get_elements_by_css] try to get elements: view[bindtap]
[D 2025-07-30 10:35:11,596 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c45c5267-3b08-4967-9dcd-53acf258782f","method":"Page.getElements","params":{"selector":"view[bindtap]","pageId":3}}
[D 2025-07-30 10:35:11,605 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c45c5267-3b08-4967-9dcd-53acf258782f","result":{"elements":[]}}
[W 2025-07-30 10:35:11,605 minium page#747 _get_elements_by_css] Could not found any element 'view[bindtap]' you need
[D 2025-07-30 10:35:11,606 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8bd431bc-bccf-4624-9545-1d3506f4be94","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,755 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8bd431bc-bccf-4624-9545-1d3506f4be94","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,756 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:11,757 minium minitest#799 _miniTearDown] =========Current case Down: test_05_mine_page_navigation=========
[I 2025-07-30 10:35:11,757 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:11,757 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"8fbe92df-3f41-4ece-b39c-3bddce278a27","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:11,905 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"8fbe92df-3f41-4ece-b39c-3bddce278a27","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:11,906 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:11,907 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:35:11,911 minium minitest#432 _miniSetUp] =========Current case: test_06_app_performance_and_stability=========
[I 2025-07-30 10:35:11,911 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_06_app_performance_and_stability
[I 2025-07-30 10:35:11,912 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:11,912 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:11,912 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:11,913 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:11,913 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:11,914 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:11,914 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"139cf7b4-6003-44e2-b1dc-428360f171d1","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:12,004 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"139cf7b4-6003-44e2-b1dc-428360f171d1","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:35:12,036 minium minitest#487 _miniSetUp] =========case: test_06_app_performance_and_stability start=========
[I 2025-07-30 10:35:13,037 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:13,038 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"f75cce4b-c65d-4d88-a4d9-bc4b523e0687","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:13,041 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:13,262 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842913259,"webviewId":2,"routeEventId":"2_1753842913060","renderer":"webview"},1753842913260]}}
[I 2025-07-30 10:35:13,264 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842913259, 'webviewId': 2, 'routeEventId': '2_1753842913060', 'renderer': 'webview'}, 1753842913260]}
[D 2025-07-30 10:35:13,268 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"f75cce4b-c65d-4d88-a4d9-bc4b523e0687","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:13,269 minium.Conn9680 connection#704 _handle_async_msg] received async msg: f75cce4b-c65d-4d88-a4d9-bc4b523e0687
[D 2025-07-30 10:35:13,772 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"a0dad50a-12d1-4e7a-8b90-e02e83a10a45","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:13,777 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"a0dad50a-12d1-4e7a-8b90-e02e83a10a45","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:13,778 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:13,779 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"49fde259-fa64-4705-b11a-de2e5ce5c5c7","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:13,783 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"49fde259-fa64-4705-b11a-de2e5ce5c5c7","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:13,789 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"524ed504-f378-44e5-ab0c-61a023871133","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:13,791 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:13,962 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:13,977 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:35:13,994 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:14,005 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:35:14,010 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:14,011 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:35:14,014 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:14,015 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:35:14,091 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842914023,"webviewId":5,"routeEventId":"5_1753842913809","renderer":"webview"},1753842914077]}}
[I 2025-07-30 10:35:14,096 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842914023, 'webviewId': 5, 'routeEventId': '5_1753842913809', 'renderer': 'webview'}, 1753842914077]}
[D 2025-07-30 10:35:14,118 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"524ed504-f378-44e5-ab0c-61a023871133","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:14,120 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 524ed504-f378-44e5-ab0c-61a023871133
[D 2025-07-30 10:35:14,622 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"03cae24d-cb2e-4295-83af-e3b203cb1327","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:14,627 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"03cae24d-cb2e-4295-83af-e3b203cb1327","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:35:14,629 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:14,630 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"4cd40a8b-f56a-42d5-862e-f3f844caea64","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:14,641 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"4cd40a8b-f56a-42d5-862e-f3f844caea64","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:14,644 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:14,649 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:35:14,943 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753842914927,"webviewId":3,"routeEventId":"3_1753842914693","renderer":"webview"},1753842914936]}}
[I 2025-07-30 10:35:14,946 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842914927, 'webviewId': 3, 'routeEventId': '3_1753842914693', 'renderer': 'webview'}, 1753842914936]}
[D 2025-07-30 10:35:14,954 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:14,956 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 3fdcf06e-af4e-4c36-83ea-9b8fe9dd5fec
[D 2025-07-30 10:35:15,458 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3d88f374-0f09-4b44-97ed-fe88e97ba6b5","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:15,461 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3d88f374-0f09-4b44-97ed-fe88e97ba6b5","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:35:15,461 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:15,461 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"c36293c5-c5e9-4911-aa65-c23aad12da51","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:15,464 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"c36293c5-c5e9-4911-aa65-c23aad12da51","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:15,465 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"eb9eb9dc-f338-4304-8fe7-5af06daca455","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:15,470 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:35:15,731 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753842915713,"webviewId":2,"routeEventId":"2_1753842915485","renderer":"webview"},1753842915722]}}
[D 2025-07-30 10:35:15,733 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"eb9eb9dc-f338-4304-8fe7-5af06daca455","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:15,735 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842915713, 'webviewId': 2, 'routeEventId': '2_1753842915485', 'renderer': 'webview'}, 1753842915722]}
[I 2025-07-30 10:35:15,736 minium.Conn9680 connection#704 _handle_async_msg] received async msg: eb9eb9dc-f338-4304-8fe7-5af06daca455
[D 2025-07-30 10:35:16,239 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"b8ad26fd-7683-4bd9-ae11-148fb7517282","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:16,240 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"b8ad26fd-7683-4bd9-ae11-148fb7517282","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:35:16,241 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:16,241 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"5a6df102-2237-4898-bf13-c17fe4e61b71","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:16,242 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"5a6df102-2237-4898-bf13-c17fe4e61b71","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:35:16,243 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"ea07110b-41a1-466e-b74b-e884c0971dca","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:16,339 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"ea07110b-41a1-466e-b74b-e884c0971dca","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAHkWZP/48VX2817wzk/u+7xDCFYIYVFzu4MGNisqiKCtfVkR3V3Y91kVXZVeW37K4KrgqoigEFRBQRA4RDEKAEJIAuUgyk2NmMsc779FXVf3+qO5+++*****************************+Vd2NluMBAAAgohBCccUVV/wA81BlFBQUFA4GCAAgYvhaccUVV/zA8oMVy4TBkoKCwlsFUYE4gCA1DzBcDgAiAEQk5vBRU8UVV3xIHo7fcCwfmPr3M5ZRMYuCwtse+xnjaCP4jFIWBYUjCtEhPwLFGZ7KNKMvSoMUFN7SaKwjcoAPS2vKvgxEP1aLCyHq7SOiudxQ9SiuuOKHMxd1jJnYPs3X2awvUzNCq
[I 2025-07-30 10:35:16,340 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:16,341 minium minitest#799 _miniTearDown] =========Current case Down: test_06_app_performance_and_stability=========
[I 2025-07-30 10:35:16,341 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:16,341 minium.Conn9680 connection#427 _safely_send] SEND > [*************]{"id":"3cca82ac-8611-452d-8173-2de58527adf6","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:16,409 minium.Conn9680 connection#660 __on_message] RECV < [*************]{"id":"3cca82ac-8611-452d-8173-2de58527adf6","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//db1cccb96V+yb3ASFcAcSwCnLHixsVb1FWlvXc366ux7rgeuyK7LK4KuKBKMqhghyKyKGIQQgQQhIgF0ney/HeyzvmzdFXVf3+qO6enp6eefNeEhJIfdDOZ2p6qqv6TX3m+/1UdTdajgcAAICIQgjFFVdc8f3MQ5VRUFBQOBAgAICI4WvFFVdc8f3LD1QsEwZLCgoKrxVEBWI/giQeYLQcAEQAiEjMoaOmiiuu+Ig8HL/hWN4/9e9jLKNiFgWF1z32McbRxvAZpSwKCocVokN+DIozOpVpRl+UBikovKbRWEfkAB+V1lR8GYh+LIkLIertI6K53Ej1KK644ocyF3WMmdg+zdfZrC+TGKHUC1tUO
[I 2025-07-30 10:35:16,410 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:16,411 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:39:04,783 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:39:04,786 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:39:04,787 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:39:04,787 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': True, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'E:\\wx-nan\\outputs', 'enable_app_log': False, 'enable_network_panel': False, 'project_path': None, 'dev_tool_path': None, 'test_port': 9420, 'mock_native_modal': {}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'framework_capture': True, 'error_capture': False, 'assert_capture': True, 'create_time': '**************'}
[I 2025-07-30 10:39:04,788 minium minium_object#83 _do_shell] de shell: where cli.bat
[I 2025-07-30 10:39:04,892 minium minium_object#101 _do_shell] err:
信息: 用提供的模式无法找到文件。


[I 2025-07-30 10:39:04,893 minium minium_object#102 _do_shell] out:

[W 2025-07-30 10:39:04,893 minium wx_minium#284 __setup_dev_tool] default dev_tool_path[C:/Program Files (x86)/Tencent/微信web开发者工具/cli.bat] not exists and command[cli.bat] not found
[I 2025-07-30 10:39:04,893 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[W 2025-07-30 10:39:04,893 minium wx_minium#783 launch_dev_tool] Can not find `project_path` in config, that means you must open dev tool by automation way first
[W 2025-07-30 10:39:04,894 minium wx_minium#787 launch_dev_tool] If you are not running command like [cli auto --project /path/to/project --auto-port 9420], you may config `project_path` or run this command first
[I 2025-07-30 10:39:04,894 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:39:09,055 minium.Conn9584 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:39:09,071 minium.Conn9584 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:39:09,102 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:39:09,107 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:39:13,264 minium.Conn9136 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:39:13,279 minium.Conn9136 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:39:13,280 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:39:13,284 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[E 2025-07-30 10:39:17,421 minium.Conn2336 connection#642 _on_error] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[I 2025-07-30 10:39:17,437 minium.Conn2336 connection#377 _ws_run_forever] websocket run forever shutdown
[E 2025-07-30 10:39:17,469 minium wx_minium#848 connect_dev_tool] [WinError 10061] 由于目标计算机积极拒绝，无法连接。
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\wx_minium.py", line 836, in connect_dev_tool
    self.connection = Connection.create(
                      ~~~~~~~~~~~~~~~~~^
        self.uri, timeout=self.conf.get("request_timeout")
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 168, in create
    instance = cls(uri, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 209, in __init__
    self._connect()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\minium\miniprogram\base_driver\connection.py", line 361, in _connect
    raise error_callback.result
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_app.py", line 327, in run_forever
    self.sock.connect(
    ~~~~~~~~~~~~~~~~~^
        self.url, header=self.header, cookie=self.cookie,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
        host=host, origin=origin, suppress_origin=suppress_origin,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        proxy_type=proxy_type, socket=self.prepared_socket)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_core.py", line 244, in connect
    self.sock, addrs = connect(url, self.sock_opt, proxy_info(**options),
                       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                               options.pop('socket', None))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 130, in connect
    sock = _open_socket(addrinfo_list, options.sockopt, options.timeout)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 205, in _open_socket
    raise err
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\websocket\_http.py", line 185, in _open_socket
    sock.connect(address)
    ~~~~~~~~~~~~^^^^^^^^^
ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。
[E 2025-07-30 10:39:17,473 minium wx_minium#797 launch_dev_tool] three times try to connect Dev tool has all fail ..., restart now...
[I 2025-07-30 10:39:59,165 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:39:59,168 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:39:59,168 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:39:59,169 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:39:59,170 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 10:39:59,171 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 10:40:01,358 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 10:40:01,358 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 10:40:01,359 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 10:40:01,555 minium.Conn8160 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 10:40:01,555 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"7d97c168-b138-47d1-8036-2c3d062c2b7c","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:40:01,558 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"7d97c168-b138-47d1-8036-2c3d062c2b7c","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 10:40:01,558 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"2a3f0d46-d945-403f-99f9-4c8d968238bf","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:40:01,563 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"2a3f0d46-d945-403f-99f9-4c8d968238bf","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 10:40:01,564 minium.App2864 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 10:40:01,565 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"d1a03693-fd97-4fd7-a30e-b023bc256001","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 10:40:01,568 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"d1a03693-fd97-4fd7-a30e-b023bc256001","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 10:40:01,569 minium.Conn8160 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:40:01,570 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"88ff531d-43e1-4307-a5f7-a46d6724c6f0","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 10:40:01,572 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"88ff531d-43e1-4307-a5f7-a46d6724c6f0","result":{}}
[D 2025-07-30 10:40:01,572 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"eaeaea69-2196-4a03-9f83-9a9d4689b94f","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 10:40:01,574 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"eaeaea69-2196-4a03-9f83-9a9d4689b94f","result":{}}
[D 2025-07-30 10:40:01,574 minium.App2864 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 10:40:01,575 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"74e2bbd4-a98c-40da-8884-38d621f131a6","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 10:40:01,577 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"74e2bbd4-a98c-40da-8884-38d621f131a6","result":{}}
[D 2025-07-30 10:40:01,578 minium.App2864 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 10:40:01,578 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"484395d6-95b8-477c-b7be-96d4470c26d7","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 10:40:01,581 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"484395d6-95b8-477c-b7be-96d4470c26d7","result":{}}
[D 2025-07-30 10:40:01,581 minium.App2864 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 10:40:01,582 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"e58c7d54-e755-4f74-82d9-4b7bec3efcfd","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 10:40:01,586 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"e58c7d54-e755-4f74-82d9-4b7bec3efcfd","result":{"result":false}}
[D 2025-07-30 10:40:01,587 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"3dc09030-112f-4376-b419-bf02630481f4","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 10:40:01,591 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"3dc09030-112f-4376-b419-bf02630481f4","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 10:40:01,591 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"ef441ae7-c4e3-40eb-88d6-782b583f8266","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 10:40:01,593 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"ef441ae7-c4e3-40eb-88d6-782b583f8266","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 10:40:01,593 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"6bbd2297-50f5-4163-a027-f7c95a4b11a8","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 10:40:01,595 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"6bbd2297-50f5-4163-a027-f7c95a4b11a8","result":{}}
[D 2025-07-30 10:40:01,595 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"4e9cb13c-6b77-464e-9f8e-f82173d4f61e","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 10:40:01,597 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"4e9cb13c-6b77-464e-9f8e-f82173d4f61e","result":{}}
[D 2025-07-30 10:40:01,597 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"5ebef77f-cd3c-4a54-b2a3-56f1a89662c5","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 10:40:01,599 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"5ebef77f-cd3c-4a54-b2a3-56f1a89662c5","result":{}}
[D 2025-07-30 10:40:01,600 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"5dba7170-5b01-4b3a-99f2-55880967f58a","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 10:40:01,604 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"5dba7170-5b01-4b3a-99f2-55880967f58a","result":{}}
[D 2025-07-30 10:40:01,605 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"a9f25701-21a8-412d-a434-c2eef6db7631","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 10:40:01,607 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"a9f25701-21a8-412d-a434-c2eef6db7631","result":{}}
[D 2025-07-30 10:40:01,608 minium.App2864 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 10:40:01,608 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"36762c7e-c044-4ad7-87ca-5885bea4cc6a","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 10:40:01,610 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"36762c7e-c044-4ad7-87ca-5885bea4cc6a","result":{"result":true}}
[D 2025-07-30 10:40:01,611 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"f9972ab4-a844-4ca3-a54a-61526c60b94d","method":"App.addBinding","params":{"name":"showModal_before_1753843201611"}}
[D 2025-07-30 10:40:01,613 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"f9972ab4-a844-4ca3-a54a-61526c60b94d","result":{}}
[D 2025-07-30 10:40:01,613 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"233d2eb6-f4bf-4560-b0e9-b2e8dd026585","method":"App.addBinding","params":{"name":"showModal_callback_1753843201611"}}
[D 2025-07-30 10:40:01,615 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"233d2eb6-f4bf-4560-b0e9-b2e8dd026585","result":{}}
[D 2025-07-30 10:40:01,615 minium.App2864 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:40:01,617 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"ad3f0751-159c-4291-8aa4-3100e62e0e20","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753843201611,after:undefined,callback:showModal_callback_1753843201611},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753843201611,true]}}
[D 2025-07-30 10:40:01,622 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"ad3f0751-159c-4291-8aa4-3100e62e0e20","result":{}}
[D 2025-07-30 10:40:01,623 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"2b8a2cf8-1509-4e13-adc5-49b1096c0304","method":"App.addBinding","params":{"name":"showToast_before_1753843201623"}}
[D 2025-07-30 10:40:01,626 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"2b8a2cf8-1509-4e13-adc5-49b1096c0304","result":{}}
[D 2025-07-30 10:40:01,626 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"4f2f6c54-8532-42ba-b990-5f78973177fe","method":"App.addBinding","params":{"name":"showToast_callback_1753843201623"}}
[D 2025-07-30 10:40:01,628 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"4f2f6c54-8532-42ba-b990-5f78973177fe","result":{}}
[D 2025-07-30 10:40:01,628 minium.App2864 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:40:01,628 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"dcedb579-c7b3-468e-8ab1-b9e6c87acf06","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753843201623,after:undefined,callback:showToast_callback_1753843201623},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753843201623,true]}}
[D 2025-07-30 10:40:01,630 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"dcedb579-c7b3-468e-8ab1-b9e6c87acf06","result":{}}
[I 2025-07-30 10:40:01,631 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x0000021CEC02E660>, whether should relaunch: True
[D 2025-07-30 10:40:01,631 minium.Conn8160 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:40:01,631 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"8c352f10-6c24-4e20-9cac-7dd3dad1fcb3","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:40:01,633 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"8c352f10-6c24-4e20-9cac-7dd3dad1fcb3","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 10:40:01,633 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 10:40:01,634 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"9c6e6a08-3b64-413c-b2da-edbfdcb9724a","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 10:40:01,636 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"9c6e6a08-3b64-413c-b2da-edbfdcb9724a","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 10:40:01,636 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 10:40:01,637 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 10:40:01,638 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"a41404df-bdf3-45ac-a2ad-737f567b1388","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 10:40:01,640 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"a41404df-bdf3-45ac-a2ad-737f567b1388","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 10:40:01,641 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"ad5347a1-fc93-41b2-a728-b1526c0c0525","method":"App.enableLog","params":{}}
[D 2025-07-30 10:40:01,642 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"ad5347a1-fc93-41b2-a728-b1526c0c0525","result":{}}
[D 2025-07-30 10:40:01,643 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"5c550178-bf22-46f8-bb5d-36994e6826e4","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 10:40:01,691 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"5c550178-bf22-46f8-bb5d-36994e6826e4","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 10:40:01,692 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"052dbc25-4763-489e-8a6e-a96a5fb3426e","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:40:01,696 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"052dbc25-4763-489e-8a6e-a96a5fb3426e","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 10:40:01,697 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 10:40:01,741 minium minitest#432 _miniSetUp] =========Current case: test_connection=========
[I 2025-07-30 10:40:01,741 minium minitest#435 _miniSetUp] package info: E:.wx-nan.connection_test, case info: ConnectionTest.test_connection
[I 2025-07-30 10:40:01,742 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:01,742 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:01,743 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:01,743 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:01,744 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:01,745 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:01,747 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"0f65ba4b-7774-44b2-9d50-ea2dd9044cff","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:01,893 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"0f65ba4b-7774-44b2-9d50-ea2dd9044cff","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//dbfU3PzDtzJy8JuUlCCHIbEwXljopIuASVRXBZWVZ0D8VjXQV1ZVf0tyyuCl6IINcCyrEccihCEAKEkARykuS9JO/IO+fos+r3R3X31PT0zJv3kpBg6gPp952a6uqqnq5Pf7+fqupGy/EAAAAQkTEmbWlLW9r72I5YRkJCQmJ/gAAAIkafpS1taUt739rSl5GQkNi/UCNrP8VmlNL9Wr60pS3tvbQJIe8aXSYqVEJC4t0OMfDZS6jDZxkOklwkJP76IHole1nU6FlGkouExKGAvacbgWUQISKOmnZw1LrzB3UFGFF+aUtb2vvVFoeC6snPOz4ijvRYI9Nl6vFfpI8jIfGuRj0+y4j8mnojptrcI
[I 2025-07-30 10:40:01,895 minium minitest#487 _miniSetUp] =========case: test_connection start=========
[I 2025-07-30 10:40:01,895 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 10:40:02,068 minium minitest#897 capture] capture assertIsNotNone-success.png
[D 2025-07-30 10:40:02,068 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"00c565e8-de4c-470a-9450-22d1bde39cb4","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:02,161 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"00c565e8-de4c-470a-9450-22d1bde39cb4","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHUWV/jnV293fkj15Sci+EYJAADFRUPaoiOyCyiA4jAwjOoviMo6COjIj+hsGRwU3RJBtAGUZFgm4IAjBhJAEspLkvSRvyVvv0mvV74/q7lu3b9/77ntJSCD1QfqdW7e6uqpv19fnfFXVjabtAgAAICJjTNrSlra097MdsoyEhITEgQABAEQMP0tb2tKW9v61pS8jISFxYKGG1gGKzSilB7R8aUtb2vtoE0LeNrpMWKiEhMTbHWLgs49Qh88yHCS5SEi88yB6JftY1OhZRpKLhMThgH2nG4FlECEkjrq2f9SG8/t1BRhRfmlLW9oH1BaHghrJzzs+Io70WCPTZRrxX6SPIyHxtkYjPsuI/JpGI6b63CGZR
[D 2025-07-30 10:40:02,163 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"668592fb-f541-4c36-b1d3-a61577ee8dfc","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:02,165 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"668592fb-f541-4c36-b1d3-a61577ee8dfc","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:02,168 minium.App2864 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:02,170 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"ae43494f-fe1b-46e6-89a4-3b74ef92c63a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:02,172 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"ae43494f-fe1b-46e6-89a4-3b74ef92c63a","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:02,174 minium minitest#897 capture] capture assertIsNotNone-success_104002174196.png
[D 2025-07-30 10:40:02,174 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"e871161a-a570-4a5d-9824-9faefa078d3a","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:02,262 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"e871161a-a570-4a5d-9824-9faefa078d3a","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmAHEW9//dbfc2xM3vkTjYJCTlIQkjkFoMCckdBriCIyFPxofxQ9PlUntdTQd7zp8jvIT4V8EAEuR4CAk9AQEQMQoAQckAukt3NsbvZY3aOPqt+f1R3T01Pz+zsJiGB1AfS+52a6uqqnq5Pf7+fqupG03YBAAAQkTEmbWlLW9p72A5ZRkJCQmJvgAAAIoafpS1taUt7z9rSl5GQkNi7UENrL8VmlNK9Wr60pS3t3bQJIe8YXSYsVEJC4p0OMfDZTajDZxkOklwkJN59EL2S3Sxq9CwjyUVC4kDA7tONwDKIEBJHXds/asP5/boCjCi/tKUt7b1qi0NBjeTnHR8RR3qskekyjfgv0seRkHhHoxGfZUR+TaMRU
[I 2025-07-30 10:40:02,263 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:02,264 minium minitest#799 _miniTearDown] =========Current case Down: test_connection=========
[I 2025-07-30 10:40:02,264 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:02,264 minium.Conn8160 connection#427 _safely_send] SEND > [*************]{"id":"d745f082-2ca5-4e61-84af-f51c2d7dc161","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:02,407 minium.Conn8160 connection#660 __on_message] RECV < [*************]{"id":"d745f082-2ca5-4e61-84af-f51c2d7dc161","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHEWd//dbfc39jtzJS0JCDpIQgtxiUFDuqIjcgsqyoKwsK7qH4rGugrqyK/pbFlcFL0SQawHlWA45PBCEYEJIAuQiyXtJ3pF3zJujz6rfH9XdU9PTM2/eywsJpD6Qft+pqa6u6un69Pf7qapuNG0XAAAAERlj0pa2tKU9znbIMhISEhJ7AwQAEDH8LG1pS1va42tLX0ZCQmLvQg2tvRSbUUr3avnSlra099AmhLxtdJmwUAkJibc7xMBnD6GOnGUkSHKRkHjnQfRK9rCosbOMJBcJiQMBe043AssgQkgcDW3/qE3n9+sKMKr80pa2tPeqLQ4FNZOfd3xEHO2xRqfLNOO/SB9HQuJtjWZ8llH5Nc1GTI25Q
[I 2025-07-30 10:40:02,408 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:02,409 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:40:13,460 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:40:13,461 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:40:13,461 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:40:13,462 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:40:13,504 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 10:40:13,508 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 10:40:14,858 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 10:40:14,859 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 10:40:14,860 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 10:40:15,078 minium.Conn6976 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 10:40:15,078 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"7ba23bb9-b56b-44f3-a2a5-0161c8051bfb","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:40:15,079 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"7ba23bb9-b56b-44f3-a2a5-0161c8051bfb","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 10:40:15,080 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"a6d741db-391c-4219-8730-91860d41a60e","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:40:15,088 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"a6d741db-391c-4219-8730-91860d41a60e","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 10:40:15,089 minium.App1680 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 10:40:15,090 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f85f8cb2-6dd1-4c3f-a3df-6f2fb0ce3193","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 10:40:15,092 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f85f8cb2-6dd1-4c3f-a3df-6f2fb0ce3193","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 10:40:15,093 minium.Conn6976 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:40:15,093 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"c8f6bd1f-4c71-412a-833b-7baa5e23bf6c","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 10:40:15,094 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"c8f6bd1f-4c71-412a-833b-7baa5e23bf6c","result":{}}
[D 2025-07-30 10:40:15,095 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"c5724c0d-9a7b-4456-a48a-fcbbbed29a7b","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 10:40:15,096 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"c5724c0d-9a7b-4456-a48a-fcbbbed29a7b","result":{}}
[D 2025-07-30 10:40:15,097 minium.App1680 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 10:40:15,097 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f503cc11-7e57-4549-a639-29af8adf380f","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 10:40:15,103 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f503cc11-7e57-4549-a639-29af8adf380f","result":{}}
[D 2025-07-30 10:40:15,105 minium.App1680 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 10:40:15,107 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"0ac8d594-11ba-4a40-ba57-ca50d35e8961","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 10:40:15,112 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"0ac8d594-11ba-4a40-ba57-ca50d35e8961","result":{}}
[D 2025-07-30 10:40:15,113 minium.App1680 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 10:40:15,113 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"2674c236-341f-4fde-8dc2-70535db688ec","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 10:40:15,115 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"2674c236-341f-4fde-8dc2-70535db688ec","result":{"result":false}}
[D 2025-07-30 10:40:15,117 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"5e4aad50-1c9d-4f05-9788-b465da367297","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 10:40:15,121 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"5e4aad50-1c9d-4f05-9788-b465da367297","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 10:40:15,122 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f20276c3-9951-47c3-b1dc-aa5215be40bf","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 10:40:15,125 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f20276c3-9951-47c3-b1dc-aa5215be40bf","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 10:40:15,126 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"7898e5c1-9f5c-4144-9066-7e4f94fe74be","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 10:40:15,127 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"7898e5c1-9f5c-4144-9066-7e4f94fe74be","result":{}}
[D 2025-07-30 10:40:15,128 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"b341e508-185d-46e1-a242-45f64406459a","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 10:40:15,129 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"b341e508-185d-46e1-a242-45f64406459a","result":{}}
[D 2025-07-30 10:40:15,129 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"428c58ea-5911-4fd4-b6da-c99699a88830","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 10:40:15,130 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"428c58ea-5911-4fd4-b6da-c99699a88830","result":{}}
[D 2025-07-30 10:40:15,131 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"79b9e24d-dd4c-40cc-9081-b74f90b9735f","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 10:40:15,134 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"79b9e24d-dd4c-40cc-9081-b74f90b9735f","result":{}}
[D 2025-07-30 10:40:15,135 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"c4279772-0ed6-49c1-9a3d-f2ac3f313283","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 10:40:15,137 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"c4279772-0ed6-49c1-9a3d-f2ac3f313283","result":{}}
[D 2025-07-30 10:40:15,138 minium.App1680 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 10:40:15,140 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"b81e1d96-685a-4161-ac01-bf11075df04f","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 10:40:15,143 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"b81e1d96-685a-4161-ac01-bf11075df04f","result":{"result":true}}
[D 2025-07-30 10:40:15,143 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"703d97ad-1b05-4a0c-9daa-815858a671b2","method":"App.addBinding","params":{"name":"showModal_before_1753843215143"}}
[D 2025-07-30 10:40:15,145 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"703d97ad-1b05-4a0c-9daa-815858a671b2","result":{}}
[D 2025-07-30 10:40:15,145 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"56d43368-1e6a-49f1-9c8a-fb40d79bc2c9","method":"App.addBinding","params":{"name":"showModal_callback_1753843215143"}}
[D 2025-07-30 10:40:15,146 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"56d43368-1e6a-49f1-9c8a-fb40d79bc2c9","result":{}}
[D 2025-07-30 10:40:15,147 minium.App1680 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:40:15,147 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"9539ec8c-c5fd-4322-8b37-72e4e19e0532","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753843215143,after:undefined,callback:showModal_callback_1753843215143},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753843215143,true]}}
[D 2025-07-30 10:40:15,151 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"9539ec8c-c5fd-4322-8b37-72e4e19e0532","result":{}}
[D 2025-07-30 10:40:15,152 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"60fc6a5e-b151-4322-a98d-92e1dc727459","method":"App.addBinding","params":{"name":"showToast_before_1753843215151"}}
[D 2025-07-30 10:40:15,155 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"60fc6a5e-b151-4322-a98d-92e1dc727459","result":{}}
[D 2025-07-30 10:40:15,156 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"0d5f71aa-a908-4c5b-bcec-5325e6b8e87e","method":"App.addBinding","params":{"name":"showToast_callback_1753843215151"}}
[D 2025-07-30 10:40:15,159 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"0d5f71aa-a908-4c5b-bcec-5325e6b8e87e","result":{}}
[D 2025-07-30 10:40:15,160 minium.App1680 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:40:15,160 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"4736e1f1-2561-4a2d-8b49-4a92696a2522","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753843215151,after:undefined,callback:showToast_callback_1753843215151},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753843215151,true]}}
[D 2025-07-30 10:40:15,162 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"4736e1f1-2561-4a2d-8b49-4a92696a2522","result":{}}
[I 2025-07-30 10:40:15,163 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x0000020F5A2EA660>, whether should relaunch: True
[D 2025-07-30 10:40:15,163 minium.Conn6976 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:40:15,163 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"42e318f2-479c-4358-bc13-e497855f7dce","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:40:15,165 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"42e318f2-479c-4358-bc13-e497855f7dce","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 10:40:15,167 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 10:40:15,167 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"a6bbdcb7-2bfd-47e5-b66c-1e6992e89ac0","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 10:40:15,169 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"a6bbdcb7-2bfd-47e5-b66c-1e6992e89ac0","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 10:40:15,170 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 10:40:15,170 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 10:40:15,171 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"b8f6f2ac-4278-449b-bddc-3f4a2f8ba1a3","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 10:40:15,176 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"b8f6f2ac-4278-449b-bddc-3f4a2f8ba1a3","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 10:40:15,176 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"3216ec1f-f00b-4f8b-9215-78331529ac32","method":"App.enableLog","params":{}}
[D 2025-07-30 10:40:15,178 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"3216ec1f-f00b-4f8b-9215-78331529ac32","result":{}}
[D 2025-07-30 10:40:15,179 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"d1ce3dee-4898-46a0-900a-741c51c7a334","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 10:40:15,181 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"d1ce3dee-4898-46a0-900a-741c51c7a334","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 10:40:15,182 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"81967e34-eb0d-41b0-ae9c-842874997e54","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:40:15,193 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"81967e34-eb0d-41b0-ae9c-842874997e54","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 10:40:15,194 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 10:40:15,197 minium minitest#432 _miniSetUp] =========Current case: test_01_api_connectivity_and_response=========
[I 2025-07-30 10:40:15,197 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_01_api_connectivity_and_response
[I 2025-07-30 10:40:15,198 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:15,198 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:15,198 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:15,198 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:15,198 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:15,252 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:15,253 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"5edcb5a9-f15f-40c8-8d74-19e99997508e","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:15,336 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"5edcb5a9-f15f-40c8-8d74-19e99997508e","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgXEWZNv6+dbZe7p59h5CEsIQguwijOOxxQxFRURkFYWD8dJz5zQyOyji4zPjNMP6GkVHAURFFEQZBAiM4gIoIQgIhJAGykeUmNzc3d+nby1nr/f6oc05Xnz7dt+9NQhJSD6Tu29V16lSd7nr6fZ+qOgdt1wcAAEBEIlK2spWt7H1sxyyjoKCgsD/AAAAR49fKVraylb1vbeXLKCgo7F/osbWfYjPO+X6tX9nKVvZe2oyxQ0aXiStVUFA41CEHPnsJfewiY0GRi4LCmw+yV7KXVU2cZRS5KCgcDth7upFYBhFi4mhqh2dtuXzYVoBxlVe2spW9X215KqiV8mLgI+J4zzU+XaYV/0X5OAoKhzRa8VnG5de0G
[I 2025-07-30 10:40:15,338 minium minitest#487 _miniSetUp] =========case: test_01_api_connectivity_and_response start=========
[I 2025-07-30 10:40:16,339 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:16,384 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"cda7a96f-060f-4506-a509-4deb064f05a0","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:16,392 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"cda7a96f-060f-4506-a509-4deb064f05a0","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:16,393 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:16,394 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f1a4963c-4c23-4788-b16d-49e0a5b1185b","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:16,396 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f1a4963c-4c23-4788-b16d-49e0a5b1185b","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:40:16,397 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"4ae7248d-0bde-4fe9-befd-06d3a011e156","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:40:16,398 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:40:16,406 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"4ae7248d-0bde-4fe9-befd-06d3a011e156","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:16,406 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 4ae7248d-0bde-4fe9-befd-06d3a011e156
[D 2025-07-30 10:40:31,412 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"fbf4966a-caa5-46dd-a8c9-d1a9dfcf1825","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:31,414 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"fbf4966a-caa5-46dd-a8c9-d1a9dfcf1825","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:31,414 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:31,415 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"5964e3be-9d8b-444a-83c4-a474628eea1e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:31,418 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"5964e3be-9d8b-444a-83c4-a474628eea1e","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:40:34,419 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"53f5d347-e47b-4271-b6a7-4aa083b9bea9","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:34,421 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"53f5d347-e47b-4271-b6a7-4aa083b9bea9","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:34,422 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:34,422 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"5cb4061e-e7f7-4095-a8c5-79f1cc45bf8d","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:34,424 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"5cb4061e-e7f7-4095-a8c5-79f1cc45bf8d","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:34,424 minium page#716 _get_elements_by_css] try to get elements: page
[D 2025-07-30 10:40:34,425 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"1d1f67ce-acff-4531-bff7-d1ed034912ec","method":"Page.getElements","params":{"selector":"page","pageId":2}}
[D 2025-07-30 10:40:34,427 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"1d1f67ce-acff-4531-bff7-d1ed034912ec","result":{"elements":[{"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","tagName":"body","nodeId":"d143550e"}]}}
[I 2025-07-30 10:40:34,428 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.CustomElement object at 0x0000020F5A2EBE00>]
[D 2025-07-30 10:40:34,428 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"9bff1f6d-b3ae-4485-9d5c-68ff1d9c9fb3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","pageId":2,"nodeId":"d143550e"}}
[D 2025-07-30 10:40:34,430 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"9bff1f6d-b3ae-4485-9d5c-68ff1d9c9fb3","result":{"properties":["伦少，欢迎\n今日菜单已开放点菜，快来选你喜欢的菜吧～\n小李点了菜品：小菜\n推荐菜单\n热门菜品推荐\n去点菜 \n红烧肉\n小菜\n留言\n（暂无留言）\n更多 \n暂无留言\n点击开始第一条留言"]}}
[I 2025-07-30 10:40:34,431 minium page#716 _get_elements_by_css] try to get elements: .error, .network-error, .empty-state
[D 2025-07-30 10:40:34,432 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"739e2350-3617-4385-8647-567142e9883f","method":"Page.getElements","params":{"selector":".error, .network-error, .empty-state","pageId":2}}
[D 2025-07-30 10:40:34,440 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"739e2350-3617-4385-8647-567142e9883f","result":{"elements":[]}}
[W 2025-07-30 10:40:34,440 minium page#747 _get_elements_by_css] Could not found any element '.error, .network-error, .empty-state' you need
[I 2025-07-30 10:40:34,441 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:34,441 minium minitest#799 _miniTearDown] =========Current case Down: test_01_api_connectivity_and_response=========
[I 2025-07-30 10:40:34,441 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:34,442 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"1d7280e5-cc24-4e43-8c27-0bd063be4861","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:34,539 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"1d7280e5-cc24-4e43-8c27-0bd063be4861","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgXEWV9zl1115evyXJy55AyA4hEQhBDI4w7HFDIoKgIgOC8jmiM9/MoKLj4DLjNyIzDIwCbogiCCJgYEQHUBFBSEgISYBsZHlZ3nt5S79e7lr1/VH33q6+fbtfv5fEJKR+kPtOV9etW3W769fn/Gq5aDkeAAAAIjLGpC1taUv7ANsRy0hISEgcDBAAQMTotbSlLW1pH1hb+jISEhIHF2pkHaTYjFJ6UMuXtrSlvZ82IeSI0WWiQiUkJI50iIHPfkIdPstwkOQiIfHWg+iV7GdRo2cZSS4SEkcD9p9uBJZBhIg4GtrBVZvOH9QVYET5pS1taR9UWxwKaiY/7/iIONJrjUyXacZ/kT6OhMQRjWZ8lhH5Nc1GT
[I 2025-07-30 10:40:34,540 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:34,540 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:40:34,586 minium minitest#432 _miniSetUp] =========Current case: test_02_login_and_authentication=========
[I 2025-07-30 10:40:34,587 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_02_login_and_authentication
[I 2025-07-30 10:40:34,588 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:34,589 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:34,589 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:34,589 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:34,590 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:34,594 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:34,595 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"48055cb7-918c-40fb-b6e6-e2f93e6f4cd9","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:34,680 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"48055cb7-918c-40fb-b6e6-e2f93e6f4cd9","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgXEWZNv6+dbbe75LkZk8gJCEJhCAQgkhUHPa4sYigDDIMCMrHhzr+ZtQRHQe38VNkhoFRwEERRRBEkMAIDkFFBEkCIWSBbGS5yd1yl769nLXq90edc7r69Om+fW8Sk5B6IOe+XV2nTtXprqff96nloGm7AAAAiMgYk7a0pS3t/WyHLCMhISFxIEAAABHD19KWtrSlvX9t6ctISEgcWKihdYBiM0rpAS1f2tKW9j7ahJDDRpcJC5WQkDjcIQY++wh15CwjQZKLhMTbD6JXso9FjZ1lJLlISBwJ2He6EVgGEULiaGj7V206v19XgFHll7a0pX1AbXEoqJn8vOMj4mivNTpdphn/Rfo4EhKHNZrxWUbl1zQbM
[I 2025-07-30 10:40:34,683 minium minitest#487 _miniSetUp] =========case: test_02_login_and_authentication start=========
[I 2025-07-30 10:40:35,685 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 10:40:35,686 minium.App1680 app#891 navigate_to] NavigateTo: /pages/login/index
[D 2025-07-30 10:40:35,686 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"aae6510c-0e09-4ac4-bd8d-9c3ca4425e5c","method":"App.callWxMethod","params":{"method":"navigateTo","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 10:40:35,688 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 10:40:38,779 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/login/index","query":{},"openType":"navigateTo","timeStamp":1753843238771,"webviewId":6,"routeEventId":"6_1753843238144","renderer":"webview"},1753843238772]}}
[D 2025-07-30 10:40:38,779 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:40:38,780 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:40:38,780 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/login/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843238771, 'webviewId': 6, 'routeEventId': '6_1753843238144', 'renderer': 'webview'}, 1753843238772]}
[D 2025-07-30 10:40:38,780 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:40:38,781 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:40:38,782 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"aae6510c-0e09-4ac4-bd8d-9c3ca4425e5c","result":{"result":{"errMsg":"navigateTo:ok","eventChannel":{"listener":{}}}}}
[I 2025-07-30 10:40:38,783 minium.Conn6976 connection#704 _handle_async_msg] received async msg: aae6510c-0e09-4ac4-bd8d-9c3ca4425e5c
[D 2025-07-30 10:40:40,784 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f561307e-c1d6-45ce-a486-636a8a4608ee","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:40,787 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f561307e-c1d6-45ce-a486-636a8a4608ee","result":{"pageId":6,"path":"pages/login/index","query":{}}}
[D 2025-07-30 10:40:40,787 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:40,787 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"69308374-bc32-443e-91a2-386a54e3bd86","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:40,789 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"69308374-bc32-443e-91a2-386a54e3bd86","result":{"result":{"pageId":6,"path":"pages/login/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:40,790 minium page#716 _get_elements_by_css] try to get elements: .wechat-login-btn, button[bindtap*='wechat']
[D 2025-07-30 10:40:40,790 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"67f5bb2b-f76e-4f24-9dd2-70568cd94333","method":"Page.getElements","params":{"selector":".wechat-login-btn, button[bindtap*='wechat']","pageId":6}}
[D 2025-07-30 10:40:40,795 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"67f5bb2b-f76e-4f24-9dd2-70568cd94333","result":{"elements":[]}}
[W 2025-07-30 10:40:40,795 minium page#747 _get_elements_by_css] Could not found any element '.wechat-login-btn, button[bindtap*='wechat']' you need
[I 2025-07-30 10:40:40,796 minium page#716 _get_elements_by_css] try to get elements: .tab-item, .login-tab
[D 2025-07-30 10:40:40,796 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"a325dfe9-3449-44b3-8064-81fe67b36f1d","method":"Page.getElements","params":{"selector":".tab-item, .login-tab","pageId":6}}
[D 2025-07-30 10:40:40,801 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"a325dfe9-3449-44b3-8064-81fe67b36f1d","result":{"elements":[{"elementId":"0b7d4374-58d4-4f6b-add9-a76cd4d4279a","tagName":"view"},{"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","tagName":"view"}]}}
[I 2025-07-30 10:40:40,802 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2EAF90>]
[I 2025-07-30 10:40:40,803 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='用户名'], input[placeholder*='账号']
[D 2025-07-30 10:40:40,803 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"284be491-9803-4e73-992f-c5e550098d78","method":"Page.getElements","params":{"selector":"input[placeholder*='\u7528\u6237\u540d'], input[placeholder*='\u8d26\u53f7']","pageId":6}}
[D 2025-07-30 10:40:40,808 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"284be491-9803-4e73-992f-c5e550098d78","result":{"elements":[]}}
[W 2025-07-30 10:40:40,809 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='用户名'], input[placeholder*='账号']' you need
[I 2025-07-30 10:40:40,809 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='密码']
[D 2025-07-30 10:40:40,810 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"277bcb55-eb51-44a3-880e-2cc83b89c261","method":"Page.getElements","params":{"selector":"input[placeholder*='\u5bc6\u7801']","pageId":6}}
[D 2025-07-30 10:40:40,812 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"277bcb55-eb51-44a3-880e-2cc83b89c261","result":{"elements":[]}}
[W 2025-07-30 10:40:40,812 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='密码']' you need
[I 2025-07-30 10:40:40,813 minium page#716 _get_elements_by_css] try to get elements: .tab-item[data-type='password'], .password-tab
[D 2025-07-30 10:40:40,813 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"6b22c6a5-c171-434f-a993-2aac0952138a","method":"Page.getElements","params":{"selector":".tab-item[data-type='password'], .password-tab","pageId":6}}
[D 2025-07-30 10:40:40,818 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"6b22c6a5-c171-434f-a993-2aac0952138a","result":{"elements":[{"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","tagName":"view"}]}}
[I 2025-07-30 10:40:40,820 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A341310>]
[D 2025-07-30 10:40:40,862 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"4be067b2-5bf9-493b-8150-bd0c685c1de2","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","pageId":6}}
[D 2025-07-30 10:40:40,865 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"4be067b2-5bf9-493b-8150-bd0c685c1de2","result":{"styles":["auto"]}}
[D 2025-07-30 10:40:40,867 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"80f44acf-ada4-43df-99a6-7d3a0ccca994","method":"Element.tap","params":{"elementId":"a181a5d8-05af-4c5b-94e5-b6fa7fc08300","pageId":6}}
[D 2025-07-30 10:40:40,927 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"80f44acf-ada4-43df-99a6-7d3a0ccca994","result":{"pageX":262.5,"pageY":170,"clientX":262.5,"clientY":170}}
[I 2025-07-30 10:40:43,929 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='用户名']
[D 2025-07-30 10:40:43,930 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"092a73b4-efc6-4130-a584-0505cd45b432","method":"Page.getElements","params":{"selector":"input[placeholder*='\u7528\u6237\u540d']","pageId":6}}
[D 2025-07-30 10:40:43,936 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"092a73b4-efc6-4130-a584-0505cd45b432","result":{"elements":[]}}
[W 2025-07-30 10:40:43,936 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='用户名']' you need
[I 2025-07-30 10:40:43,937 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='密码']
[D 2025-07-30 10:40:43,938 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"fb3fcc10-96ec-4d7b-9e4f-2efa03b1e317","method":"Page.getElements","params":{"selector":"input[placeholder*='\u5bc6\u7801']","pageId":6}}
[D 2025-07-30 10:40:43,942 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"fb3fcc10-96ec-4d7b-9e4f-2efa03b1e317","result":{"elements":[{"elementId":"9c138cbd-6ba2-4c5a-a23f-2f24f00f7067","tagName":"input"}]}}
[I 2025-07-30 10:40:43,943 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.FormElement object at 0x0000020F5A2EACF0>]
[D 2025-07-30 10:40:43,944 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"e70b6cc1-04fd-4a82-b890-9dd61b20f48a","method":"Element.callFunction","params":{"functionName":"input.input","args":["test123",false],"elementId":"9c138cbd-6ba2-4c5a-a23f-2f24f00f7067","pageId":6}}
[D 2025-07-30 10:40:43,954 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"e70b6cc1-04fd-4a82-b890-9dd61b20f48a","result":{}}
[I 2025-07-30 10:40:44,456 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:44,456 minium minitest#799 _miniTearDown] =========Current case Down: test_02_login_and_authentication=========
[I 2025-07-30 10:40:44,457 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:44,457 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"c2fd96b5-278a-4812-963f-d643ea0db543","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:44,519 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"c2fd96b5-278a-4812-963f-d643ea0db543","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzt3Xl8FPXdwPHvXjm4jyQkipSiBRQ5CihYUUGuIsEW1IqhWgJBAYVSwAINaFVSoRzlAQWUQCgWREFiIRG5RC1q0HCmKKJQiiCbgwKSkGOPef6Y3c1usrmQ3+b6vF++nu7OTmY2D9lPfvPb2YmhsMgmAKCMsbqfAIA6jsoAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAU
[I 2025-07-30 10:40:44,522 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:44,522 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:40:44,526 minium minitest#432 _miniSetUp] =========Current case: test_03_user_association_functionality=========
[I 2025-07-30 10:40:44,526 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_03_user_association_functionality
[I 2025-07-30 10:40:44,527 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:44,527 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:44,527 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:44,527 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:44,528 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:44,529 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:44,529 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f4a1b072-c945-41d9-af81-c98265d674be","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:44,601 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f4a1b072-c945-41d9-af81-c98265d674be","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzt3Xl8FPXdwPHvXjm4jyQkipSiBRQ5CihYUUGuIsEW1IqhWgJBAYVSwAINaFVSoRzlAQWUQCgWREFiIRG5RC1q0HCmKKJQiiCbgwKSkGOPef6Y3c1usrmQ3+b6vF++nu7OTmY2D9lPfvPb2YmhsMgmAKCMsbqfAIA6jsoAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAUIvKAFCLygBQi8oAU
[I 2025-07-30 10:40:44,603 minium minitest#487 _miniSetUp] =========case: test_03_user_association_functionality start=========
[I 2025-07-30 10:40:45,604 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:45,605 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"0cacc3c1-2a28-4b3a-a67c-599005fcbc68","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:45,610 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:45,853 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843245846,"webviewId":3,"routeEventId":"3_1753843245630","renderer":"webview"},1753843245847]}}
[I 2025-07-30 10:40:45,854 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843245846, 'webviewId': 3, 'routeEventId': '3_1753843245630', 'renderer': 'webview'}, 1753843245847]}
[D 2025-07-30 10:40:45,856 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"0cacc3c1-2a28-4b3a-a67c-599005fcbc68","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:45,856 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 0cacc3c1-2a28-4b3a-a67c-599005fcbc68
[D 2025-07-30 10:40:47,857 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"a754eb15-23f5-4dc6-95b8-279121478fd9","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:47,859 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"a754eb15-23f5-4dc6-95b8-279121478fd9","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:40:47,860 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:47,860 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"476d12fd-379e-4950-a783-e2d069abf293","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:47,862 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"476d12fd-379e-4950-a783-e2d069abf293","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:47,862 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:40:47,863 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"bd921a2f-194e-49fa-9639-963a896988c0","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:40:47,869 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"bd921a2f-194e-49fa-9639-963a896988c0","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:40:47,870 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2D6D70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2D6C40>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2AEF90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2BF680>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2BF020>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A336250>]
[D 2025-07-30 10:40:47,871 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"d6e816fa-36ef-4e60-b532-e109221fd143","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:40:47,873 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"d6e816fa-36ef-4e60-b532-e109221fd143","result":{"properties":["留言"]}}
[D 2025-07-30 10:40:47,874 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"31e4ebf4-6885-41e2-9cc8-3a82fbcddd9a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:47,876 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"31e4ebf4-6885-41e2-9cc8-3a82fbcddd9a","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:40:47,877 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f0af4cc6-cb40-4900-a892-066208d6d8a2","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:47,880 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f0af4cc6-cb40-4900-a892-066208d6d8a2","result":{"styles":["auto"]}}
[D 2025-07-30 10:40:47,880 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"31e59c20-c20a-49a9-ac88-742a116132fe","method":"Element.tap","params":{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:47,954 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/user_connection/index"}]}}
[D 2025-07-30 10:40:47,996 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"31e59c20-c20a-49a9-ac88-742a116132fe","result":{"pageX":187.5,"pageY":244,"clientX":187.5,"clientY":244}}
[D 2025-07-30 10:40:49,223 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,225 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,226 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,229 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,232 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,238 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,240 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,241 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 10:40:49,881 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/user_connection/index","query":{},"openType":"navigateTo","timeStamp":1753843249872,"webviewId":7,"routeEventId":"7_1753843249090","renderer":"webview"},1753843249874]}}
[D 2025-07-30 10:40:49,883 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:40:49,884 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/user_connection/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843249872, 'webviewId': 7, 'routeEventId': '7_1753843249090', 'renderer': 'webview'}, 1753843249874]}
[D 2025-07-30 10:40:49,885 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:40:49,886 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:40:49,886 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:40:51,997 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"cacca522-71de-42d3-952a-e0ccb452c617","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:52,003 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"cacca522-71de-42d3-952a-e0ccb452c617","result":{"pageId":7,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 10:40:52,004 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:52,006 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"85f5390d-6cef-40fb-9b2a-0f26129fa961","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:52,011 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"85f5390d-6cef-40fb-9b2a-0f26129fa961","result":{"result":{"pageId":7,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:52,013 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='搜索'], .search-input
[D 2025-07-30 10:40:52,014 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"56135063-633d-454d-bd43-be6ed45f36a6","method":"Page.getElements","params":{"selector":"input[placeholder*='\u641c\u7d22'], .search-input","pageId":7}}
[D 2025-07-30 10:40:52,028 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"56135063-633d-454d-bd43-be6ed45f36a6","result":{"elements":[]}}
[W 2025-07-30 10:40:52,029 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='搜索'], .search-input' you need
[I 2025-07-30 10:40:52,029 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:52,030 minium minitest#799 _miniTearDown] =========Current case Down: test_03_user_association_functionality=========
[I 2025-07-30 10:40:52,031 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:52,031 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"99f44436-36d8-401e-80bb-eed5d0b944f1","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:52,094 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"99f44436-36d8-401e-80bb-eed5d0b944f1","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsXXd8VUXafueU21IhIYVQkkAQZSlJEBBUBAsKqwFc2MVPRUoS2FV0d91dPlxCAOVjlbWgK00Eu4IiUUHQFbCAgCShWOhBWgKEknbvPXW+P069LbkJuUlI5pHfOPecOVOeM+eZ933n3Bs0JacCAWAAhBDGWM0DwhgjhACwnsfmvCkFnyMNKQMIwMhjr3zd/QGEQGvLyOMAea2Mnir1mPIA4MUJ4YfwQ/hpGD9TcioAQCEFVAr8Q+moVkbNK/zpeaTWqtINWKlPa00bqlZcGbxvy8ZxzzJe/fHXZ49qtK5pA1baVS/W8sg8lIBjJ/wQfgg/DeSHUoaHAQAQNuXVYWMjxRgDIGzK68NGWh5jDKqqqXlt2JpSqsdBV
[I 2025-07-30 10:40:52,095 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:52,095 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:40:52,103 minium minitest#432 _miniSetUp] =========Current case: test_04_order_flow_and_api_integration=========
[I 2025-07-30 10:40:52,104 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_04_order_flow_and_api_integration
[I 2025-07-30 10:40:52,104 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:52,104 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:52,105 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:52,105 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:52,105 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:52,106 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:52,106 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"cbdc5ac5-a67b-4a5c-ad32-e251c35872a9","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:52,164 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"cbdc5ac5-a67b-4a5c-ad32-e251c35872a9","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsXXd8VUXafueU21IhIYVQkkAQZSlJEBBUBAsKqwFc2MVPRUoS2FV0d91dPlxCAOVjlbWgK00Eu4IiUUHQFbCAgCShWOhBWgKEknbvPXW+P069LbkJuUlI5pHfOPecOVOeM+eZ933n3Bs0JacCAWAAhBDGWM0DwhgjhACwnsfmvCkFnyMNKQMIwMhjr3zd/QGEQGvLyOMAea2Mnir1mPIA4MUJ4YfwQ/hpGD9TcioAQCEFVAr8Q+moVkbNK/zpeaTWqtINWKlPa00bqlZcGbxvy8ZxzzJe/fHXZ49qtK5pA1baVS/W8sg8lIBjJ/wQfgg/DeSHUoaHAQAQNuXVYWMjxRgDIGzK68NGWh5jDKqqqXlt2JpSqsdBV
[I 2025-07-30 10:40:52,176 minium minitest#487 _miniSetUp] =========case: test_04_order_flow_and_api_integration start=========
[I 2025-07-30 10:40:53,177 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:53,178 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"7343b583-7582-46e8-b795-38d179acfc82","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:40:53,181 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:40:53,340 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:40:53,348 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:40:53,356 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:40:53,358 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:40:53,370 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:40:53,371 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:40:53,372 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:40:53,373 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:40:53,374 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:40:53,375 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:40:53,376 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:40:53,376 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:40:53,406 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:40:53,407 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:40:53,408 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:40:53,408 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:40:53,414 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843253404,"webviewId":5,"routeEventId":"5_1753843253201","renderer":"webview"},1753843253405]}}
[D 2025-07-30 10:40:53,415 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"7343b583-7582-46e8-b795-38d179acfc82","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:53,415 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843253404, 'webviewId': 5, 'routeEventId': '5_1753843253201', 'renderer': 'webview'}, 1753843253405]}
[I 2025-07-30 10:40:53,415 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 7343b583-7582-46e8-b795-38d179acfc82
[D 2025-07-30 10:40:56,418 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"9fa57463-8b08-4207-a73e-a5350d68e447","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:56,422 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"9fa57463-8b08-4207-a73e-a5350d68e447","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:40:56,424 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:56,425 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"66f2dab1-9a98-4e64-a2ff-5f8bd9739b31","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:56,430 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"66f2dab1-9a98-4e64-a2ff-5f8bd9739b31","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:56,432 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item
[D 2025-07-30 10:40:56,435 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"25bb62d3-15b5-4588-929e-611a8c197411","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item","pageId":5}}
[D 2025-07-30 10:40:56,442 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"25bb62d3-15b5-4588-929e-611a8c197411","result":{"elements":[{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","tagName":"view"},{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","tagName":"view"}]}}
[I 2025-07-30 10:40:56,443 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A336250>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2F7110>]
[I 2025-07-30 10:40:56,444 minium element#521 _get_elements_by_css] try to get elements: .add-btn, .plus-btn, button[bindtap*='add']
[D 2025-07-30 10:40:56,444 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"13e78cca-8ba6-4c9c-b386-ad531722cbd2","method":"Element.getElements","params":{"selector":".add-btn, .plus-btn, button[bindtap*='add']","elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:40:56,448 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"13e78cca-8ba6-4c9c-b386-ad531722cbd2","result":{"elements":[]}}
[D 2025-07-30 10:40:56,448 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"75939932-13a8-40c6-ac54-5af6b682ca98","method":"Element.getProperties","params":{"names":["node_id"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:40:56,455 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"75939932-13a8-40c6-ac54-5af6b682ca98","result":{"properties":[null]}}
[D 2025-07-30 10:40:56,456 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"7f7586e5-e6df-4f9c-aba8-6722b24737a8","method":"Element.getAttributes","params":{"names":["node_id"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:40:56,461 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"7f7586e5-e6df-4f9c-aba8-6722b24737a8","result":{"attributes":[null]}}
[W 2025-07-30 10:40:56,461 minium element#526 _get_elements_by_css] Could not found any element '.add-btn, .plus-btn, button[bindtap*='add']' you need
[I 2025-07-30 10:40:56,462 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:56,462 minium minitest#799 _miniTearDown] =========Current case Down: test_04_order_flow_and_api_integration=========
[I 2025-07-30 10:40:56,463 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:56,463 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"d3d3ecb5-047d-4985-b2c8-e01330b028e9","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:56,550 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"d3d3ecb5-047d-4985-b2c8-e01330b028e9","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:40:56,553 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:56,553 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:40:56,558 minium minitest#432 _miniSetUp] =========Current case: test_05_dish_management_functionality=========
[I 2025-07-30 10:40:56,559 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_05_dish_management_functionality
[I 2025-07-30 10:40:56,559 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:56,559 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:56,560 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:56,560 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:56,560 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:56,561 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:56,561 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"d95c4747-4a46-4c97-8ca1-b4f8d95b5f1d","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:56,657 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"d95c4747-4a46-4c97-8ca1-b4f8d95b5f1d","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:40:56,659 minium minitest#487 _miniSetUp] =========case: test_05_dish_management_functionality start=========
[I 2025-07-30 10:40:57,659 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:57,660 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"1eb7600a-dcdd-4c58-8d69-afe138eba6f2","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:57,664 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:57,923 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843257909,"webviewId":3,"routeEventId":"3_1753843257681","renderer":"webview"},1753843257912]}}
[I 2025-07-30 10:40:57,925 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843257909, 'webviewId': 3, 'routeEventId': '3_1753843257681', 'renderer': 'webview'}, 1753843257912]}
[D 2025-07-30 10:40:57,935 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"1eb7600a-dcdd-4c58-8d69-afe138eba6f2","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:57,936 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 1eb7600a-dcdd-4c58-8d69-afe138eba6f2
[D 2025-07-30 10:40:59,941 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"1b2d98a1-4522-4653-92ac-2f418d7580f8","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:59,942 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"1b2d98a1-4522-4653-92ac-2f418d7580f8","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:40:59,943 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:59,943 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"280713fb-74b5-4492-a377-7e5744a86d12","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:59,945 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"280713fb-74b5-4492-a377-7e5744a86d12","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:59,945 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:40:59,945 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"716765dc-cb2a-4094-9f1a-05a1baa7ac9f","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:40:59,948 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"716765dc-cb2a-4094-9f1a-05a1baa7ac9f","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:40:59,950 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2F7F20>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A316510>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3809F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A320EF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2DB050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2DAF90>]
[D 2025-07-30 10:40:59,951 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"964f5fcc-b94d-4e17-9bea-56c3a7a0a0f2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:40:59,955 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"964f5fcc-b94d-4e17-9bea-56c3a7a0a0f2","result":{"properties":["留言"]}}
[D 2025-07-30 10:40:59,956 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"0a40d0b7-847c-4de8-9ef4-8905c6f61a09","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:59,958 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"0a40d0b7-847c-4de8-9ef4-8905c6f61a09","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:40:59,959 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"3b01979b-2df2-4dd3-95f0-328004277da5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:40:59,962 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"3b01979b-2df2-4dd3-95f0-328004277da5","result":{"properties":["我的菜品"]}}
[D 2025-07-30 10:40:59,962 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"469e24b2-bc7c-4899-8f73-4a95ce9e452d","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:40:59,965 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"469e24b2-bc7c-4899-8f73-4a95ce9e452d","result":{"styles":["auto"]}}
[D 2025-07-30 10:40:59,969 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"2047b9c1-ab20-4f57-b2f6-662b93a920f2","method":"Element.tap","params":{"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:41:00,029 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/dish/my-dishes/index"}]}}
[D 2025-07-30 10:41:00,064 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"2047b9c1-ab20-4f57-b2f6-662b93a920f2","result":{"pageX":187.5,"pageY":296,"clientX":187.5,"clientY":296}}
[D 2025-07-30 10:41:01,404 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,405 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,406 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,407 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,460 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,462 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,463 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,463 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:41:01,472 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,473 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,475 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,476 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,483 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,484 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,486 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,488 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["✅ 图片加载成功:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg?w=400&h=300&q=80"]}}
[D 2025-07-30 10:41:01,824 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/dish/my-dishes/index","query":{},"openType":"navigateTo","timeStamp":1753843261812,"webviewId":8,"routeEventId":"8_1753843261136","renderer":"webview"},1753843261814]}}
[D 2025-07-30 10:41:01,824 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:41:01,825 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:41:01,825 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/dish/my-dishes/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843261812, 'webviewId': 8, 'routeEventId': '8_1753843261136', 'renderer': 'webview'}, 1753843261814]}
[D 2025-07-30 10:41:01,826 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:41:01,826 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:41:04,066 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"e532dfa4-ae08-4715-8fbf-0725decd13f5","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:41:04,070 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"e532dfa4-ae08-4715-8fbf-0725decd13f5","result":{"pageId":8,"path":"pages/dish/my-dishes/index","query":{}}}
[D 2025-07-30 10:41:04,071 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:41:04,071 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"8a55d511-1e33-4f40-a56f-65f0bfb9c876","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:41:04,074 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"8a55d511-1e33-4f40-a56f-65f0bfb9c876","result":{"result":{"pageId":8,"path":"pages/dish/my-dishes/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:41:04,074 minium page#716 _get_elements_by_css] try to get elements: .dish-item, .dish-card
[D 2025-07-30 10:41:04,075 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"6f0c7ed1-b829-4b9c-a8ab-dad0efb11029","method":"Page.getElements","params":{"selector":".dish-item, .dish-card","pageId":8}}
[D 2025-07-30 10:41:04,081 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"6f0c7ed1-b829-4b9c-a8ab-dad0efb11029","result":{"elements":[{"elementId":"fd50e1e8-f09a-43c0-af62-12b71d4acb3e","tagName":"view"}]}}
[I 2025-07-30 10:41:04,082 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3315A0>]
[I 2025-07-30 10:41:04,083 minium page#716 _get_elements_by_css] try to get elements: button, .btn
[D 2025-07-30 10:41:04,084 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"0f188e45-88cc-4df3-a39e-c2dac6de5683","method":"Page.getElements","params":{"selector":"button, .btn","pageId":8}}
[D 2025-07-30 10:41:04,089 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"0f188e45-88cc-4df3-a39e-c2dac6de5683","result":{"elements":[]}}
[W 2025-07-30 10:41:04,089 minium page#747 _get_elements_by_css] Could not found any element 'button, .btn' you need
[I 2025-07-30 10:41:04,089 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:41:04,090 minium minitest#799 _miniTearDown] =========Current case Down: test_05_dish_management_functionality=========
[I 2025-07-30 10:41:04,090 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:41:04,090 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f643440f-e8dc-4302-852b-e7efcba2c7c7","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:41:04,165 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f643440f-e8dc-4302-852b-e7efcba2c7c7","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAHHWZ//88n6OO7p47mdwJIYRDIIIIqKDcrK6Au3J4AbuogIDIoj9BXb+7uruC4NcLRAW+ysq1CwIuBFflUEFQDjkMIBCOQO5jMkdfdXyO5/dH9fR0JpNMTzITBvJ5ocW7q6qru4qpdz/P8zkK41QDAAAgIhE57bTTTo+zrruMw+FwTAQMABCx/tppp512eny1i2UcDsfEwupq8jif0047/ZbSLpZxOBwTShbLYMMap5122unx1C6WcTgcE4uryzjttNMTrF0s43A4JhQ2+i4Oh8OxHTiXcTgcE0uDyzTkUU477bTT46W3pS5THwTlcDh2NhrLuk0ixrT3SP5Cg5vG+tEOh2NS0xiO1FVmAmPymqZdhoCGD
[I 2025-07-30 10:41:04,167 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:41:04,167 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:41:04,173 minium minitest#432 _miniSetUp] =========Current case: test_06_message_and_notification_system=========
[I 2025-07-30 10:41:04,173 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_06_message_and_notification_system
[I 2025-07-30 10:41:04,173 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:41:04,174 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:41:04,174 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:41:04,174 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:41:04,174 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:41:04,226 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:41:04,230 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"1431a5cf-d15c-443d-aecc-b9fff58d37f3","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:41:04,332 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"1431a5cf-d15c-443d-aecc-b9fff58d37f3","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAHHWZ//88n6OO7p47mdwJIYRDIIIIqKDcrK6Au3J4AbuogIDIoj9BXb+7uruC4NcLRAW+ysq1CwIuBFflUEFQDjkMIBCOQO5jMkdfdXyO5/dH9fR0JpNMTzITBvJ5ocW7q6qru4qpdz/P8zkK41QDAAAgIhE57bTTTo+zrruMw+FwTAQMABCx/tppp512eny1i2UcDsfEwupq8jif0047/ZbSLpZxOBwTShbLYMMap5122unx1C6WcTgcE4uryzjttNMTrF0s43A4JhQ2+i4Oh8OxHTiXcTgcE0uDyzTkUU477bTT46W3pS5THwTlcDh2NhrLuk0ixrT3SP5Cg5vG+tEOh2NS0xiO1FVmAmPymqZdhoCGD
[I 2025-07-30 10:41:04,335 minium minitest#487 _miniSetUp] =========case: test_06_message_and_notification_system start=========
[I 2025-07-30 10:41:05,335 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:41:05,336 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"adde7fa6-23ba-46aa-a7af-df664a1f83ed","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:41:05,338 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:41:05,574 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843265570,"webviewId":3,"routeEventId":"3_1753843265355","renderer":"webview"},1753843265571]}}
[I 2025-07-30 10:41:05,574 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843265570, 'webviewId': 3, 'routeEventId': '3_1753843265355', 'renderer': 'webview'}, 1753843265571]}
[D 2025-07-30 10:41:05,577 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"adde7fa6-23ba-46aa-a7af-df664a1f83ed","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:41:05,577 minium.Conn6976 connection#704 _handle_async_msg] received async msg: adde7fa6-23ba-46aa-a7af-df664a1f83ed
[D 2025-07-30 10:41:07,578 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"42df7635-11d2-42bd-8a6e-77f59a563257","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:41:07,580 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"42df7635-11d2-42bd-8a6e-77f59a563257","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:41:07,580 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:41:07,581 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"6b4e4890-4718-4508-b174-d3d3ae58a25e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:41:07,584 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"6b4e4890-4718-4508-b174-d3d3ae58a25e","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:41:07,586 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:41:07,586 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"3f357028-4e07-4943-a31b-f5e9dcac48cc","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:41:07,590 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"3f357028-4e07-4943-a31b-f5e9dcac48cc","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:41:07,592 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3314F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398C30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398690>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A399090>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398AF0>]
[D 2025-07-30 10:41:07,593 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"3d5ba7bf-fb70-48c7-aa26-20d55f149c86","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:41:07,595 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"3d5ba7bf-fb70-48c7-aa26-20d55f149c86","result":{"properties":["留言"]}}
[D 2025-07-30 10:41:07,596 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"73d212a9-c269-46bf-baea-c4c890b2ff9e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:41:07,605 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"73d212a9-c269-46bf-baea-c4c890b2ff9e","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:41:07,606 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"14bc2421-4e5b-4a3c-bcbf-6c99769e59db","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:41:07,609 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"14bc2421-4e5b-4a3c-bcbf-6c99769e59db","result":{"properties":["我的菜品"]}}
[D 2025-07-30 10:41:07,610 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"3d896185-a8bb-4d0a-b15d-5ae934a5ae3b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:41:07,612 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"3d896185-a8bb-4d0a-b15d-5ae934a5ae3b","result":{"properties":["通知中心"]}}
[D 2025-07-30 10:41:07,614 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"5cf72a26-4d55-497e-b9f9-5cede8396388","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:41:07,622 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"5cf72a26-4d55-497e-b9f9-5cede8396388","result":{"styles":["auto"]}}
[D 2025-07-30 10:41:07,624 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"bae76717-f2a2-4c5c-9e5c-431743b6256d","method":"Element.tap","params":{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:41:07,680 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/notification_center/index"}]}}
[D 2025-07-30 10:41:07,729 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"bae76717-f2a2-4c5c-9e5c-431743b6256d","result":{"pageX":187.5,"pageY":348,"clientX":187.5,"clientY":348}}
[D 2025-07-30 10:41:09,262 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/notification_center/index","query":{},"openType":"navigateTo","timeStamp":1753843269254,"webviewId":9,"routeEventId":"9_1753843268629","renderer":"webview"},1753843269255]}}
[D 2025-07-30 10:41:09,262 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:41:09,263 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/notification_center/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843269254, 'webviewId': 9, 'routeEventId': '9_1753843268629', 'renderer': 'webview'}, 1753843269255]}
[D 2025-07-30 10:41:09,263 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:41:09,264 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:41:09,264 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:41:11,738 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"0ce81a61-d1c2-4372-925b-f2e06df0065f","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:41:11,739 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"0ce81a61-d1c2-4372-925b-f2e06df0065f","result":{"pageId":9,"path":"pages/notification_center/index","query":{}}}
[D 2025-07-30 10:41:11,740 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:41:11,740 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"f935131f-b11d-4018-ae98-be521871a7d5","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:41:11,742 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"f935131f-b11d-4018-ae98-be521871a7d5","result":{"result":{"pageId":9,"path":"pages/notification_center/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:41:11,742 minium page#716 _get_elements_by_css] try to get elements: .message-item, .notification-item
[D 2025-07-30 10:41:11,742 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"8b30ca10-e021-482d-9d5d-232a5af35e0f","method":"Page.getElements","params":{"selector":".message-item, .notification-item","pageId":9}}
[D 2025-07-30 10:41:11,747 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"8b30ca10-e021-482d-9d5d-232a5af35e0f","result":{"elements":[{"elementId":"46018972-da6a-4301-bd2f-ac2d691fa1f3","tagName":"view"},{"elementId":"ef86fbe1-a5b2-4082-a5a3-a26c74da7c48","tagName":"view"},{"elementId":"962e48db-dfdd-4d8b-ac18-ae78d20a47d0","tagName":"view"},{"elementId":"12203d39-03a8-44ff-b80a-11a3ca588dd0","tagName":"view"}]}}
[I 2025-07-30 10:41:11,747 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3989B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398370>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3984B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398FF0>]
[D 2025-07-30 10:41:11,748 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"288d96f6-3608-4f09-9c91-45ed5bbc6672","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"46018972-da6a-4301-bd2f-ac2d691fa1f3","pageId":9}}
[D 2025-07-30 10:41:11,755 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"288d96f6-3608-4f09-9c91-45ed5bbc6672","result":{"styles":["auto"]}}
[D 2025-07-30 10:41:11,756 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"14ee74b0-47b3-40d7-9e95-09bbdd86904b","method":"Element.tap","params":{"elementId":"46018972-da6a-4301-bd2f-ac2d691fa1f3","pageId":9}}
[D 2025-07-30 10:41:11,822 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"14ee74b0-47b3-40d7-9e95-09bbdd86904b","result":{"pageX":188,"pageY":233,"clientX":188,"clientY":233}}
[D 2025-07-30 10:41:12,027 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["标记通知 cmdoc7et5002mskd1z72j11jv 为已读成功"]}}
[D 2025-07-30 10:41:12,039 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["标记通知 cmdoc7et5002mskd1z72j11jv 为已读成功"]}}
[D 2025-07-30 10:41:12,040 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["标记通知 cmdoc7et5002mskd1z72j11jv 为已读成功"]}}
[D 2025-07-30 10:41:12,040 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["标记通知 cmdoc7et5002mskd1z72j11jv 为已读成功"]}}
[I 2025-07-30 10:41:13,824 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:41:13,825 minium minitest#799 _miniTearDown] =========Current case Down: test_06_message_and_notification_system=========
[I 2025-07-30 10:41:13,826 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:41:13,827 minium.Conn6976 connection#427 _safely_send] SEND > [*************]{"id":"2d6f3b19-970b-4245-a910-cc84367cc1dd","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:41:13,896 minium.Conn6976 connection#660 __on_message] RECV < [*************]{"id":"2d6f3b19-970b-4245-a910-cc84367cc1dd","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAFdWZN/ycU3frpheWBppm37eALYIQRSejWUAQCSo6TqKYIajAJKNmjO/rgqLzfkm+GJP5BJXhi0syjmJUBBFUIIkiEWmghbAj0A10X7ob6PUudW/V8/5xquqeu3bdprtvL88vM8dfn3vqnFOHqt99zu+cqssCahgAAIAxhojEiRMn3srcUhkCgUBoC3AAYIxZfxMnTpx463KKZQgEQtuCW6zjKB9x4sS7FG+LWMZyfQgEQueCrA6tBRHLyPW2hKMBg7RKncSJE29/3hb38mXFMhSzEAjdBJcT47TQl4nRlw4x9yNOnHibcZDu+jb3ZezHL9EFKeohEDogZF2wfUyacY3DftFm9QURSE0IhE6FyA2bfH4Sd
[I 2025-07-30 10:41:13,898 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:41:13,898 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:42:37,753 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 10:42:37,755 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 10:42:37,755 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 10:42:37,755 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 10:42:37,756 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 10:42:37,757 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 10:42:39,194 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 10:42:39,194 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 10:42:39,195 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 10:42:39,653 minium.Conn2448 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 10:42:39,653 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"a50c2c74-417b-42c7-b184-9f9483e54925","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:42:39,655 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"a50c2c74-417b-42c7-b184-9f9483e54925","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 10:42:39,655 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"9fe0ed47-cd41-481e-8ae3-bf6d8b161781","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:42:39,661 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"9fe0ed47-cd41-481e-8ae3-bf6d8b161781","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 10:42:39,662 minium.App7152 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 10:42:39,663 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"9c7aa496-9102-45a7-85f0-9e882022af7c","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 10:42:39,665 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"9c7aa496-9102-45a7-85f0-9e882022af7c","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 10:42:39,667 minium.Conn2448 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:42:39,667 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"e309662a-0eea-49c8-87f8-c7c669e59f3d","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 10:42:39,669 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"e309662a-0eea-49c8-87f8-c7c669e59f3d","result":{}}
[D 2025-07-30 10:42:39,670 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"b5489750-8f15-4810-8794-44b067ea5fa7","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 10:42:39,672 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"b5489750-8f15-4810-8794-44b067ea5fa7","result":{}}
[D 2025-07-30 10:42:39,672 minium.App7152 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 10:42:39,673 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"4124ffaa-f8f2-41a5-8b70-deba12c9f0d3","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 10:42:39,675 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"4124ffaa-f8f2-41a5-8b70-deba12c9f0d3","result":{}}
[D 2025-07-30 10:42:39,676 minium.App7152 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 10:42:39,676 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"eda82f16-c772-46a3-96aa-b725d5ff0c86","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 10:42:39,679 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"eda82f16-c772-46a3-96aa-b725d5ff0c86","result":{}}
[D 2025-07-30 10:42:39,679 minium.App7152 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 10:42:39,680 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"4dbf4035-ed90-4617-b872-483ce79eb2bc","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 10:42:39,682 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"4dbf4035-ed90-4617-b872-483ce79eb2bc","result":{"result":false}}
[D 2025-07-30 10:42:39,683 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"1889ed10-e2d8-4305-852f-756e0ec39925","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 10:42:39,688 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"1889ed10-e2d8-4305-852f-756e0ec39925","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 10:42:39,688 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"********-a18b-457c-bc53-bc867a0f648b","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 10:42:39,690 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"********-a18b-457c-bc53-bc867a0f648b","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 10:42:39,691 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"3b8a3fe3-c9a6-49fe-a4c1-57a8b2c5b689","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 10:42:39,694 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"3b8a3fe3-c9a6-49fe-a4c1-57a8b2c5b689","result":{}}
[D 2025-07-30 10:42:39,694 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"cf9005fc-dccf-4ee4-b7b4-d09cb071419a","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 10:42:39,696 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"cf9005fc-dccf-4ee4-b7b4-d09cb071419a","result":{}}
[D 2025-07-30 10:42:39,696 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"81f732c4-166d-44a0-91e6-a135afec3c22","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 10:42:39,698 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"81f732c4-166d-44a0-91e6-a135afec3c22","result":{}}
[D 2025-07-30 10:42:39,698 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"5242f538-89e9-4a40-b324-2e4e5b4cd2d8","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 10:42:39,702 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"5242f538-89e9-4a40-b324-2e4e5b4cd2d8","result":{}}
[D 2025-07-30 10:42:39,703 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"c57f870d-5027-4853-acbe-96e8cfd7e07e","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 10:42:39,705 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"c57f870d-5027-4853-acbe-96e8cfd7e07e","result":{}}
[D 2025-07-30 10:42:39,706 minium.App7152 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 10:42:39,706 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"6fdbeaff-91b6-481e-a811-76f1c6cc75c5","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 10:42:39,708 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"6fdbeaff-91b6-481e-a811-76f1c6cc75c5","result":{"result":true}}
[D 2025-07-30 10:42:39,709 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"981268fd-a8fb-4c27-acd2-b468bd970a9e","method":"App.addBinding","params":{"name":"showModal_before_1753843359709"}}
[D 2025-07-30 10:42:39,710 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"981268fd-a8fb-4c27-acd2-b468bd970a9e","result":{}}
[D 2025-07-30 10:42:39,711 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"4b95d3a3-d911-4d23-901e-e26df9369922","method":"App.addBinding","params":{"name":"showModal_callback_1753843359709"}}
[D 2025-07-30 10:42:39,712 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"4b95d3a3-d911-4d23-901e-e26df9369922","result":{}}
[D 2025-07-30 10:42:39,713 minium.App7152 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:42:39,713 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"9bd86de3-825d-4828-afbd-034ca1faff42","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753843359709,after:undefined,callback:showModal_callback_1753843359709},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753843359709,true]}}
[D 2025-07-30 10:42:39,715 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"9bd86de3-825d-4828-afbd-034ca1faff42","result":{}}
[D 2025-07-30 10:42:39,718 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"b7ee061b-e6c4-4311-99e6-443741cfd811","method":"App.addBinding","params":{"name":"showToast_before_1753843359718"}}
[D 2025-07-30 10:42:39,720 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"b7ee061b-e6c4-4311-99e6-443741cfd811","result":{}}
[D 2025-07-30 10:42:39,721 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"16cf4e29-894b-4348-a7c4-3d3c74a15a21","method":"App.addBinding","params":{"name":"showToast_callback_1753843359718"}}
[D 2025-07-30 10:42:39,723 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"16cf4e29-894b-4348-a7c4-3d3c74a15a21","result":{}}
[D 2025-07-30 10:42:39,723 minium.App7152 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 10:42:39,723 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"54e5ab46-03ea-47aa-afab-b21e0fad1a64","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753843359718,after:undefined,callback:showToast_callback_1753843359718},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753843359718,true]}}
[D 2025-07-30 10:42:39,725 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"54e5ab46-03ea-47aa-afab-b21e0fad1a64","result":{}}
[I 2025-07-30 10:42:39,726 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x00000225332EE660>, whether should relaunch: True
[D 2025-07-30 10:42:39,726 minium.Conn2448 connection#287 remove] remove key which is not in observers
[D 2025-07-30 10:42:39,726 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"acf0469c-c4dd-467e-bff0-9561f43602b9","method":"Tool.getInfo","params":{}}
[D 2025-07-30 10:42:39,728 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"acf0469c-c4dd-467e-bff0-9561f43602b9","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 10:42:39,728 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 10:42:39,728 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"2a421c86-97f1-47af-aca4-f8c100db5901","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 10:42:39,729 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"2a421c86-97f1-47af-aca4-f8c100db5901","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 10:42:39,729 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 10:42:39,729 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 10:42:39,730 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"95ce08b5-0083-49ae-a144-51ec71bc32d9","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 10:42:39,732 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"95ce08b5-0083-49ae-a144-51ec71bc32d9","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 10:42:39,735 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"54ef6b36-6e4a-4d83-8c0d-a9bb6983b8f9","method":"App.enableLog","params":{}}
[D 2025-07-30 10:42:39,738 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"54ef6b36-6e4a-4d83-8c0d-a9bb6983b8f9","result":{}}
[D 2025-07-30 10:42:39,739 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"ac1efc80-f40d-4874-b388-344197e23db2","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 10:42:39,741 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"ac1efc80-f40d-4874-b388-344197e23db2","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 10:42:39,742 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"efa46dd7-d639-46d3-b031-e991d39524ad","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 10:42:39,747 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"efa46dd7-d639-46d3-b031-e991d39524ad","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 10:42:39,747 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 10:42:39,750 minium minitest#432 _miniSetUp] =========Current case: test_01_basic_navigation_and_api=========
[I 2025-07-30 10:42:39,752 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_01_basic_navigation_and_api
[I 2025-07-30 10:42:39,752 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:42:39,753 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:42:39,753 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:42:39,753 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:42:39,753 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:42:39,798 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:42:39,799 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"ee3cc36d-a682-49d4-9cf3-b7964d4aabfd","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:39,879 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"ee3cc36d-a682-49d4-9cf3-b7964d4aabfd","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAFdWZN/ycU3frpheWBppm37eALYIQRSejWUAQCSo6TqKYIajAJKNmjO/rgqLzfkm+GJP5BJXhi0syjmJUBBFUIIkiEWmghbAj0A10X7ob6PUudW/V8/5xquqeu3bdprtvL88vM8dfn3vqnFOHqt99zu+cqssCahgAAIAxhojEiRMn3srcUhkCgUBoC3AAYIxZfxMnTpx463KKZQgEQtuCW6zjKB9x4sS7FG+LWMZyfQgEQueCrA6tBRHLyPW2hKMBg7RKncSJE29/3hb38mXFMhSzEAjdBJcT47TQl4nRlw4x9yNOnHibcZDu+jb3ZezHL9EFKeohEDogZF2wfUyacY3DftFm9QURSE0IhE6FyA2bfH4Sd
[I 2025-07-30 10:42:39,880 minium minitest#487 _miniSetUp] =========case: test_01_basic_navigation_and_api start=========
[I 2025-07-30 10:42:40,881 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:42:40,885 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"740ee0f7-788d-486d-b170-2cedebf10e9b","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:40,914 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"740ee0f7-788d-486d-b170-2cedebf10e9b","result":{"pageId":9,"path":"pages/notification_center/index","query":{}}}
[D 2025-07-30 10:42:40,915 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:40,919 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"d4f5c5f7-78b9-4d9c-8e23-882d86de39dc","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:40,929 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"d4f5c5f7-78b9-4d9c-8e23-882d86de39dc","result":{"result":{"pageId":9,"path":"pages/notification_center/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:42:40,930 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"356ae2eb-4243-4c9f-ad91-bd79d57c8bef","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:42:40,936 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:42:41,199 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753843361187,"webviewId":2,"routeEventId":"2_1753843360959","renderer":"webview"},1753843361190]}}
[I 2025-07-30 10:42:41,200 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843361187, 'webviewId': 2, 'routeEventId': '2_1753843360959', 'renderer': 'webview'}, 1753843361190]}
[D 2025-07-30 10:42:41,205 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"356ae2eb-4243-4c9f-ad91-bd79d57c8bef","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:41,206 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 356ae2eb-4243-4c9f-ad91-bd79d57c8bef
[D 2025-07-30 10:42:44,208 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"c97d93ec-ead7-4499-a7cf-a8a5dea7a4b2","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:44,213 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"c97d93ec-ead7-4499-a7cf-a8a5dea7a4b2","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:42:44,215 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:44,219 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"625c6a88-e1bf-41bf-bc0d-91d1c22c125f","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:44,223 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"625c6a88-e1bf-41bf-bc0d-91d1c22c125f","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:42:44,401 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:42:44,401 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"578f1c91-9f78-4a0d-a3d4-b5fb19354630","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:44,476 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"578f1c91-9f78-4a0d-a3d4-b5fb19354630","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAJEWZNv6+kWcdXX3Mfd83w3ANIA4qLvcoityKiAjCyvKp6LeKu+qyeH8r+lsWVgFXRRTlWAQEFBDwQhAGGIaZAeZiZrrn6O7po7qOPCN+f0RmVlRWVnV1z4wzMPHAZL8VFRkZkVXx1Ps+cSRajgcAAICIjDFpS1va0t7HdsQyEhISEvsDBAAQMXotbWlLW9r71pa+jISExP6FGln7KTajlO7X8qUtbWnvpU0IecvoMlGhEhISb3WIgc9eQh0+y3CQ5CIh8faD6JXsZVGjZxlJLhIShwL2nm4ElkGEiDga2sFVm84f1BVgRPmlLW1p71dbHApqJj/v+Ig40muNTJdpxn+RPo6ExFsazfgsI/Jrmo2YGnOHZ
[I 2025-07-30 10:42:44,480 minium page#716 _get_elements_by_css] try to get elements: page
[D 2025-07-30 10:42:44,485 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"e5f09a0d-f140-48c6-ab73-c6ecf1d75fff","method":"Page.getElements","params":{"selector":"page","pageId":2}}
[D 2025-07-30 10:42:44,522 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"e5f09a0d-f140-48c6-ab73-c6ecf1d75fff","result":{"elements":[{"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","tagName":"body","nodeId":"d143550e"}]}}
[I 2025-07-30 10:42:44,522 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.CustomElement object at 0x00000225332EEF90>]
[D 2025-07-30 10:42:44,523 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"f13e9e68-faba-4d62-8d8b-a27cc7c98b09","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","pageId":2,"nodeId":"d143550e"}}
[D 2025-07-30 10:42:44,525 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"f13e9e68-faba-4d62-8d8b-a27cc7c98b09","result":{"properties":["伦少，欢迎\n今日菜单已开放点菜，快来选你喜欢的菜吧～\n小李点了菜品：小菜\n推荐菜单\n热门菜品推荐\n去点菜 \n红烧肉\n小菜\n留言\n（暂无留言）\n更多 \n暂无留言\n点击开始第一条留言"]}}
[I 2025-07-30 10:42:44,526 minium page#716 _get_elements_by_css] try to get elements: view, text, image
[D 2025-07-30 10:42:44,526 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"0600c1fc-77c6-42b7-b4ca-e0089c64c935","method":"Page.getElements","params":{"selector":"view, text, image","pageId":2}}
[D 2025-07-30 10:42:44,530 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"0600c1fc-77c6-42b7-b4ca-e0089c64c935","result":{"elements":[{"elementId":"fd02b6bf-ef7b-4b78-ab45-9811dbe58dfc","tagName":"view"},{"elementId":"ad45d734-385f-46db-bdda-2a72792a34ed","tagName":"view"},{"elementId":"309568af-fe3e-4a77-b99f-8477b66e66a6","tagName":"view"},{"elementId":"b0c94023-6875-4aff-9e74-13477fd70b46","tagName":"view"},{"elementId":"d9c25d80-26ab-4061-8c04-cd02fabbc1e4","tagName":"view"},{"elementId":"a364fda7-f1ea-4be9-b7bc-8cdf7c84f690","tagName":"image"},{"elementId":"a446a70b-8
[I 2025-07-30 10:42:44,531 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332EEF90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533321D10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533321F90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332E35C0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332E36F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253339C950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253329F020>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253329F130>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253333D650>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253333DA50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225333BC230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225333BC320>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253331B690>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253331A350>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533334AE0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332BE2D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225332BE210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533331020>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533332830>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337582D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337584B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758370>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758410>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758550>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337585F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758690>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758730>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337587D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758870>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758910>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337589B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758A50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758AF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758B90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758C30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758CD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758D70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533758E10>]
[D 2025-07-30 10:42:44,535 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"12e35be9-5e01-4a3e-89ee-039ca220028a","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:42:44,538 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:42:44,625 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:42:44,627 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:42:44,628 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:42:44,629 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:42:44,630 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:42:44,636 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:42:44,638 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:42:44,638 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:42:44,639 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:42:44,640 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:42:44,646 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:42:44,647 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:42:44,647 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:42:44,648 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:42:44,648 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:42:44,652 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:42:44,653 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:42:44,655 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:42:44,656 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:42:44,657 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:42:44,795 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843364784,"webviewId":5,"routeEventId":"5_1753843364553","renderer":"webview"},1753843364788]}}
[I 2025-07-30 10:42:44,798 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843364784, 'webviewId': 5, 'routeEventId': '5_1753843364553', 'renderer': 'webview'}, 1753843364788]}
[D 2025-07-30 10:42:44,806 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"12e35be9-5e01-4a3e-89ee-039ca220028a","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:44,807 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 12e35be9-5e01-4a3e-89ee-039ca220028a
[D 2025-07-30 10:42:47,809 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"c4263db3-02f6-4991-b066-ff3ad0f01abe","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:47,817 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"c4263db3-02f6-4991-b066-ff3ad0f01abe","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:42:47,820 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:47,821 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"f721998b-bef1-4360-abea-79e348948aef","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:47,825 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"f721998b-bef1-4360-abea-79e348948aef","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:42:47,830 minium minitest#897 capture] capture assertIn-success_104247830033.png
[D 2025-07-30 10:42:47,830 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"1e6596c7-6135-42d3-a7a2-ee99d828c522","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:47,919 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"1e6596c7-6135-42d3-a7a2-ee99d828c522","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:42:47,921 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item, view[bindtap]
[D 2025-07-30 10:42:47,921 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"2a7bfaf0-82be-4937-b730-83ccd63255fb","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item, view[bindtap]","pageId":5}}
[D 2025-07-30 10:42:47,925 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"2a7bfaf0-82be-4937-b730-83ccd63255fb","result":{"elements":[{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","tagName":"view"},{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","tagName":"view"}]}}
[I 2025-07-30 10:42:47,926 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759130>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759270>]
[D 2025-07-30 10:42:47,926 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"66a0b407-677c-440a-a21f-aeda699675b5","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:47,928 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:48,181 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843368172,"webviewId":3,"routeEventId":"3_1753843367941","renderer":"webview"},1753843368175]}}
[D 2025-07-30 10:42:48,189 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"66a0b407-677c-440a-a21f-aeda699675b5","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:48,189 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843368172, 'webviewId': 3, 'routeEventId': '3_1753843367941', 'renderer': 'webview'}, 1753843368175]}
[I 2025-07-30 10:42:48,191 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 66a0b407-677c-440a-a21f-aeda699675b5
[D 2025-07-30 10:42:51,196 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"a478ce85-a456-48ce-8474-8acf691f90c7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:42:51,203 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"a478ce85-a456-48ce-8474-8acf691f90c7","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:42:51,206 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:42:51,208 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"895f5435-1c5b-4caf-b109-4fdceb24e1b2","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:42:51,214 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"895f5435-1c5b-4caf-b109-4fdceb24e1b2","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:42:51,225 minium minitest#897 capture] capture assertIn-success_104251225533.png
[D 2025-07-30 10:42:51,226 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"31f4f6a0-71f6-4caa-97ee-bf90ca3054b8","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:51,341 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"31f4f6a0-71f6-4caa-97ee-bf90ca3054b8","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:42:51,343 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:42:51,343 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"412478cc-e279-4b53-ace1-247483d4a999","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:42:51,347 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"412478cc-e279-4b53-ace1-247483d4a999","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:42:51,347 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759590>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000225337580F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759450>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759770>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000022533759A90>]
[I 2025-07-30 10:42:51,348 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:42:51,349 minium minitest#799 _miniTearDown] =========Current case Down: test_01_basic_navigation_and_api=========
[I 2025-07-30 10:42:51,350 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:42:51,350 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"93c7d27b-892c-4380-838d-d468f30accfa","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:51,455 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"93c7d27b-892c-4380-838d-d468f30accfa","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:42:51,456 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:42:51,457 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:42:51,461 minium minitest#432 _miniSetUp] =========Current case: test_02_user_interaction_features=========
[I 2025-07-30 10:42:51,462 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_02_user_interaction_features
[I 2025-07-30 10:42:51,462 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:42:51,462 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:42:51,463 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:42:51,463 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:42:51,463 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:42:51,464 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:42:51,464 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"02d91f60-f013-45c0-9e0d-ca7b03e444aa","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:42:51,552 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"02d91f60-f013-45c0-9e0d-ca7b03e444aa","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:42:51,553 minium minitest#487 _miniSetUp] =========case: test_02_user_interaction_features start=========
[I 2025-07-30 10:42:52,554 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:42:52,555 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"a5d0125c-8cf2-4aa9-a483-78aa87743bec","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:52,557 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:42:52,559 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"a5d0125c-8cf2-4aa9-a483-78aa87743bec","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:42:52,560 minium.Conn2448 connection#704 _handle_async_msg] received async msg: a5d0125c-8cf2-4aa9-a483-78aa87743bec
[D 2025-07-30 10:43:07,562 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"920d1bff-e141-4a33-93d4-c32beefcaace","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:07,564 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"920d1bff-e141-4a33-93d4-c32beefcaace","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:43:07,565 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:07,565 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"547589b4-7290-4b99-9eea-74879a8f7b55","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:07,568 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"547589b4-7290-4b99-9eea-74879a8f7b55","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:09,570 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"7775eb89-9f3b-4c9b-a073-d9d4a8303401","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:09,574 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"7775eb89-9f3b-4c9b-a073-d9d4a8303401","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:43:09,576 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:09,577 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"0ade7c10-b799-472a-a209-5125b8570c48","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:09,581 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"0ade7c10-b799-472a-a209-5125b8570c48","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:43:09,586 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:43:09,586 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"647c0279-b480-4179-9b65-203543aa54f4","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:43:09,591 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"647c0279-b480-4179-9b65-203543aa54f4","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:43:09,592 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AAD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375A8F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375A710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AB70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AC10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375ACB0>]
[D 2025-07-30 10:43:09,593 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"3510b5f4-753f-4fcd-8c30-158ecf1db3f6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,597 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"3510b5f4-753f-4fcd-8c30-158ecf1db3f6","result":{"properties":["留言"]}}
[D 2025-07-30 10:43:09,597 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"480d4610-bf7a-4a69-928a-212209e8255c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:43:09,603 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"480d4610-bf7a-4a69-928a-212209e8255c","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:43:09,604 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"894077c9-110a-456d-a486-c23a4beebfd8","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:43:09,607 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"894077c9-110a-456d-a486-c23a4beebfd8","result":{"properties":["我的菜品"]}}
[D 2025-07-30 10:43:09,607 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"166e3f9d-ab24-41bd-a609-a5308d3225b5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:43:09,610 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"166e3f9d-ab24-41bd-a609-a5308d3225b5","result":{"properties":["通知中心"]}}
[D 2025-07-30 10:43:09,611 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"b13bdf58-d99d-4656-8443-e248f7dac59b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","pageId":3}}
[D 2025-07-30 10:43:09,613 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"b13bdf58-d99d-4656-8443-e248f7dac59b","result":{"properties":["我的订单"]}}
[D 2025-07-30 10:43:09,614 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"d61cdbd6-b802-48a2-8ce9-0774565c85f4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","pageId":3}}
[D 2025-07-30 10:43:09,619 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"d61cdbd6-b802-48a2-8ce9-0774565c85f4","result":{"properties":["退出登录"]}}
[D 2025-07-30 10:43:09,620 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"051adefb-7ffe-4397-b377-00a783b82755","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,623 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"051adefb-7ffe-4397-b377-00a783b82755","result":{"properties":["留言"]}}
[D 2025-07-30 10:43:09,623 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"91c436b9-cfa8-4946-96f5-2f5b1310f42b","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,625 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"91c436b9-cfa8-4946-96f5-2f5b1310f42b","result":{"styles":["auto"]}}
[D 2025-07-30 10:43:09,626 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"2cb9fd5f-1185-49c7-a4f4-edac2da114f2","method":"Element.tap","params":{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:43:09,700 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/family_message/index"}]}}
[D 2025-07-30 10:43:09,761 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"2cb9fd5f-1185-49c7-a4f4-edac2da114f2","result":{"pageX":187.5,"pageY":192,"clientX":187.5,"clientY":192}}
[D 2025-07-30 10:43:11,518 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/family_message/index","query":{},"openType":"navigateTo","timeStamp":1753843391506,"webviewId":10,"routeEventId":"10_1753843390826","renderer":"webview"},1753843391508]}}
[D 2025-07-30 10:43:11,519 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:43:11,520 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/family_message/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843391506, 'webviewId': 10, 'routeEventId': '10_1753843390826', 'renderer': 'webview'}, 1753843391508]}
[D 2025-07-30 10:43:11,520 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:11,521 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:11,521 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:11,522 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:12,762 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"eee8d6ef-e5ef-49e7-8d91-f5ca73339d8d","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:12,771 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"eee8d6ef-e5ef-49e7-8d91-f5ca73339d8d","result":{"pageId":10,"path":"pages/family_message/index","query":{}}}
[D 2025-07-30 10:43:12,771 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:12,771 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"b5999990-6f07-4bc8-945a-50daacaac0b1","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:12,774 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"b5999990-6f07-4bc8-945a-50daacaac0b1","result":{"result":{"pageId":10,"path":"pages/family_message/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:12,775 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:12,775 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"484786ed-4007-4e86-8bd8-51521ff9920a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[]}}
[D 2025-07-30 10:43:12,777 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"484786ed-4007-4e86-8bd8-51521ff9920a","result":{"result":[{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"},{"pageId":10,"path":"pages/family_message/index","query":{},"renderer":"webview"}]}}
[I 2025-07-30 10:43:12,777 minium.App7152 app#971 navigate_back] NavigateBack from:/pages/family_message/index
[D 2025-07-30 10:43:12,778 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"f09f3d0a-c8da-41d0-a1a9-c2ca43a84aed","method":"App.callWxMethod","params":{"method":"navigateBack","args":[{"delta":1}]}}
[D 2025-07-30 10:43:12,779 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateBack_before_global","args":[{"delta":1}]}}
[D 2025-07-30 10:43:12,782 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"f09f3d0a-c8da-41d0-a1a9-c2ca43a84aed","result":{"result":{}}}
[D 2025-07-30 10:43:13,065 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"navigateBack","timeStamp":1753843393014,"webviewId":3,"routeEventId":"3_1753843392794","renderer":"webview"},1753843393020]}}
[I 2025-07-30 10:43:13,067 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'navigateBack', 'timeStamp': 1753843393014, 'webviewId': 3, 'routeEventId': '3_1753843392794', 'renderer': 'webview'}, 1753843393020]}
[I 2025-07-30 10:43:14,068 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:43:14,069 minium minitest#799 _miniTearDown] =========Current case Down: test_02_user_interaction_features=========
[I 2025-07-30 10:43:14,070 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:43:14,070 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"4e595f3b-8f9d-4d91-b36f-6cc9d8a378a3","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:14,125 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"4e595f3b-8f9d-4d91-b36f-6cc9d8a378a3","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:43:14,126 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:43:14,126 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:43:14,175 minium minitest#432 _miniSetUp] =========Current case: test_03_order_flow_simulation=========
[I 2025-07-30 10:43:14,176 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_03_order_flow_simulation
[I 2025-07-30 10:43:14,176 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:43:14,176 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:43:14,177 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:43:14,177 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:43:14,177 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:43:14,178 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:43:14,178 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"2697696b-7964-4578-8e3a-64c8677ba4e6","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:14,239 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"2697696b-7964-4578-8e3a-64c8677ba4e6","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:43:14,285 minium minitest#487 _miniSetUp] =========case: test_03_order_flow_simulation start=========
[I 2025-07-30 10:43:15,286 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:43:15,288 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"3db342ac-65f1-45b2-99ca-aa85866748f9","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:15,291 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:15,420 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:15,421 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:15,423 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:15,425 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:15,426 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:15,427 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:15,427 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:15,428 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:15,428 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:15,429 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:15,440 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:15,441 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:15,441 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:15,441 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:15,442 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:15,442 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:15,443 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:15,443 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:15,444 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:15,444 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:15,519 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843395513,"webviewId":5,"routeEventId":"5_1753843395307","renderer":"webview"},1753843395514]}}
[I 2025-07-30 10:43:15,520 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843395513, 'webviewId': 5, 'routeEventId': '5_1753843395307', 'renderer': 'webview'}, 1753843395514]}
[D 2025-07-30 10:43:15,521 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"3db342ac-65f1-45b2-99ca-aa85866748f9","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:15,521 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 3db342ac-65f1-45b2-99ca-aa85866748f9
[D 2025-07-30 10:43:18,522 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"6df627d1-2d1d-4736-835b-8d2da10fdfae","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:18,525 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"6df627d1-2d1d-4736-835b-8d2da10fdfae","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:43:18,526 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:18,526 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"b5582656-9b7d-4e3b-afeb-eac28b802dc6","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:18,529 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"b5582656-9b7d-4e3b-afeb-eac28b802dc6","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:43:18,530 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button, .dish-card, .food-item
[D 2025-07-30 10:43:18,531 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"ae299bb4-dfce-4f82-9817-b64732f241c6","method":"Page.getElements","params":{"selector":"view[bindtap], button, .dish-card, .food-item","pageId":5}}
[D 2025-07-30 10:43:18,539 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"ae299bb4-dfce-4f82-9817-b64732f241c6","result":{"elements":[{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","tagName":"view"},{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","tagName":"view"}]}}
[I 2025-07-30 10:43:18,540 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AF30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AC10>]
[D 2025-07-30 10:43:18,541 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"0c340ead-8210-4a18-a996-cbe3804d74bc","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:43:18,590 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"0c340ead-8210-4a18-a996-cbe3804d74bc","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车"]}}
[D 2025-07-30 10:43:18,591 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"b58ea13e-4de5-4e99-b219-f27f0cd12987","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:43:18,594 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"b58ea13e-4de5-4e99-b219-f27f0cd12987","result":{"styles":["auto"]}}
[D 2025-07-30 10:43:18,595 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"72d6f2b2-c2fd-4ffe-ac58-a49cd8560dc1","method":"Element.tap","params":{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:43:18,705 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"72d6f2b2-c2fd-4ffe-ac58-a49cd8560dc1","result":{"pageX":238,"pageY":82,"clientX":238,"clientY":82}}
[D 2025-07-30 10:43:18,788 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/detail/index"}]}}
[D 2025-07-30 10:43:19,765 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 菜品详情数据:",{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","description":"我是备注","ingredients":"我是原材料","cookingMethod":"直接煮","remark":"我是备注","image":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","tags":["麻"],"isPublished":true,"createdBy":"cmdo8ul870000skd1mka1k5wu","categoryId":"cmdo9ur3a000bskd1kyjwl4ez","createdAt":"2025-07-29T08:45:12.019Z","updatedAt":"2025-07-29T08:45:52.517Z","category":{"id":"cmdo9ur3a0
[D 2025-07-30 10:43:19,768 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 菜品详情数据:",{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","description":"我是备注","ingredients":"我是原材料","cookingMethod":"直接煮","remark":"我是备注","image":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","tags":["麻"],"isPublished":true,"createdBy":"cmdo8ul870000skd1mka1k5wu","categoryId":"cmdo9ur3a000bskd1kyjwl4ez","createdAt":"2025-07-29T08:45:12.019Z","updatedAt":"2025-07-29T08:45:52.517Z","category":{"id":"cmdo9ur3a0
[D 2025-07-30 10:43:19,768 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 菜品详情数据:",{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","description":"我是备注","ingredients":"我是原材料","cookingMethod":"直接煮","remark":"我是备注","image":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","tags":["麻"],"isPublished":true,"createdBy":"cmdo8ul870000skd1mka1k5wu","categoryId":"cmdo9ur3a000bskd1kyjwl4ez","createdAt":"2025-07-29T08:45:12.019Z","updatedAt":"2025-07-29T08:45:52.517Z","category":{"id":"cmdo9ur3a0
[D 2025-07-30 10:43:19,769 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 菜品详情数据:",{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","description":"我是备注","ingredients":"我是原材料","cookingMethod":"直接煮","remark":"我是备注","image":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","tags":["麻"],"isPublished":true,"createdBy":"cmdo8ul870000skd1mka1k5wu","categoryId":"cmdo9ur3a000bskd1kyjwl4ez","createdAt":"2025-07-29T08:45:12.019Z","updatedAt":"2025-07-29T08:45:52.517Z","category":{"id":"cmdo9ur3a0
[D 2025-07-30 10:43:19,770 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 菜品详情数据:",{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","description":"我是备注","ingredients":"我是原材料","cookingMethod":"直接煮","remark":"我是备注","image":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","tags":["麻"],"isPublished":true,"createdBy":"cmdo8ul870000skd1mka1k5wu","categoryId":"cmdo9ur3a000bskd1kyjwl4ez","createdAt":"2025-07-29T08:45:12.019Z","updatedAt":"2025-07-29T08:45:52.517Z","category":{"id":"cmdo9ur3a0
[D 2025-07-30 10:43:20,343 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/detail/index","query":{},"openType":"navigateTo","timeStamp":1753843400337,"webviewId":11,"routeEventId":"11_1753843399702","renderer":"webview"},1753843400338]}}
[D 2025-07-30 10:43:20,344 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[I 2025-07-30 10:43:20,344 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/detail/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843400337, 'webviewId': 11, 'routeEventId': '11_1753843399702', 'renderer': 'webview'}, 1753843400338]}
[D 2025-07-30 10:43:20,345 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:20,345 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:20,346 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:20,346 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["hook pageRouter succ"]}}
[D 2025-07-30 10:43:20,708 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"a762f8c6-f081-4833-892a-d5bc4326017b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 10:43:20,716 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"a762f8c6-f081-4833-892a-d5bc4326017b","result":{"properties":["红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 10:43:20,720 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"f2e1045f-a8df-4755-af5f-eadedb2a070b","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 10:43:20,729 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"f2e1045f-a8df-4755-af5f-eadedb2a070b","result":{"styles":["auto"]}}
[D 2025-07-30 10:43:20,730 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"9c34b94f-a6f3-4883-9491-73b4c54afdd8","method":"Element.tap","params":{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 10:43:20,794 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"9c34b94f-a6f3-4883-9491-73b4c54afdd8","result":{"pageX":238,"pageY":237,"clientX":238,"clientY":237}}
[I 2025-07-30 10:43:22,797 minium page#716 _get_elements_by_css] try to get elements: .cart, .basket, .shopping, button[bindtap*='cart']
[D 2025-07-30 10:43:22,798 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"b4758d7e-bc07-4d41-8d33-8113d867ab6c","method":"Page.getElements","params":{"selector":".cart, .basket, .shopping, button[bindtap*='cart']","pageId":5}}
[D 2025-07-30 10:43:22,805 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"b4758d7e-bc07-4d41-8d33-8113d867ab6c","result":{"elements":[]}}
[W 2025-07-30 10:43:22,806 minium page#747 _get_elements_by_css] Could not found any element '.cart, .basket, .shopping, button[bindtap*='cart']' you need
[I 2025-07-30 10:43:22,808 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:43:22,808 minium minitest#799 _miniTearDown] =========Current case Down: test_03_order_flow_simulation=========
[I 2025-07-30 10:43:22,809 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:43:22,810 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"f438c83a-f84c-4a14-a449-528bb4b22931","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:22,884 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"f438c83a-f84c-4a14-a449-528bb4b22931","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmcZWdV773Wep49nbnm6upOes7cGcnEIIgBAcNwQUA+cMEZ0dfhXj9X8ZXX4ZXPq3D1SkRArigKKlcvqEhUMBCGzJCkE5LuTs9TVXXNZz57ep613j/2qUql0+lOQvUl6ezvJ6k+Z5/nnD57nz6/Ws8aMUoM5OTk5Jw16Pv9BnJycs5xcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5JxdcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5Jxd9DN9goicjfeRk5PzfAERn9H6p6syTxaXXG5ycl5QrIjLy
[I 2025-07-30 10:43:23,194 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:43:23,195 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 10:43:23,211 minium minitest#432 _miniSetUp] =========Current case: test_04_data_persistence_and_state=========
[I 2025-07-30 10:43:23,211 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_04_data_persistence_and_state
[I 2025-07-30 10:43:23,212 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:43:23,212 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:43:23,213 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:43:23,214 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:43:23,215 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:43:23,218 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:43:23,218 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"e6743819-e704-4262-a21e-8f1e63864afb","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:23,286 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"e6743819-e704-4262-a21e-8f1e63864afb","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmcZWdV773Wep49nbnm6upOes7cGcnEIIgBAcNwQUA+cMEZ0dfhXj9X8ZXX4ZXPq3D1SkRArigKKlcvqEhUMBCGzJCkE5LuTs9TVXXNZz57ep613j/2qUql0+lOQvUl6ezvJ6k+Z5/nnD57nz6/Ws8aMUoM5OTk5Jw16Pv9BnJycs5xcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5JxdcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5Jxd9DN9goicjfeRk5PzfAERn9H6p6syTxaXXG5ycl5QrIjLy
[I 2025-07-30 10:43:23,594 minium minitest#487 _miniSetUp] =========case: test_04_data_persistence_and_state start=========
[I 2025-07-30 10:43:24,595 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:43:24,597 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"2bd78636-bd89-4c04-bd5e-08deffe2a695","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:24,603 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:24,852 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753843404843,"webviewId":2,"routeEventId":"2_1753843404628","renderer":"webview"},1753843404845]}}
[I 2025-07-30 10:43:24,853 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843404843, 'webviewId': 2, 'routeEventId': '2_1753843404628', 'renderer': 'webview'}, 1753843404845]}
[D 2025-07-30 10:43:24,853 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"2bd78636-bd89-4c04-bd5e-08deffe2a695","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:24,854 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 2bd78636-bd89-4c04-bd5e-08deffe2a695
[D 2025-07-30 10:43:25,855 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"4d1f9a74-f706-4a6d-b1af-e972590e3d24","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:25,860 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"4d1f9a74-f706-4a6d-b1af-e972590e3d24","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:43:25,861 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:25,862 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"ded3f97e-9478-4753-9ddc-7ff7128aaed2","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:25,871 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"ded3f97e-9478-4753-9ddc-7ff7128aaed2","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:25,872 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"749d1f1a-b64a-4b80-ba47-cb50c5f8d07f","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:25,875 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:26,102 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843406098,"webviewId":5,"routeEventId":"5_1753843405894","renderer":"webview"},1753843406099]}}
[I 2025-07-30 10:43:26,103 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843406098, 'webviewId': 5, 'routeEventId': '5_1753843405894', 'renderer': 'webview'}, 1753843406099]}
[D 2025-07-30 10:43:26,104 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"749d1f1a-b64a-4b80-ba47-cb50c5f8d07f","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:26,104 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 749d1f1a-b64a-4b80-ba47-cb50c5f8d07f
[D 2025-07-30 10:43:26,269 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:26,270 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:26,272 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:26,273 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:26,273 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["result",{"code":200,"message":"Success","data":{"hotpot":[{"id":"cmdoakatv000uskd1qglvkx9u","name":"小菜","img":"https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg","remark":"我是备注","material":"新鲜食材精心制作","method":"传统工艺，营养健康","description":"我是备注","ingredients":"我是原材料","tags":["numbing"],"category":{"id":"cmdo9ur3a000bskd1kyjwl4ez","name":"火锅"},"createdAt":"2025-07-29T08:45:12.019Z","createdDate":"2025-07-29T08:45:12.01
[D 2025-07-30 10:43:26,279 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:26,279 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:26,280 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:26,280 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:26,281 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["📋 全部分类包含 2 个菜品"]}}
[D 2025-07-30 10:43:26,281 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:26,282 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:26,286 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:26,287 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:26,288 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"]}}
[D 2025-07-30 10:43:26,297 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:26,298 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:26,298 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:26,299 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:26,301 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"log","args":["🎯 使用缓存图片:","https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"]}}
[D 2025-07-30 10:43:27,105 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"c516142e-3568-40c6-837a-c91e833413d1","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:27,111 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"c516142e-3568-40c6-837a-c91e833413d1","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:43:27,112 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:27,114 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"2635cd90-a880-4ce8-83b1-aff0c8b9c76e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:27,123 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"2635cd90-a880-4ce8-83b1-aff0c8b9c76e","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:27,125 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"71c56383-6491-4090-94ed-c9ad8df6d0d1","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:43:27,128 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:43:27,387 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843407375,"webviewId":3,"routeEventId":"3_1753843407149","renderer":"webview"},1753843407378]}}
[I 2025-07-30 10:43:27,391 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843407375, 'webviewId': 3, 'routeEventId': '3_1753843407149', 'renderer': 'webview'}, 1753843407378]}
[D 2025-07-30 10:43:27,395 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"71c56383-6491-4090-94ed-c9ad8df6d0d1","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:27,744 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 71c56383-6491-4090-94ed-c9ad8df6d0d1
[D 2025-07-30 10:43:28,747 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"e99d64f7-e764-4a31-9bc3-e6c86fb9e6d7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:28,750 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"e99d64f7-e764-4a31-9bc3-e6c86fb9e6d7","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:43:28,752 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:28,753 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"20c21692-45c6-49b9-a11f-6c1aad90650f","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:28,757 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"20c21692-45c6-49b9-a11f-6c1aad90650f","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:28,758 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"f0744fef-25f8-4c37-ade1-46b5dbe05da0","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:28,761 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:28,996 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753843408991,"webviewId":2,"routeEventId":"2_1753843408779","renderer":"webview"},1753843408992]}}
[I 2025-07-30 10:43:28,997 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843408991, 'webviewId': 2, 'routeEventId': '2_1753843408779', 'renderer': 'webview'}, 1753843408992]}
[D 2025-07-30 10:43:29,000 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"f0744fef-25f8-4c37-ade1-46b5dbe05da0","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:29,003 minium.Conn2448 connection#704 _handle_async_msg] received async msg: f0744fef-25f8-4c37-ade1-46b5dbe05da0
[D 2025-07-30 10:43:30,006 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"c423839c-17d8-40b4-9fd9-ba355e1819bc","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:30,008 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"c423839c-17d8-40b4-9fd9-ba355e1819bc","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:43:30,008 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:30,009 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"e739c442-911e-40d4-9b01-96eb1d751dcd","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:30,010 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"e739c442-911e-40d4-9b01-96eb1d751dcd","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:30,011 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"2da30c8d-b9ca-41b3-94d6-0a5eedd19793","method":"App.callFunction","params":{"functionDeclaration":"getCurrentPages().length","args":[]}}
[D 2025-07-30 10:43:30,012 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"0b2d930e-c59a-4495-a8d3-64939307309b","method":"App.callFunction","params":{"functionDeclaration":"wx.getStorageInfoSync()","args":[]}}
[I 2025-07-30 10:43:30,012 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:43:30,013 minium minitest#799 _miniTearDown] =========Current case Down: test_04_data_persistence_and_state=========
[D 2025-07-30 10:43:30,013 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"2da30c8d-b9ca-41b3-94d6-0a5eedd19793","error":{"message":"Arg string terminates parameters early"}}
[E 2025-07-30 10:43:30,014 minium.Conn2448 connection#668 __on_message] [2da30c8d-b9ca-41b3-94d6-0a5eedd19793]: Arg string terminates parameters early
[I 2025-07-30 10:43:30,014 minium minitest#897 capture] capture teardown.png
[I 2025-07-30 10:43:30,014 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 2da30c8d-b9ca-41b3-94d6-0a5eedd19793
[D 2025-07-30 10:43:30,014 minium.Conn2448 connection#427 _safely_send] SEND > [*************]{"id":"9d2d0e84-20b7-4faa-b096-1d1222450f64","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:30,015 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"0b2d930e-c59a-4495-a8d3-64939307309b","error":{"message":"Arg string terminates parameters early"}}
[E 2025-07-30 10:43:30,015 minium.Conn2448 connection#668 __on_message] [0b2d930e-c59a-4495-a8d3-64939307309b]: Arg string terminates parameters early
[I 2025-07-30 10:43:30,015 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 0b2d930e-c59a-4495-a8d3-64939307309b
[D 2025-07-30 10:43:30,094 minium.Conn2448 connection#660 __on_message] RECV < [*************]{"id":"9d2d0e84-20b7-4faa-b096-1d1222450f64","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgXEWVP35O3bWX12/Jvi9kDyEIhCAGFWWPiqyCMsogCCPDuOBvZhxFhxHX74jOMOCCjIooyCKyKzCAiBCEhISQBMhGlpflvZe39OvlrlW/P+re29W3b/frl8UEUh/Ifaer69atut316XM+tVy0HA8AAAARGWPSlra0pb2f7YhlJCQkJA4ECAAgYvRa2tKWtrT3ry19GQkJiQMLNbIOUGxGKT2g5Utb2tLeR5sQ8rbRZaJCJSQk3u4QA599hDp0lqEgyUVC4p0H0SvZx6L2nmUkuUhIHA7Yd7oRWAYRIuJoaAdXbTp/UFeAYeWXtrSlfUBtcSiomfy84yPicK81PF2mGf9F+jgSEm9rNOOzDMuvaTZiaswdk
[I 2025-07-30 10:43:30,096 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:43:30,096 minium basenative#63 wrapper] call BaseNative.get_start_up end 
