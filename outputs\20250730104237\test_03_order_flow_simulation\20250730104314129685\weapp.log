{"type": "log", "args": ["result", {"code": 200, "message": "Success", "data": {"hotpot": [{"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "remark": "我是备注", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "我是备注", "ingredients": "我是原材料", "tags": ["numbing"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:45:12.019Z", "createdDate": "2025-07-29T08:45:12.019Z", "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdBy": "cmdo8ul870000skd1mka1k5wu", "isMyDish": false}, {"id": "cmdo9zdr6000qskd1a197ae92", "name": "红烧肉", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg", "remark": "33", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "33", "ingredients": "我是原材料", "tags": ["sweet"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:28:56.034Z", "createdDate": "2025-07-29T08:28:56.034Z", "creator": {"id": "cmdo5jahi0000skd8xaghog3b", "name": "伦少", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo5jahi0000skd8xaghog3b.jpg"}, "createdBy": "cmdo5jahi0000skd8xaghog3b", "isMyDish": true}], "dessert": [], "jianghu": [], "noodles": [], "drinks": [], "sichuan": [], "alcohol": [], "hunan": [], "snacks": [], "cantonese": [], "soup": [], "staple": [], "vegetarian": [], "cold": []}}], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["result", {"code": 200, "message": "Success", "data": {"hotpot": [{"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "remark": "我是备注", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "我是备注", "ingredients": "我是原材料", "tags": ["numbing"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:45:12.019Z", "createdDate": "2025-07-29T08:45:12.019Z", "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdBy": "cmdo8ul870000skd1mka1k5wu", "isMyDish": false}, {"id": "cmdo9zdr6000qskd1a197ae92", "name": "红烧肉", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg", "remark": "33", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "33", "ingredients": "我是原材料", "tags": ["sweet"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:28:56.034Z", "createdDate": "2025-07-29T08:28:56.034Z", "creator": {"id": "cmdo5jahi0000skd8xaghog3b", "name": "伦少", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo5jahi0000skd8xaghog3b.jpg"}, "createdBy": "cmdo5jahi0000skd8xaghog3b", "isMyDish": true}], "dessert": [], "jianghu": [], "noodles": [], "drinks": [], "sichuan": [], "alcohol": [], "hunan": [], "snacks": [], "cantonese": [], "soup": [], "staple": [], "vegetarian": [], "cold": []}}], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["result", {"code": 200, "message": "Success", "data": {"hotpot": [{"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "remark": "我是备注", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "我是备注", "ingredients": "我是原材料", "tags": ["numbing"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:45:12.019Z", "createdDate": "2025-07-29T08:45:12.019Z", "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdBy": "cmdo8ul870000skd1mka1k5wu", "isMyDish": false}, {"id": "cmdo9zdr6000qskd1a197ae92", "name": "红烧肉", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg", "remark": "33", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "33", "ingredients": "我是原材料", "tags": ["sweet"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:28:56.034Z", "createdDate": "2025-07-29T08:28:56.034Z", "creator": {"id": "cmdo5jahi0000skd8xaghog3b", "name": "伦少", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo5jahi0000skd8xaghog3b.jpg"}, "createdBy": "cmdo5jahi0000skd8xaghog3b", "isMyDish": true}], "dessert": [], "jianghu": [], "noodles": [], "drinks": [], "sichuan": [], "alcohol": [], "hunan": [], "snacks": [], "cantonese": [], "soup": [], "staple": [], "vegetarian": [], "cold": []}}], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["result", {"code": 200, "message": "Success", "data": {"hotpot": [{"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "remark": "我是备注", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "我是备注", "ingredients": "我是原材料", "tags": ["numbing"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:45:12.019Z", "createdDate": "2025-07-29T08:45:12.019Z", "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdBy": "cmdo8ul870000skd1mka1k5wu", "isMyDish": false}, {"id": "cmdo9zdr6000qskd1a197ae92", "name": "红烧肉", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg", "remark": "33", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "33", "ingredients": "我是原材料", "tags": ["sweet"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:28:56.034Z", "createdDate": "2025-07-29T08:28:56.034Z", "creator": {"id": "cmdo5jahi0000skd8xaghog3b", "name": "伦少", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo5jahi0000skd8xaghog3b.jpg"}, "createdBy": "cmdo5jahi0000skd8xaghog3b", "isMyDish": true}], "dessert": [], "jianghu": [], "noodles": [], "drinks": [], "sichuan": [], "alcohol": [], "hunan": [], "snacks": [], "cantonese": [], "soup": [], "staple": [], "vegetarian": [], "cold": []}}], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["result", {"code": 200, "message": "Success", "data": {"hotpot": [{"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "remark": "我是备注", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "我是备注", "ingredients": "我是原材料", "tags": ["numbing"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:45:12.019Z", "createdDate": "2025-07-29T08:45:12.019Z", "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdBy": "cmdo8ul870000skd1mka1k5wu", "isMyDish": false}, {"id": "cmdo9zdr6000qskd1a197ae92", "name": "红烧肉", "img": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg", "remark": "33", "material": "新鲜食材精心制作", "method": "传统工艺，营养健康", "description": "33", "ingredients": "我是原材料", "tags": ["sweet"], "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅"}, "createdAt": "2025-07-29T08:28:56.034Z", "createdDate": "2025-07-29T08:28:56.034Z", "creator": {"id": "cmdo5jahi0000skd8xaghog3b", "name": "伦少", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo5jahi0000skd8xaghog3b.jpg"}, "createdBy": "cmdo5jahi0000skd8xaghog3b", "isMyDish": true}], "dessert": [], "jianghu": [], "noodles": [], "drinks": [], "sichuan": [], "alcohol": [], "hunan": [], "snacks": [], "cantonese": [], "soup": [], "staple": [], "vegetarian": [], "cold": []}}], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["📋 全部分类包含 2 个菜品"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["📋 全部分类包含 2 个菜品"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["📋 全部分类包含 2 个菜品"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["📋 全部分类包含 2 个菜品"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["📋 全部分类包含 2 个菜品"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["🎯 使用缓存图片:", "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753777676766.jpg"], "dt": "2025-07-30 10:43:15"}
{"type": "log", "args": ["📋 菜品详情数据:", {"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "description": "我是备注", "ingredients": "我是原材料", "cookingMethod": "直接煮", "remark": "我是备注", "image": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "tags": ["麻"], "isPublished": true, "createdBy": "cmdo8ul870000skd1mka1k5wu", "categoryId": "cmdo9ur3a000bskd1kyjwl4ez", "createdAt": "2025-07-29T08:45:12.019Z", "updatedAt": "2025-07-29T08:45:52.517Z", "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅", "createdAt": "2025-07-29T08:25:20.038Z", "updatedAt": "2025-07-29T08:25:20.038Z"}, "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdDate": "今天", "isMyDish": false}], "dt": "2025-07-30 10:43:19"}
{"type": "log", "args": ["📋 菜品详情数据:", {"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "description": "我是备注", "ingredients": "我是原材料", "cookingMethod": "直接煮", "remark": "我是备注", "image": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "tags": ["麻"], "isPublished": true, "createdBy": "cmdo8ul870000skd1mka1k5wu", "categoryId": "cmdo9ur3a000bskd1kyjwl4ez", "createdAt": "2025-07-29T08:45:12.019Z", "updatedAt": "2025-07-29T08:45:52.517Z", "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅", "createdAt": "2025-07-29T08:25:20.038Z", "updatedAt": "2025-07-29T08:25:20.038Z"}, "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdDate": "今天", "isMyDish": false}], "dt": "2025-07-30 10:43:19"}
{"type": "log", "args": ["📋 菜品详情数据:", {"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "description": "我是备注", "ingredients": "我是原材料", "cookingMethod": "直接煮", "remark": "我是备注", "image": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "tags": ["麻"], "isPublished": true, "createdBy": "cmdo8ul870000skd1mka1k5wu", "categoryId": "cmdo9ur3a000bskd1kyjwl4ez", "createdAt": "2025-07-29T08:45:12.019Z", "updatedAt": "2025-07-29T08:45:52.517Z", "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅", "createdAt": "2025-07-29T08:25:20.038Z", "updatedAt": "2025-07-29T08:25:20.038Z"}, "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdDate": "今天", "isMyDish": false}], "dt": "2025-07-30 10:43:19"}
{"type": "log", "args": ["📋 菜品详情数据:", {"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "description": "我是备注", "ingredients": "我是原材料", "cookingMethod": "直接煮", "remark": "我是备注", "image": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "tags": ["麻"], "isPublished": true, "createdBy": "cmdo8ul870000skd1mka1k5wu", "categoryId": "cmdo9ur3a000bskd1kyjwl4ez", "createdAt": "2025-07-29T08:45:12.019Z", "updatedAt": "2025-07-29T08:45:52.517Z", "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅", "createdAt": "2025-07-29T08:25:20.038Z", "updatedAt": "2025-07-29T08:25:20.038Z"}, "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdDate": "今天", "isMyDish": false}], "dt": "2025-07-30 10:43:19"}
{"type": "log", "args": ["📋 菜品详情数据:", {"id": "cmdoakatv000uskd1qglvkx9u", "name": "小菜", "description": "我是备注", "ingredients": "我是原材料", "cookingMethod": "直接煮", "remark": "我是备注", "image": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/menus/menu_1753778683873.jpg", "tags": ["麻"], "isPublished": true, "createdBy": "cmdo8ul870000skd1mka1k5wu", "categoryId": "cmdo9ur3a000bskd1kyjwl4ez", "createdAt": "2025-07-29T08:45:12.019Z", "updatedAt": "2025-07-29T08:45:52.517Z", "category": {"id": "cmdo9ur3a000bskd1kyjwl4ez", "name": "火锅", "createdAt": "2025-07-29T08:25:20.038Z", "updatedAt": "2025-07-29T08:25:20.038Z"}, "creator": {"id": "cmdo8ul870000skd1mka1k5wu", "name": "小李", "avatar": "https://cdn.jsdelivr.net/gh/2497462726/picx-images-hosting/avatars/cmdo8ul870000skd1mka1k5wu.png"}, "createdDate": "今天", "isMyDish": false}], "dt": "2025-07-30 10:43:19"}
{"type": "warn", "args": ["hook pageRouter succ"], "dt": "2025-07-30 10:43:20"}
{"type": "warn", "args": ["hook pageRouter succ"], "dt": "2025-07-30 10:43:20"}
{"type": "warn", "args": ["hook pageRouter succ"], "dt": "2025-07-30 10:43:20"}
{"type": "warn", "args": ["hook pageRouter succ"], "dt": "2025-07-30 10:43:20"}
{"type": "warn", "args": ["hook pageRouter succ"], "dt": "2025-07-30 10:43:20"}
