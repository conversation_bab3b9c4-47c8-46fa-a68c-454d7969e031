#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复后的页面导航测试
使用正确的页面路径进行验证
"""

import os
import sys
import time
import json
import unittest
from datetime import datetime

try:
    from minium import MiniTest
    print("✅ minium导入成功")
except ImportError as e:
    print(f"❌ minium导入失败: {e}")
    sys.exit(1)


class FixedNavigationTest(MiniTest):
    """修复后的页面导航测试"""
    
    def setUp(self):
        super().setUp()
        self.app = self.mini.app
        self.test_results = []
        print(f"🚀 开始测试: {self._testMethodName}")
        time.sleep(1)
    
    def tearDown(self):
        print(f"✅ 测试完成: {self._testMethodName}")
        super().tearDown()
    
    def log_result(self, test_name, success, details=""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {details}")
    
    def wait_for_page_change(self, original_path, max_wait=5):
        """等待页面变化"""
        for i in range(max_wait):
            time.sleep(1)
            current_page = self.app.get_current_page()
            if current_page.path != original_path:
                return current_page
        return None
    
    def check_page_navigation(self, current_page, expected_paths):
        """检查页面导航是否成功"""
        current_path = current_page.path
        
        for expected_path in expected_paths:
            if expected_path in current_path:
                return True
        return False
    
    def test_fixed_page_navigation(self):
        """测试修复后的页面导航"""
        print("🧪 测试修复后的页面导航")
        
        try:
            # 导航到个人中心
            self.app.switch_tab("/pages/mine/index")
            time.sleep(2)
            
            mine_page = self.app.get_current_page()
            original_path = mine_page.path
            
            # 测试我的菜品页面跳转
            self._test_my_dishes_navigation(mine_page, original_path)
            
            # 返回个人中心
            self.app.switch_tab("/pages/mine/index")
            time.sleep(2)
            
            # 测试用户关联页面跳转
            self._test_user_connection_navigation(mine_page, original_path)
            
            # 测试新增菜品页面跳转
            self._test_add_dish_navigation(mine_page, original_path)
            
        except Exception as e:
            self.log_result("页面导航测试", False, f"测试异常: {e}")
    
    def _test_my_dishes_navigation(self, page, original_path):
        """测试我的菜品页面导航"""
        try:
            # 查找我的菜品入口
            all_elements = page.get_elements("view, text, button")
            my_dishes_elements = []
            
            for element in all_elements:
                try:
                    text = element.inner_text
                    if text and ("我的菜品" in text or "菜品管理" in text):
                        my_dishes_elements.append(element)
                except:
                    continue
            
            if my_dishes_elements:
                self.log_result("我的菜品入口发现", True, f"找到{len(my_dishes_elements)}个入口")
                
                # 点击第一个入口
                my_dishes_elements[0].click()
                time.sleep(3)  # 增加等待时间
                
                # 等待页面变化
                new_page = self.wait_for_page_change(original_path, max_wait=5)
                
                if new_page:
                    # 使用正确的路径检查
                    my_dishes_paths = ["/pages/dish/my-dishes/index", "my-dishes"]
                    
                    if self.check_page_navigation(new_page, my_dishes_paths):
                        self.log_result("我的菜品页面跳转", True, f"成功跳转到: {new_page.path}")
                        
                        # 测试页面内容
                        self._test_my_dishes_page_content(new_page)
                    else:
                        self.log_result("我的菜品页面跳转", False, f"跳转到错误页面: {new_page.path}")
                else:
                    self.log_result("我的菜品页面跳转", False, "页面未发生变化")
            else:
                self.log_result("我的菜品入口发现", False, "未找到我的菜品入口")
                
        except Exception as e:
            self.log_result("我的菜品导航测试", False, f"测试异常: {e}")
    
    def _test_user_connection_navigation(self, page, original_path):
        """测试用户关联页面导航"""
        try:
            # 查找用户关联入口
            all_elements = page.get_elements("view, text, button")
            connection_elements = []
            
            for element in all_elements:
                try:
                    text = element.inner_text
                    if text and ("用户关联" in text or "关联用户" in text or "家庭成员" in text):
                        connection_elements.append(element)
                except:
                    continue
            
            if connection_elements:
                self.log_result("用户关联入口发现", True, f"找到{len(connection_elements)}个入口")
                
                # 点击第一个入口
                connection_elements[0].click()
                time.sleep(3)  # 增加等待时间
                
                # 等待页面变化
                new_page = self.wait_for_page_change(original_path, max_wait=5)
                
                if new_page:
                    # 使用正确的路径检查
                    connection_paths = ["/pages/user_connection/index", "user_connection"]
                    
                    if self.check_page_navigation(new_page, connection_paths):
                        self.log_result("用户关联页面跳转", True, f"成功跳转到: {new_page.path}")
                        
                        # 测试页面内容
                        self._test_connection_page_content(new_page)
                    else:
                        self.log_result("用户关联页面跳转", False, f"跳转到错误页面: {new_page.path}")
                else:
                    self.log_result("用户关联页面跳转", False, "页面未发生变化")
            else:
                self.log_result("用户关联入口发现", False, "未找到用户关联入口")
                
        except Exception as e:
            self.log_result("用户关联导航测试", False, f"测试异常: {e}")
    
    def _test_add_dish_navigation(self, page, original_path):
        """测试新增菜品页面导航"""
        try:
            # 查找新增菜品入口
            all_elements = page.get_elements("view, text, button")
            add_dish_elements = []
            
            for element in all_elements:
                try:
                    text = element.inner_text
                    if text and ("新增菜品" in text or "添加菜品" in text or "+" in text):
                        add_dish_elements.append(element)
                except:
                    continue
            
            if add_dish_elements:
                self.log_result("新增菜品入口发现", True, f"找到{len(add_dish_elements)}个入口")
                
                # 点击第一个入口
                add_dish_elements[0].click()
                time.sleep(3)  # 增加等待时间
                
                # 等待页面变化
                new_page = self.wait_for_page_change(original_path, max_wait=5)
                
                if new_page:
                    # 使用正确的路径检查
                    add_dish_paths = ["/pages/dish/add/index", "add"]
                    
                    if self.check_page_navigation(new_page, add_dish_paths):
                        self.log_result("新增菜品页面跳转", True, f"成功跳转到: {new_page.path}")
                        
                        # 测试页面内容
                        self._test_add_dish_page_content(new_page)
                    else:
                        self.log_result("新增菜品页面跳转", False, f"跳转到错误页面: {new_page.path}")
                else:
                    self.log_result("新增菜品页面跳转", False, "页面未发生变化")
            else:
                self.log_result("新增菜品入口发现", False, "未找到新增菜品入口")
                
        except Exception as e:
            self.log_result("新增菜品导航测试", False, f"测试异常: {e}")
    
    def _test_my_dishes_page_content(self, page):
        """测试我的菜品页面内容"""
        try:
            # 检查页面元素
            dish_items = page.get_elements(".dish-item, .dish-card, .food-item")
            self.log_result("我的菜品页面内容", True, f"找到{len(dish_items)}个菜品项")
            
            # 检查操作按钮
            action_buttons = page.get_elements("button")
            self.log_result("我的菜品操作按钮", True, f"找到{len(action_buttons)}个操作按钮")
            
        except Exception as e:
            self.log_result("我的菜品页面内容测试", False, f"测试异常: {e}")
    
    def _test_connection_page_content(self, page):
        """测试用户关联页面内容"""
        try:
            # 检查搜索功能
            search_inputs = page.get_elements("input")
            self.log_result("用户关联搜索功能", True, f"找到{len(search_inputs)}个搜索框")
            
            # 检查用户列表
            user_items = page.get_elements(".user-item, .user-card, .connection-item")
            self.log_result("用户关联列表", True, f"找到{len(user_items)}个用户项")
            
        except Exception as e:
            self.log_result("用户关联页面内容测试", False, f"测试异常: {e}")
    
    def _test_add_dish_page_content(self, page):
        """测试新增菜品页面内容"""
        try:
            # 检查表单元素
            input_elements = page.get_elements("input, textarea")
            self.log_result("新增菜品表单", True, f"找到{len(input_elements)}个输入字段")
            
            # 检查提交按钮
            submit_buttons = page.get_elements("button")
            self.log_result("新增菜品提交按钮", True, f"找到{len(submit_buttons)}个按钮")
            
        except Exception as e:
            self.log_result("新增菜品页面内容测试", False, f"测试异常: {e}")


def run_fixed_navigation_test():
    """运行修复后的导航测试"""
    print("🚀 开始运行修复后的页面导航测试")
    print("=" * 60)
    print("📱 小程序AppID: wx82283b353918af82")
    print("🔧 修复内容: 页面路径匹配逻辑")
    print("=" * 60)
    
    start_time = datetime.now()
    
    # 运行测试
    suite = unittest.TestLoader().loadTestsFromTestCase(FixedNavigationTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 获取测试结果
    test_instance = None
    for test_case in suite:
        if hasattr(test_case, 'test_results'):
            test_instance = test_case
            break
    
    detailed_results = test_instance.test_results if test_instance else []
    
    # 统计结果
    total_tests = len(detailed_results)
    passed_tests = sum(1 for r in detailed_results if r['success'])
    failed_tests = total_tests - passed_tests
    
    # 生成报告
    navigation_report = {
        "test_time": start_time.isoformat(),
        "duration_seconds": duration,
        "navigation_tests": {
            "total": total_tests,
            "passed": passed_tests,
            "failed": failed_tests
        },
        "overall_success": result.wasSuccessful(),
        "detailed_results": detailed_results,
        "fixes_applied": {
            "path_matching": "使用正确的页面路径进行匹配",
            "wait_mechanism": "增加页面变化等待机制",
            "error_handling": "改进错误处理和日志记录"
        }
    }
    
    # 保存报告
    with open("fixed_navigation_test_report.json", 'w', encoding='utf-8') as f:
        json.dump(navigation_report, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("🎯 修复后的导航测试总结")
    print("=" * 60)
    print(f"⏱️ 测试耗时: {duration:.2f} 秒")
    print(f"🔧 导航测试: {passed_tests}/{total_tests} 通过")
    
    if total_tests > 0:
        success_rate = passed_tests / total_tests * 100
        print(f"🎯 导航成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 页面导航修复成功！")
        elif success_rate >= 60:
            print("👍 页面导航有所改善！")
        else:
            print("⚠️ 页面导航仍需优化")
    
    print(f"\n📊 详细报告: fixed_navigation_test_report.json")
    print("=" * 60)
    
    return navigation_report['overall_success']


if __name__ == "__main__":
    success = run_fixed_navigation_test()
    
    if success:
        print("🎉 修复后的导航测试成功！")
    else:
        print("⚠️ 导航测试仍有问题，请查看详细报告")
