# 楠楠家厨项目架构文档

## 🏗️ 项目概述

楠楠家厨是一个完整的家庭菜单管理和订餐系统，包含微信小程序前端、Node.js后端API和Vue3管理后台。

### 技术栈
- **前端**: 微信小程序 + Vant Weapp UI
- **后端**: Node.js + Express.js + Prisma ORM
- **数据库**: MySQL (生产) / SQLite (开发)
- **管理后台**: Vue 3 + Element Plus
- **部署**: 阿里云ECS + 宝塔面板 + PM2

## 🎯 核心功能

### 用户端功能
- 用户注册登录 (微信授权)
- 菜单浏览和搜索
- 在线订餐和订单管理
- 用户关联 (好友系统)
- 消息通知和推送
- 历史订单查看

### 管理端功能
- 菜品管理 (增删改查)
- 菜单配置和推荐
- 订单统计和分析
- 用户管理
- 消息推送管理

## 🏛️ 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   微信小程序      │    │   管理后台       │    │   后端API服务    │
│   (用户端)       │    │   (Vue3+EP)     │    │   (Express.js)   │
│                 │    │                 │    │                 │
│ • 用户登录注册   │◄──►│ • 用户管理       │◄──►│ • RESTful API   │
│ • 菜单浏览点餐   │    │ • 菜单管理       │    │ • JWT认证       │
│ • 消息通知       │    │ • 订单管理       │    │ • 文件上传      │
│ • 用户关联       │    │ • 统计分析       │    │ • 推送服务      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │   数据库层       │
                                              │   (MySQL)       │
                                              │                 │
                                              │ • Prisma ORM   │
                                              │ • 双环境分离    │
                                              │ • 自动迁移      │
                                              └─────────────────┘
```

### 双环境部署架构
```
🖥️ 阿里云服务器 (*************)
├── 🧪 测试环境 (端口3000)
│   ├── 数据库: nannan_db_test
│   ├── 用途: 开发测试
│   └── API: http://*************:3000/api
└── 🚀 生产环境 (端口3001)
    ├── 数据库: nannan_db
    ├── 用途: 正式用户
    └── API: http://*************:3001/api
```

## 🗄️ 数据库设计

### 核心数据模型
- **User**: 用户信息、角色权限
- **Dish**: 菜品信息、分类管理
- **Menu**: 菜单管理、今日菜单
- **Order**: 订单系统、状态跟踪
- **Message**: 消息系统、家庭消息
- **UserConnection**: 用户关联、好友系统
- **Notification**: 通知推送、订阅消息

### 环境配置
- **测试数据库**: `nannan_db_test` (开发调试)
- **生产数据库**: `nannan_db` (正式用户数据)

## 🚀 部署和运维

### 服务器配置
- **云服务商**: 阿里云ECS
- **服务器规格**: 2核2GB
- **操作系统**: Ubuntu 22.04 LTS
- **管理面板**: 宝塔面板 9.6.0
- **进程管理**: PM2

### 自动化部署
- **代码仓库**: GitHub
- **自动部署**: WebHook + 宝塔面板
- **部署流程**: 代码推送 → 自动拉取 → 依赖安装 → 服务重启

### 访问地址
- **测试环境API**: http://*************:3000/api
- **生产环境API**: http://*************:3001/api
- **管理后台**: http://*************:8080

## 📋 开发指南

### 环境切换
小程序环境切换 (config/env.js):
```javascript
const CURRENT_SERVER = 'test';        // 测试环境
// const CURRENT_SERVER = 'production'; // 生产环境
```

### 常用命令
```bash
# 依赖检查
npm run check:deps

# 部署到服务器
npm run deploy

# 启动开发服务器
npm run dev

# 数据库操作
npx prisma generate
npx prisma db push
```

## 🔧 故障排除

### 常见问题
1. **依赖缺失**: 运行 `npm run check:deps` 检查
2. **端口冲突**: 检查3000/3001端口占用
3. **数据库连接**: 验证MySQL连接配置
4. **WebHook失败**: 查看部署日志

### 日志查看
```bash
# PM2服务日志
pm2 logs nannan-api
pm2 logs nannan-api-test

# WebHook部署日志
tail -f /www/webhook-deploy.log
```

## 📞 技术支持

如有问题，请查看相关文档：
- 账号密码信息: `账号密码相关.md`
- WebHook配置: `WebHook自动部署配置指南.md`
- Nginx配置: `nginx配置应用指南.md`
