# 楠楠家厨小程序优化实施方案

## 🎯 立即可执行的优化改进

### 🖼️ 1. 图片上传功能优化实施

#### A. 新增菜品页面图片上传优化

**文件**: `pages/dish/add/index.js`

```javascript
// 添加图片上传功能
data: {
  imageList: [],
  uploading: false
},

// 选择图片
chooseImage() {
  const that = this;
  wx.chooseImage({
    count: 3, // 最多选择3张图片
    sizeType: ['compressed'], // 压缩图片
    sourceType: ['album', 'camera'],
    success(res) {
      that.uploadImages(res.tempFilePaths);
    },
    fail(err) {
      wx.showToast({
        title: '选择图片失败',
        icon: 'none'
      });
    }
  });
},

// 上传图片
async uploadImages(filePaths) {
  this.setData({ uploading: true });
  
  try {
    const uploadPromises = filePaths.map(filePath => this.uploadSingleImage(filePath));
    const results = await Promise.all(uploadPromises);
    
    this.setData({
      imageList: [...this.data.imageList, ...results],
      uploading: false
    });
    
    wx.showToast({
      title: '上传成功',
      icon: 'success'
    });
  } catch (error) {
    this.setData({ uploading: false });
    wx.showToast({
      title: '上传失败，请重试',
      icon: 'none'
    });
  }
},

// 单个图片上传
uploadSingleImage(filePath) {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('token');
    
    wx.uploadFile({
      url: 'http://*************:3000/api/upload',
      filePath: filePath,
      name: 'image',
      header: {
        'Authorization': `Bearer ${token}`
      },
      formData: {
        type: 'dish',
        category: 'menu'
      },
      success(res) {
        try {
          const data = JSON.parse(res.data);
          if (data.success) {
            resolve(data.data.url);
          } else {
            reject(new Error(data.message));
          }
        } catch (e) {
          reject(new Error('上传响应解析失败'));
        }
      },
      fail: reject
    });
  });
}
```

#### B. 用户头像上传优化

**文件**: `pages/user_profile/index.js`

```javascript
// 头像上传功能
uploadAvatar() {
  const that = this;
  wx.chooseImage({
    count: 1,
    sizeType: ['compressed'],
    sourceType: ['album', 'camera'],
    success(res) {
      that.uploadAvatarImage(res.tempFilePaths[0]);
    }
  });
},

async uploadAvatarImage(filePath) {
  wx.showLoading({ title: '上传中...' });
  
  try {
    const token = wx.getStorageSync('token');
    
    const uploadResult = await new Promise((resolve, reject) => {
      wx.uploadFile({
        url: 'http://*************:3000/api/upload',
        filePath: filePath,
        name: 'avatar',
        header: {
          'Authorization': `Bearer ${token}`
        },
        formData: {
          type: 'avatar',
          entityId: wx.getStorageSync('userId')
        },
        success: resolve,
        fail: reject
      });
    });
    
    const data = JSON.parse(uploadResult.data);
    if (data.success) {
      this.setData({
        'userInfo.avatar': data.data.url
      });
      
      // 更新用户信息
      await this.updateUserAvatar(data.data.url);
      
      wx.hideLoading();
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      });
    }
  } catch (error) {
    wx.hideLoading();
    wx.showToast({
      title: '头像上传失败',
      icon: 'none'
    });
  }
}
```

### 📢 2. 消息推送系统优化实施

#### A. 订单推送通知优化

**文件**: `webs/server/src/controllers/orderController.js`

```javascript
// 优化订单推送功能
const createOrderWithPush = async (req, res) => {
  try {
    const { items, remark, diningTime, pushToUsers } = req.body;
    const userId = req.user.id;
    
    // 创建订单
    const order = await createOrder(req, res);
    
    if (order && pushToUsers && pushToUsers.length > 0) {
      // 推送给指定用户
      await pushOrderToUsers(order, pushToUsers, userId);
    }
    
    // 推送给家庭成员
    await pushOrderToFamily(order, userId);
    
    return success(res, order, '订单创建并推送成功', 201);
  } catch (error) {
    console.error('订单推送失败:', error);
    return error(res, '订单推送失败', 500);
  }
};

// 推送给家庭成员
const pushOrderToFamily = async (order, creatorId) => {
  try {
    // 获取家庭成员
    const familyMembers = await prisma.userConnection.findMany({
      where: {
        OR: [
          { requesterId: creatorId, status: 'accepted' },
          { targetUserId: creatorId, status: 'accepted' }
        ]
      },
      include: {
        requester: true,
        targetUser: true
      }
    });
    
    // 推送通知
    for (const connection of familyMembers) {
      const targetUser = connection.requesterId === creatorId 
        ? connection.targetUser 
        : connection.requester;
      
      await sendPushNotification({
        userId: targetUser.id,
        type: 'order_created',
        title: '新的家庭订单',
        content: `${order.user.name} 创建了新的订单`,
        data: {
          orderId: order.id,
          orderItems: order.items
        }
      });
    }
  } catch (error) {
    console.error('家庭推送失败:', error);
  }
};

// 发送推送通知
const sendPushNotification = async (notification) => {
  try {
    // 保存通知到数据库
    await prisma.notification.create({
      data: {
        userId: notification.userId,
        type: notification.type,
        title: notification.title,
        content: notification.content,
        data: JSON.stringify(notification.data),
        status: 'unread'
      }
    });
    
    // 发送微信订阅消息（如果用户已订阅）
    await sendWechatSubscriptionMessage(notification);
    
  } catch (error) {
    console.error('推送通知失败:', error);
  }
};
```

#### B. 微信订阅消息优化

**文件**: `webs/server/src/services/wechatService.js`

```javascript
// 微信订阅消息服务
const sendWechatSubscriptionMessage = async (notification) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: notification.userId }
    });
    
    if (!user.openid) {
      console.log('用户未绑定微信，跳过订阅消息');
      return;
    }
    
    const templateId = getTemplateId(notification.type);
    if (!templateId) {
      console.log('未找到对应的消息模板');
      return;
    }
    
    const messageData = formatMessageData(notification);
    
    // 调用微信API发送订阅消息
    const result = await wechatApi.sendSubscribeMessage({
      touser: user.openid,
      template_id: templateId,
      data: messageData,
      miniprogram_state: 'formal' // 正式版
    });
    
    console.log('订阅消息发送结果:', result);
  } catch (error) {
    console.error('发送订阅消息失败:', error);
  }
};

// 获取消息模板ID
const getTemplateId = (type) => {
  const templates = {
    'order_created': 'template_order_created_id',
    'order_confirmed': 'template_order_confirmed_id',
    'menu_updated': 'template_menu_updated_id'
  };
  return templates[type];
};

// 格式化消息数据
const formatMessageData = (notification) => {
  const data = JSON.parse(notification.data || '{}');
  
  switch (notification.type) {
    case 'order_created':
      return {
        thing1: { value: notification.title },
        thing2: { value: notification.content },
        time3: { value: new Date().toLocaleString() }
      };
    default:
      return {
        thing1: { value: notification.title },
        thing2: { value: notification.content }
      };
  }
};
```

### 📋 3. 订单状态管理优化实施

#### A. 订单状态流转优化

**文件**: `webs/server/src/controllers/orderController.js`

```javascript
// 订单状态管理
const orderStatusFlow = {
  'pending': ['confirmed', 'cancelled'],
  'confirmed': ['preparing', 'cancelled'],
  'preparing': ['ready', 'cancelled'],
  'ready': ['completed'],
  'completed': [],
  'cancelled': []
};

// 更新订单状态
const updateOrderStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status, remark } = req.body;
    const userId = req.user.id;
    
    // 获取当前订单
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { user: true }
    });
    
    if (!order) {
      return error(res, '订单不存在', 404);
    }
    
    // 验证状态流转
    const allowedStatuses = orderStatusFlow[order.status];
    if (!allowedStatuses.includes(status)) {
      return error(res, `无法从${order.status}状态变更为${status}状态`, 400);
    }
    
    // 更新订单状态
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: {
        status,
        updatedAt: new Date()
      },
      include: { user: true }
    });
    
    // 记录状态变更历史
    await prisma.orderStatusHistory.create({
      data: {
        orderId,
        fromStatus: order.status,
        toStatus: status,
        changedBy: userId,
        remark: remark || null
      }
    });
    
    // 发送状态变更通知
    await notifyStatusChange(updatedOrder, status);
    
    return success(res, updatedOrder, '订单状态更新成功');
  } catch (err) {
    console.error('更新订单状态失败:', err);
    return error(res, '更新订单状态失败', 500);
  }
};

// 状态变更通知
const notifyStatusChange = async (order, newStatus) => {
  const statusMessages = {
    'confirmed': '订单已确认，正在准备中',
    'preparing': '订单正在准备中，请耐心等待',
    'ready': '订单已完成，请及时享用',
    'cancelled': '订单已取消'
  };
  
  const message = statusMessages[newStatus];
  if (message) {
    await sendPushNotification({
      userId: order.userId,
      type: 'order_status_changed',
      title: '订单状态更新',
      content: message,
      data: {
        orderId: order.id,
        status: newStatus
      }
    });
  }
};
```

### 📊 4. 统计图表渲染优化实施

#### A. 统计页面图表优化

**文件**: `pages/statistics/index.js`

```javascript
// 引入图表库
import * as echarts from '../../miniprogram_npm/echarts/index';

data: {
  chartData: null,
  loading: true
},

onLoad() {
  this.initChart();
  this.loadStatisticsData();
},

// 初始化图表
initChart() {
  this.selectComponent('#chart-container').init((canvas, width, height, dpr) => {
    const chart = echarts.init(canvas, null, {
      width: width,
      height: height,
      devicePixelRatio: dpr
    });
    
    this.chart = chart;
    return chart;
  });
},

// 加载统计数据
async loadStatisticsData() {
  try {
    wx.showLoading({ title: '加载中...' });
    
    const [orderStats, dishStats, userStats] = await Promise.all([
      this.getOrderStatistics(),
      this.getDishStatistics(),
      this.getUserStatistics()
    ]);
    
    this.renderCharts({
      orders: orderStats,
      dishes: dishStats,
      users: userStats
    });
    
    wx.hideLoading();
    this.setData({ loading: false });
  } catch (error) {
    wx.hideLoading();
    wx.showToast({
      title: '数据加载失败',
      icon: 'none'
    });
  }
},

// 渲染图表
renderCharts(data) {
  const option = {
    title: {
      text: '菜品热度排行',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.dishes.map(item => item.name)
    },
    yAxis: {
      type: 'value',
      name: '订单数量'
    },
    series: [{
      name: '订单数量',
      type: 'bar',
      data: data.dishes.map(item => item.orderCount),
      itemStyle: {
        color: '#3B82F6'
      }
    }]
  };
  
  this.chart.setOption(option);
}
```

### ⚡ 5. 性能优化实施

#### A. API缓存优化

**文件**: `utils/enhancedRequest.js`

```javascript
// API缓存管理
class ApiCache {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟
  }
  
  set(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
    
    // 设置过期清理
    setTimeout(() => {
      this.cache.delete(key);
    }, this.cacheTimeout);
  }
  
  get(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }
  
  clear() {
    this.cache.clear();
  }
}

const apiCache = new ApiCache();

// 带缓存的请求函数
const cachedRequest = async (url, options = {}) => {
  const cacheKey = `${url}_${JSON.stringify(options)}`;
  
  // 检查缓存
  const cached = apiCache.get(cacheKey);
  if (cached && !options.skipCache) {
    console.log('使用缓存数据:', cacheKey);
    return cached;
  }
  
  // 发起请求
  const response = await request(url, options);
  
  // 缓存响应（仅缓存GET请求）
  if (!options.method || options.method === 'GET') {
    apiCache.set(cacheKey, response);
  }
  
  return response;
};
```

## 🎯 优化实施时间表

### 🔥 第一周 (立即执行)
- [x] 图片上传功能优化 - 2天
- [x] 订单状态管理优化 - 2天  
- [x] 消息推送系统优化 - 3天

### 📋 第二周 (持续优化)
- [ ] 统计图表渲染优化 - 3天
- [ ] 性能缓存优化 - 2天
- [ ] 错误处理增强 - 2天

### 📊 第三周 (测试验证)
- [ ] 功能测试验证 - 3天
- [ ] 性能测试验证 - 2天
- [ ] 用户体验测试 - 2天

## 🏆 预期优化效果

### 📈 量化指标
- **图片上传成功率**: 95%+
- **消息推送到达率**: 90%+
- **页面加载速度**: 提升30%
- **API响应时间**: 减少20%
- **用户操作成功率**: 95%+

### 🎉 用户体验提升
- 图片上传流程更加流畅
- 订单状态变更及时通知
- 统计数据可视化展示
- 系统响应速度显著提升
- 错误提示更加友好

**🚀 通过这些优化，楠楠家厨小程序将达到生产级别的质量标准！**
