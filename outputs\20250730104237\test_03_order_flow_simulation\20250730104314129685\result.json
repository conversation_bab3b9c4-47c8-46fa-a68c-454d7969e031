{"case_name": "test_03_order_flow_simulation", "run_time": "20250730 10:43:14", "test_type": "StableFunctionalTest", "case_doc": "测试订餐流程模拟", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843394.2858374, "is_failure": false, "is_error": false, "module": "E:.wx-nan.stable_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/mine/index", "path": "images\\setup.png", "ts": 1753843394, "datetime": "2025-07-30 10:43:14", "use_region": false}, {"name": "teardown", "url": "/pages/detail/index", "path": "images\\teardown.png", "ts": 1753843403, "datetime": "2025-07-30 10:43:23", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843403.2033277, "appId": "", "appName": "", "source": {"code": ["    def test_03_order_flow_simulation(self):\n", "        \"\"\"测试订餐流程模拟\"\"\"\n", "        print(\"🧪 测试订餐流程模拟\")\n", "        \n", "        try:\n", "            # 进入订餐页面\n", "            self.app.switch_tab(\"/pages/order/index\")\n", "            time.sleep(3)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 查找可点击的菜品或按钮\n", "            interactive_elements = current_page.get_elements(\"view[bindtap], button, .dish-card, .food-item\")\n", "            \n", "            if interactive_elements:\n", "                self.log_result(\"订餐页面交互元素\", True, f\"找到{len(interactive_elements)}个可交互元素\")\n", "                \n", "                # 尝试点击前几个元素模拟用户操作\n", "                for i, element in enumerate(interactive_elements[:3]):\n", "                    try:\n", "                        element_info = \"\"\n", "                        try:\n", "                            element_info = element.inner_text.strip()[:20]\n", "                        except:\n", "                            element_info = f\"元素{i+1}\"\n", "                        \n", "                        element.click()\n", "                        time.sleep(1)\n", "                        \n", "                        self.log_result(f\"订餐交互-{i+1}\", True, f\"点击了: {element_info}\")\n", "                        \n", "                    except Exception as e:\n", "                        self.log_result(f\"订餐交互-{i+1}\", False, f\"点击失败: {e}\")\n", "                \n", "                # 查找购物车相关元素\n", "                cart_elements = current_page.get_elements(\".cart, .basket, .shopping, button[bindtap*='cart']\")\n", "                if cart_elements:\n", "                    self.log_result(\"购物车功能\", True, f\"找到{len(cart_elements)}个购物车相关元素\")\n", "                    \n", "                    # 尝试点击购物车\n", "                    try:\n", "                        cart_elements[0].click()\n", "                        time.sleep(2)\n", "                        \n", "                        new_page = self.app.get_current_page()\n", "                        if new_page.path != current_page.path:\n", "                            self.log_result(\"购物车页面跳转\", True, f\"跳转到: {new_page.path}\")\n", "                        else:\n", "                            self.log_result(\"购物车交互\", True, \"购物车有响应\")\n", "                            \n", "                    except Exception as e:\n", "                        self.log_result(\"购物车操作\", False, f\"操作失败: {e}\")\n", "                else:\n", "                    self.log_result(\"购物车功能\", False, \"未找到购物车元素\")\n", "            else:\n", "                self.log_result(\"订餐页面交互元素\", False, \"未找到可交互元素\")\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"订餐流程测试\", False, f\"异常: {e}\")\n"], "start": 169}, "filename": "result.json"}