const {userApi} = require('../../services/api');

Page({
  data: {
    form: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    showOldPassword: false,
    showNewPassword: false,
    showConfirmPassword: false,
    loading: false,
    tipMessage: '',
    tipType: 'info' // info, error, success
  },

  onLoad() {
    // 检查是否已登录
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录',
        showCancel: false,
        success: () => {
          wx.redirectTo({
            url: '/pages/login/index'
          });
        }
      });
    }
  },

  // 输入事件处理
  onOldPasswordChange(e) {
    this.setData({
      'form.oldPassword': e.detail.value,
      tipMessage: ''
    });
  },

  onNewPasswordChange(e) {
    this.setData({
      'form.newPassword': e.detail.value,
      tipMessage: ''
    });
  },

  onConfirmPasswordChange(e) {
    this.setData({
      'form.confirmPassword': e.detail.value,
      tipMessage: ''
    });
  },

  // 切换密码显示
  toggleOldPassword() {
    this.setData({
      showOldPassword: !this.data.showOldPassword
    });
  },

  toggleNewPassword() {
    this.setData({
      showNewPassword: !this.data.showNewPassword
    });
  },

  toggleConfirmPassword() {
    this.setData({
      showConfirmPassword: !this.data.showConfirmPassword
    });
  },

  // 表单验证
  validateForm() {
    const {oldPassword, newPassword, confirmPassword} = this.data.form;

    if (!oldPassword) {
      this.showTip('请输入当前密码', 'error');
      return false;
    }

    if (!newPassword || newPassword.length < 6) {
      this.showTip('新密码至少需要6个字符', 'error');
      return false;
    }

    if (newPassword === oldPassword) {
      this.showTip('新密码不能与当前密码相同', 'error');
      return false;
    }

    if (newPassword !== confirmPassword) {
      this.showTip('两次输入的新密码不一致', 'error');
      return false;
    }

    return true;
  },

  // 显示提示信息
  showTip(message, type = 'info') {
    this.setData({
      tipMessage: message,
      tipType: type
    });

    // 3秒后自动清除提示
    setTimeout(() => {
      this.setData({
        tipMessage: ''
      });
    }, 3000);
  },

  // 修改密码处理
  async handleChangePassword() {
    if (!this.validateForm()) {
      return;
    }

    this.setData({loading: true});

    try {
      const {oldPassword, newPassword} = this.data.form;
      const userInfo = wx.getStorageSync('userInfo');

      const response = await userApi.changePassword({
        userId: userInfo.id,
        oldPassword: oldPassword,
        newPassword: newPassword
      });

      if (response.code === 200) {
        this.showTip('密码修改成功！', 'success');

        // 延迟跳转，提示用户重新登录
        setTimeout(() => {
          wx.showModal({
            title: '密码修改成功',
            content: '为了安全起见，请重新登录',
            showCancel: false,
            success: () => {
              // 清除用户信息
              wx.removeStorageSync('userInfo');
              wx.removeStorageSync('token');

              // 跳转到登录页面
              wx.reLaunch({
                url: '/pages/login/index'
              });
            }
          });
        }, 1500);
      } else {
        this.showTip(response.message || '密码修改失败，请重试', 'error');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      this.showTip(error.message || '网络错误，请重试', 'error');
    } finally {
      this.setData({loading: false});
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
