#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试订单创建问题
详细分析500错误的原因
"""

import requests
import json
from datetime import datetime, timezone

def debug_order_creation():
    """调试订单创建"""
    api_base = "http://8.148.231.104:3000/api"
    
    print("🔍 开始调试订单创建问题")
    print("=" * 60)
    
    # 先登录获取token
    print("🧪 用户登录...")
    try:
        login_response = requests.post(
            f"{api_base}/auth/login",
            json={
                "username": "13800000001",
                "password": "test123456"
            },
            timeout=10
        )
        
        if login_response.status_code == 200:
            result = login_response.json()
            token = result["data"]["token"]
            user_id = result["data"]["user"]["id"]
            print(f"✅ 登录成功: ID={user_id}")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return
    
    # 获取可用菜品
    print("\n🧪 获取可用菜品...")
    try:
        dishes_response = requests.get(
            f"{api_base}/dishes",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if dishes_response.status_code == 200:
            dishes_data = dishes_response.json()
            dishes = dishes_data.get("data", [])
            if dishes:
                dish_id = dishes[0]["id"]
                dish_name = dishes[0]["name"]
                print(f"✅ 获取到菜品: ID={dish_id}, 名称={dish_name}")
            else:
                print("❌ 没有可用菜品")
                return
        else:
            print(f"❌ 获取菜品失败: {dishes_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 获取菜品异常: {e}")
        return
    
    # 测试不同的订单创建方式
    test_cases = [
        {
            "name": "基础订单（无diningTime）",
            "data": {
                "items": [
                    {
                        "dishId": dish_id,
                        "count": 1
                    }
                ],
                "remark": "基础测试订单"
            }
        },
        {
            "name": "带用餐时间的订单（ISO格式）",
            "data": {
                "items": [
                    {
                        "dishId": dish_id,
                        "count": 2
                    }
                ],
                "remark": "带时间的测试订单",
                "diningTime": datetime.now(timezone.utc).isoformat()
            }
        },
        {
            "name": "带用餐时间的订单（简单格式）",
            "data": {
                "items": [
                    {
                        "dishId": dish_id,
                        "count": 1
                    }
                ],
                "remark": "简单时间格式订单",
                "diningTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        },
        {
            "name": "带目标用户的订单",
            "data": {
                "items": [
                    {
                        "dishId": dish_id,
                        "count": 1
                    }
                ],
                "remark": "推送订单",
                "targetUserId": user_id  # 推送给自己
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试用例 {i}: {test_case['name']}")
        
        try:
            response = requests.post(
                f"{api_base}/orders",
                json=test_case["data"],
                headers={"Authorization": f"Bearer {token}"},
                timeout=15
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 201:
                result = response.json()
                order_id = result["data"]["order"]["id"]
                print(f"   ✅ 订单创建成功: ID={order_id}")
            else:
                print(f"   ❌ 订单创建失败")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    # 测试orderPush接口（这是前端实际使用的接口）
    print(f"\n🧪 测试orderPush接口（前端实际使用）")
    
    try:
        order_push_data = {
            "items": [
                {
                    "dishId": dish_id,
                    "count": 2
                }
            ],
            "remark": "orderPush接口测试",
            "diningTime": datetime.now().isoformat(),
            "pushToUsers": []  # 空数组表示不推送给其他用户
        }
        
        response = requests.post(
            f"{api_base}/order-push",
            json=order_push_data,
            headers={"Authorization": f"Bearer {token}"},
            timeout=15
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            order_id = result["data"]["order"]["id"]
            print(f"   ✅ orderPush创建成功: ID={order_id}")
        else:
            print(f"   ❌ orderPush创建失败")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
                
    except Exception as e:
        print(f"   ❌ orderPush异常: {e}")
    
    # 验证创建的订单
    print(f"\n🧪 验证创建的订单...")
    try:
        orders_response = requests.get(
            f"{api_base}/orders",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if orders_response.status_code == 200:
            result = orders_response.json()
            orders_count = len(result.get("data", {}).get("list", []))
            print(f"   ✅ 当前订单总数: {orders_count}")
        else:
            print(f"   ❌ 获取订单失败: {orders_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 验证订单异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 调试完成")

if __name__ == "__main__":
    debug_order_creation()
