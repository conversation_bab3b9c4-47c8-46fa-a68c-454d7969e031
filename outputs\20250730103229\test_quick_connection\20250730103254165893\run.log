[I 2025-07-30 10:32:54,168 minium minitest#432 _miniSetUp] =========Current case: test_quick_connection=========
[I 2025-07-30 10:32:54,169 minium minitest#435 _miniSetUp] package info: E:.wx-nan.advanced_connection_test, case info: QuickTest.test_quick_connection
[I 2025-07-30 10:32:54,169 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:32:54,169 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:32:54,169 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:32:54,170 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:32:54,170 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:32:54,172 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:32:54,173 minium.Conn4976 connection#427 _safely_send] SEND > [2259465544976]{"id":"8108cd55-a64c-4354-ad38-353abc06abeb","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,366 minium.Conn4976 connection#660 __on_message] RECV < [2259465544976]{"id":"8108cd55-a64c-4354-ad38-353abc06abeb","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWV/jm3tl7fln2HkIQQCEEggAijMOxxQxZRETcQRsYRHec3gzPoMLjPyDjDwCjLqIiiLCJIYEQGUBFBSCCEJEA2kryX5b2Xt/TrpdZ7fn/cqurq6up+/V4SE8j9IPVO3751697qvl+f892l0LRdAAAARCQiaUtb2tLey3bIMhISEhL7AgwAEDF8LW1pS1vae9eWvoyEhMS+hRpa+yg245zv0/KlLW1p76HNGHvT6DJhoRISEm92RAOfPYQ6epbRIMlFQuKth6hXsodFjZ9lJLlISBwM2HO6ibAMIoTE0dT2r9pyfr+uAGPKL21pS3uf2tGhoFbyi46PiGO91th0mVb8F+njSEi8qdGKzzImv6bViKk5d
[I 2025-07-30 10:32:54,369 minium minitest#487 _miniSetUp] =========case: test_quick_connection start=========
[I 2025-07-30 10:32:54,369 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:32:54,370 minium.Conn4976 connection#427 _safely_send] SEND > [2259465544976]{"id":"29187262-9304-47f3-8aff-97a6c3d908d6","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:32:54,372 minium.Conn4976 connection#660 __on_message] RECV < [2259465544976]{"id":"29187262-9304-47f3-8aff-97a6c3d908d6","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:32:54,373 minium.App8000 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:32:54,374 minium.Conn4976 connection#427 _safely_send] SEND > [2259465544976]{"id":"9577513a-1e08-4d52-9e16-b3cbf32d7e0c","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:32:54,376 minium.Conn4976 connection#660 __on_message] RECV < [2259465544976]{"id":"9577513a-1e08-4d52-9e16-b3cbf32d7e0c","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:32:54,377 minium.Conn4976 connection#427 _safely_send] SEND > [2259465544976]{"id":"81f65f54-526b-434d-91ad-16196fcc2952","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,517 minium.Conn4976 connection#660 __on_message] RECV < [2259465544976]{"id":"81f65f54-526b-434d-91ad-16196fcc2952","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgHFWZNv6+p7be7pp9JyE7hCAQQAQVhz1uCCIqg6hsIx+fyzjfiOMyDO4zMvyGERVwVEQRhEHQwIiOgCKCkEAISYBsJLk3ubn35i59e6n1vL8/TlV1dXV13743iUnIeSB13z596tQ51X2eft/nLIWm7QIAACAiEUlb2tKW9n62Q5aRkJCQOBBgAICI4WtpS1va0t6/tvRlJCQkDizU0DpAsRnn/ICWL21pS3sfbcbYYaPLhIVKSEgc7ogGPvsIdfQso0GSi4TEGw9Rr2Qfixo/y0hykZA4ErDvdBNhGUQIiaOh7V+16fx+XQHGlF/a0pb2AbWjQ0HN5BcdHxHHeq2x6TLN+C/Sx5GQOKzRjM8yJr+m2YipM
[I 2025-07-30 10:32:54,536 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:32:54,536 minium minitest#799 _miniTearDown] =========Current case Down: test_quick_connection=========
[I 2025-07-30 10:32:54,538 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:32:54,538 minium.Conn4976 connection#427 _safely_send] SEND > [2259465544976]{"id":"30904b09-3e5f-4e93-924e-3578a0dfc97f","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:32:54,622 minium.Conn4976 connection#660 __on_message] RECV < [2259465544976]{"id":"30904b09-3e5f-4e93-924e-3578a0dfc97f","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgHFWZ7/ed2nq5e5KbfSEkIQmEIBBABBWHPW4oICqDjoIwMD63eTODIzI83MY3Mr5hZJRlVERRlkHQwIgOoCKCkEAIWchKknuTu+UufXup9Xzvj1NVXV1d3bfvTWIScn6Qul+fPnXqnOo+v/6+31kKTdsFAABARCKStrSlLe0DbIcsIyEhIXEwwAAAEcPX0pa2tKV9YG3py0hISBxcqKF1kGIzzvlBLV/a0pb2ftqMsSNGlwkLlZCQONIRDXz2E+rYWcaCJBcJiTcfol7JfhY1cZaR5CIhcTRg/+kmwjKIEBJHXdu/asP5/boCjCu/tKUt7YNqR4eCGskvOj4ijvda49NlGvFfpI8jIXFEoxGfZVx+TaMRU
[I 2025-07-30 10:32:54,623 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:32:54,624 minium basenative#63 wrapper] call BaseNative.get_start_up end 
