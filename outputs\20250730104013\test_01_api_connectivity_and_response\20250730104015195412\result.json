{"case_name": "test_01_api_connectivity_and_response", "run_time": "20250730 10:40:15", "test_type": "ComprehensiveFunctionalTest", "case_doc": "测试API连接和响应", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843215.3387787, "is_failure": false, "is_error": false, "module": "E:.wx-nan.comprehensive_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "", "path": "images\\setup.png", "ts": 1753843215, "datetime": "2025-07-30 10:40:15", "use_region": false}, {"name": "teardown", "url": "/pages/home/<USER>", "path": "images\\teardown.png", "ts": 1753843234, "datetime": "2025-07-30 10:40:34", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843234.541878, "appId": "", "appName": "", "source": {"code": ["    def test_01_api_connectivity_and_response(self):\n", "        \"\"\"测试API连接和响应\"\"\"\n", "        print(\"🧪 测试API连接和响应\")\n", "        \n", "        try:\n", "            # 导航到首页，触发API调用\n", "            self.app.switch_tab(\"/pages/home/<USER>\")\n", "            time.sleep(3)\n", "            \n", "            # 检查网络请求\n", "            network_logs = []\n", "            try:\n", "                # 尝试获取网络日志\n", "                logs = self.app.get_perf_data()\n", "                if logs:\n", "                    network_logs = logs.get('network', [])\n", "            except:\n", "                pass\n", "            \n", "            # 检查页面是否加载了数据\n", "            current_page = self.app.get_current_page()\n", "            page_content = current_page.get_element(\"page\").inner_text\n", "            \n", "            # 验证API响应\n", "            api_working = False\n", "            if \"楠楠家厨\" in page_content or \"欢迎\" in page_content:\n", "                api_working = True\n", "                self.log_test_result(\"API基础连接\", True, \"页面内容正常加载\")\n", "            else:\n", "                self.log_test_result(\"API基础连接\", False, \"页面内容加载异常\")\n", "            \n", "            # 检查是否有错误提示\n", "            error_elements = current_page.get_elements(\".error, .network-error, .empty-state\")\n", "            if error_elements:\n", "                self.log_test_result(\"API错误检查\", False, f\"发现{len(error_elements)}个错误元素\")\n", "            else:\n", "                self.log_test_result(\"API错误检查\", True, \"无API错误提示\")\n", "            \n", "        except Exception as e:\n", "            self.log_test_result(\"API连接测试\", False, f\"测试异常: {e}\")\n"], "start": 80}, "filename": "result.json"}