#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的minium连接测试 - 使用配置文件
"""

import os
import sys
import unittest

def main():
    print("🚀 测试minium与微信开发者工具连接")
    print("🔌 端口: 25209")
    print("📱 项目: wx82283b353918af82")
    print("=" * 50)
    
    try:
        # 导入minium
        from minium import MiniTest
        print("✅ minium导入成功")
        
        # 检查配置文件
        config_path = "tests/config/minium.json"
        if os.path.exists(config_path):
            print(f"✅ 配置文件存在: {config_path}")
        else:
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        # 创建测试类
        class SimpleConnectionTest(MiniTest):
            def setUp(self):
                super().setUp()
                self.app = self.mini.app
                print("✅ 测试类初始化成功")
            
            def test_01_app_launch(self):
                """测试小程序启动"""
                print("🧪 测试小程序启动")
                
                # 获取小程序实例
                self.assertIsNotNone(self.app, "小程序应用实例应该存在")
                print("✅ 小程序启动成功")
                
                # 获取当前页面
                current_page = self.app.get_current_page()
                self.assertIsNotNone(current_page, "当前页面应该存在")
                
                page_path = current_page.path
                print(f"📱 当前页面: {page_path}")
                
                # 截图
                screenshot_path = "simple_test_screenshot.png"
                self.app.screen_shot(screenshot_path)
                print(f"📸 截图保存: {screenshot_path}")
                
                return True
            
            def test_02_navigate_to_home(self):
                """测试导航到首页"""
                print("🧪 测试导航到首页")
                
                try:
                    # 导航到首页
                    self.app.switch_tab("/pages/home/<USER>")
                    import time
                    time.sleep(2)
                    
                    # 获取当前页面
                    current_page = self.app.get_current_page()
                    page_path = current_page.path
                    
                    print(f"✅ 成功导航到: {page_path}")
                    
                    # 截图
                    screenshot_path = "home_page_test.png"
                    self.app.screen_shot(screenshot_path)
                    print(f"📸 截图保存: {screenshot_path}")
                    
                    return True
                    
                except Exception as e:
                    print(f"⚠️ 导航测试异常: {e}")
                    return False
        
        # 设置配置文件路径
        os.chdir(os.path.dirname(config_path))
        
        # 运行测试
        suite = unittest.TestLoader().loadTestsFromTestCase(SimpleConnectionTest)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        # 返回原目录
        os.chdir("../..")
        
        if result.wasSuccessful():
            print("\n🎉 连接测试成功！")
            print("✅ minium已成功连接到微信开发者工具")
            print("✅ 小程序项目加载正常")
            print("✅ 页面导航功能正常")
            
            # 显示测试统计
            print(f"\n📊 测试统计:")
            print(f"  总测试数: {result.testsRun}")
            print(f"  成功测试: {result.testsRun - len(result.failures) - len(result.errors)}")
            print(f"  失败测试: {len(result.failures)}")
            print(f"  错误测试: {len(result.errors)}")
            
            return True
        else:
            print("\n❌ 连接测试失败！")
            if result.failures:
                print("失败原因:")
                for failure in result.failures:
                    print(f"  {failure[1]}")
            if result.errors:
                print("错误信息:")
                for error in result.errors:
                    print(f"  {error[1]}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 连接成功！现在可以运行完整的功能测试了")
        print("📋 生成的文件:")
        if os.path.exists("simple_test_screenshot.png"):
            print("  📸 simple_test_screenshot.png")
        if os.path.exists("home_page_test.png"):
            print("  📸 home_page_test.png")
    else:
        print("\n🔧 请检查:")
        print("  1. 微信开发者工具是否已启动")
        print("  2. 小程序项目是否已加载 (wx82283b353918af82)")
        print("  3. 服务端口25209是否已开启")
        print("  4. 自动化接口是否已启用")
        print("  5. 项目路径是否正确: E:/wx-nan")
