#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
订单创建500错误深度诊断工具
逐步测试每个可能的失败点
"""

import requests
import json
from datetime import datetime, timezone

def diagnose_order_creation():
    """诊断订单创建问题"""
    api_base = "http://8.148.231.104:3000/api"
    
    print("🔍 开始深度诊断订单创建500错误")
    print("=" * 70)
    
    # 步骤1: 登录获取token
    print("🔐 步骤1: 用户登录...")
    try:
        login_response = requests.post(
            f"{api_base}/auth/login",
            json={
                "username": "13800000001",
                "password": "test123456"
            },
            timeout=10
        )
        
        if login_response.status_code == 200:
            result = login_response.json()
            token = result["data"]["token"]
            user_id = result["data"]["user"]["id"]
            user_name = result["data"]["user"]["name"]
            print(f"✅ 登录成功: ID={user_id}, 姓名={user_name}")
        else:
            print(f"❌ 登录失败: {login_response.status_code}")
            print(f"   响应: {login_response.text}")
            return
            
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return
    
    # 步骤2: 验证用户权限
    print("\n👤 步骤2: 验证用户权限...")
    try:
        profile_response = requests.get(
            f"{api_base}/auth/profile",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if profile_response.status_code == 200:
            profile = profile_response.json()
            print(f"✅ 用户权限正常: 角色={profile['data'].get('role', 'user')}")
        else:
            print(f"❌ 用户权限验证失败: {profile_response.status_code}")
            
    except Exception as e:
        print(f"❌ 权限验证异常: {e}")
    
    # 步骤3: 获取可用菜品
    print("\n🍽️ 步骤3: 获取可用菜品...")
    dish_id = None
    try:
        dishes_response = requests.get(
            f"{api_base}/dishes",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if dishes_response.status_code == 200:
            dishes_data = dishes_response.json()
            dishes = dishes_data.get("data", [])
            if dishes:
                dish_id = dishes[0]["id"]
                dish_name = dishes[0]["name"]
                print(f"✅ 获取菜品成功: ID={dish_id}, 名称={dish_name}")
                print(f"   总共{len(dishes)}个可用菜品")
            else:
                print("❌ 没有可用菜品")
                return
        else:
            print(f"❌ 获取菜品失败: {dishes_response.status_code}")
            print(f"   响应: {dishes_response.text}")
            return
            
    except Exception as e:
        print(f"❌ 获取菜品异常: {e}")
        return
    
    # 步骤4: 验证菜品存在性
    print(f"\n🔍 步骤4: 验证菜品存在性...")
    try:
        dish_detail_response = requests.get(
            f"{api_base}/dishes/{dish_id}",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if dish_detail_response.status_code == 200:
            dish_detail = dish_detail_response.json()
            print(f"✅ 菜品详情验证成功: {dish_detail['data']['name']}")
        else:
            print(f"❌ 菜品详情验证失败: {dish_detail_response.status_code}")
            
    except Exception as e:
        print(f"❌ 菜品详情验证异常: {e}")
    
    # 步骤5: 测试最简单的订单创建
    print(f"\n📝 步骤5: 测试最简单的订单创建...")
    
    simple_order_data = {
        "items": [
            {
                "dishId": dish_id,
                "count": 1
            }
        ]
    }
    
    try:
        print(f"   发送数据: {json.dumps(simple_order_data, indent=2)}")
        
        simple_response = requests.post(
            f"{api_base}/orders",
            json=simple_order_data,
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            timeout=15
        )
        
        print(f"   响应状态码: {simple_response.status_code}")
        print(f"   响应头: {dict(simple_response.headers)}")
        
        if simple_response.status_code == 201:
            result = simple_response.json()
            print(f"✅ 简单订单创建成功: {result}")
        else:
            print(f"❌ 简单订单创建失败")
            try:
                error_detail = simple_response.json()
                print(f"   错误详情: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"   响应内容: {simple_response.text}")
                
    except Exception as e:
        print(f"❌ 简单订单创建异常: {e}")
    
    # 步骤6: 测试带备注的订单
    print(f"\n📝 步骤6: 测试带备注的订单...")
    
    remark_order_data = {
        "items": [
            {
                "dishId": dish_id,
                "count": 1
            }
        ],
        "remark": "测试备注"
    }
    
    try:
        remark_response = requests.post(
            f"{api_base}/orders",
            json=remark_order_data,
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            timeout=15
        )
        
        print(f"   响应状态码: {remark_response.status_code}")
        
        if remark_response.status_code == 201:
            print(f"✅ 带备注订单创建成功")
        else:
            print(f"❌ 带备注订单创建失败")
            try:
                error_detail = remark_response.json()
                print(f"   错误详情: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"   响应内容: {remark_response.text}")
                
    except Exception as e:
        print(f"❌ 带备注订单创建异常: {e}")
    
    # 步骤7: 测试带时间的订单
    print(f"\n📝 步骤7: 测试带时间的订单...")
    
    # 测试多种时间格式
    time_formats = [
        datetime.now().isoformat(),
        datetime.now(timezone.utc).isoformat(),
        datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%fZ")
    ]
    
    for i, time_format in enumerate(time_formats, 1):
        print(f"\n   🕐 测试时间格式 {i}: {time_format}")
        
        time_order_data = {
            "items": [
                {
                    "dishId": dish_id,
                    "count": 1
                }
            ],
            "remark": f"时间格式测试{i}",
            "diningTime": time_format
        }
        
        try:
            time_response = requests.post(
                f"{api_base}/orders",
                json=time_order_data,
                headers={
                    "Authorization": f"Bearer {token}",
                    "Content-Type": "application/json"
                },
                timeout=15
            )
            
            print(f"      响应状态码: {time_response.status_code}")
            
            if time_response.status_code == 201:
                print(f"      ✅ 时间格式{i}创建成功")
                break  # 找到可用格式就停止
            else:
                print(f"      ❌ 时间格式{i}创建失败")
                try:
                    error_detail = time_response.json()
                    print(f"      错误详情: {error_detail.get('message', 'Unknown error')}")
                except:
                    print(f"      响应内容: {time_response.text[:200]}...")
                    
        except Exception as e:
            print(f"      ❌ 时间格式{i}异常: {e}")
    
    # 步骤8: 测试order-push接口
    print(f"\n🚀 步骤8: 测试order-push接口...")
    
    push_order_data = {
        "items": [
            {
                "dishId": dish_id,
                "count": 1
            }
        ],
        "remark": "order-push接口测试",
        "pushToUsers": []
    }
    
    try:
        push_response = requests.post(
            f"{api_base}/order-push/create-and-push",
            json=push_order_data,
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            timeout=15
        )
        
        print(f"   响应状态码: {push_response.status_code}")
        
        if push_response.status_code == 201:
            result = push_response.json()
            print(f"✅ order-push接口创建成功: 订单ID={result['data']['order']['id']}")
        else:
            print(f"❌ order-push接口创建失败")
            try:
                error_detail = push_response.json()
                print(f"   错误详情: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"   响应内容: {push_response.text}")
                
    except Exception as e:
        print(f"❌ order-push接口异常: {e}")
    
    # 步骤9: 验证创建的订单
    print(f"\n📊 步骤9: 验证创建的订单...")
    try:
        orders_response = requests.get(
            f"{api_base}/orders",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if orders_response.status_code == 200:
            result = orders_response.json()
            orders_count = len(result.get("data", {}).get("list", []))
            print(f"✅ 当前用户订单总数: {orders_count}")
        else:
            print(f"❌ 获取订单列表失败: {orders_response.status_code}")
            
    except Exception as e:
        print(f"❌ 验证订单异常: {e}")
    
    print("\n" + "=" * 70)
    print("🎯 诊断完成")
    print("=" * 70)

if __name__ == "__main__":
    diagnose_order_creation()
