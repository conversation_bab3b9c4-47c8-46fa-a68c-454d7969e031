[I 2025-07-30 10:41:04,173 minium minitest#432 _miniSetUp] =========Current case: test_06_message_and_notification_system=========
[I 2025-07-30 10:41:04,173 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_06_message_and_notification_system
[I 2025-07-30 10:41:04,173 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:41:04,174 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:41:04,174 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:41:04,174 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:41:04,174 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:41:04,226 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:41:04,230 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"1431a5cf-d15c-443d-aecc-b9fff58d37f3","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:41:04,332 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"1431a5cf-d15c-443d-aecc-b9fff58d37f3","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAHHWZ//88n6OO7p47mdwJIYRDIIIIqKDcrK6Au3J4AbuogIDIoj9BXb+7uruC4NcLRAW+ysq1CwIuBFflUEFQDjkMIBCOQO5jMkdfdXyO5/dH9fR0JpNMTzITBvJ5ocW7q6qru4qpdz/P8zkK41QDAAAgIhE57bTTTo+zrruMw+FwTAQMABCx/tppp512eny1i2UcDsfEwupq8jif0047/ZbSLpZxOBwTShbLYMMap5122unx1C6WcTgcE4uryzjttNMTrF0s43A4JhQ2+i4Oh8OxHTiXcTgcE0uDyzTkUU477bTT46W3pS5THwTlcDh2NhrLuk0ixrT3SP5Cg5vG+tEOh2NS0xiO1FVmAmPymqZdhoCGD
[I 2025-07-30 10:41:04,335 minium minitest#487 _miniSetUp] =========case: test_06_message_and_notification_system start=========
[I 2025-07-30 10:41:05,335 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:41:05,336 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [2264960766976]{"id":"adde7fa6-23ba-46aa-a7af-df664a1f83ed","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:41:05,338 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:41:05,574 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843265570,"webviewId":3,"routeEventId":"3_1753843265355","renderer":"webview"},1753843265571]}}
[I 2025-07-30 10:41:05,574 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843265570, 'webviewId': 3, 'routeEventId': '3_1753843265355', 'renderer': 'webview'}, 1753843265571]}
[D 2025-07-30 10:41:05,577 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"adde7fa6-23ba-46aa-a7af-df664a1f83ed","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:41:05,577 minium.Conn6976 connection#704 _handle_async_msg] received async msg: adde7fa6-23ba-46aa-a7af-df664a1f83ed
[D 2025-07-30 10:41:07,578 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"42df7635-11d2-42bd-8a6e-77f59a563257","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:41:07,580 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"42df7635-11d2-42bd-8a6e-77f59a563257","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:41:07,580 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:41:07,581 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"6b4e4890-4718-4508-b174-d3d3ae58a25e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:41:07,584 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"6b4e4890-4718-4508-b174-d3d3ae58a25e","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:41:07,586 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:41:07,586 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"3f357028-4e07-4943-a31b-f5e9dcac48cc","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:41:07,590 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"3f357028-4e07-4943-a31b-f5e9dcac48cc","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:41:07,592 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3314F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398C30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398690>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A399090>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398AF0>]
[D 2025-07-30 10:41:07,593 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"3d5ba7bf-fb70-48c7-aa26-20d55f149c86","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:41:07,595 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"3d5ba7bf-fb70-48c7-aa26-20d55f149c86","result":{"properties":["留言"]}}
[D 2025-07-30 10:41:07,596 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"73d212a9-c269-46bf-baea-c4c890b2ff9e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:41:07,605 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"73d212a9-c269-46bf-baea-c4c890b2ff9e","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:41:07,606 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"14bc2421-4e5b-4a3c-bcbf-6c99769e59db","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:41:07,609 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"14bc2421-4e5b-4a3c-bcbf-6c99769e59db","result":{"properties":["我的菜品"]}}
[D 2025-07-30 10:41:07,610 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"3d896185-a8bb-4d0a-b15d-5ae934a5ae3b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:41:07,612 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"3d896185-a8bb-4d0a-b15d-5ae934a5ae3b","result":{"properties":["通知中心"]}}
[D 2025-07-30 10:41:07,614 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"5cf72a26-4d55-497e-b9f9-5cede8396388","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:41:07,622 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"5cf72a26-4d55-497e-b9f9-5cede8396388","result":{"styles":["auto"]}}
[D 2025-07-30 10:41:07,624 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"bae76717-f2a2-4c5c-9e5c-431743b6256d","method":"Element.tap","params":{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 10:41:07,680 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/notification_center/index"}]}}
[D 2025-07-30 10:41:07,729 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"bae76717-f2a2-4c5c-9e5c-431743b6256d","result":{"pageX":187.5,"pageY":348,"clientX":187.5,"clientY":348}}
[D 2025-07-30 10:41:09,262 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/notification_center/index","query":{},"openType":"navigateTo","timeStamp":1753843269254,"webviewId":9,"routeEventId":"9_1753843268629","renderer":"webview"},1753843269255]}}
[I 2025-07-30 10:41:09,263 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/notification_center/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843269254, 'webviewId': 9, 'routeEventId': '9_1753843268629', 'renderer': 'webview'}, 1753843269255]}
[D 2025-07-30 10:41:11,738 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"0ce81a61-d1c2-4372-925b-f2e06df0065f","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:41:11,739 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"0ce81a61-d1c2-4372-925b-f2e06df0065f","result":{"pageId":9,"path":"pages/notification_center/index","query":{}}}
[D 2025-07-30 10:41:11,740 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:41:11,740 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"f935131f-b11d-4018-ae98-be521871a7d5","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:41:11,742 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"f935131f-b11d-4018-ae98-be521871a7d5","result":{"result":{"pageId":9,"path":"pages/notification_center/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:41:11,742 minium page#716 _get_elements_by_css] try to get elements: .message-item, .notification-item
[D 2025-07-30 10:41:11,742 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"8b30ca10-e021-482d-9d5d-232a5af35e0f","method":"Page.getElements","params":{"selector":".message-item, .notification-item","pageId":9}}
[D 2025-07-30 10:41:11,747 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"8b30ca10-e021-482d-9d5d-232a5af35e0f","result":{"elements":[{"elementId":"46018972-da6a-4301-bd2f-ac2d691fa1f3","tagName":"view"},{"elementId":"ef86fbe1-a5b2-4082-a5a3-a26c74da7c48","tagName":"view"},{"elementId":"962e48db-dfdd-4d8b-ac18-ae78d20a47d0","tagName":"view"},{"elementId":"12203d39-03a8-44ff-b80a-11a3ca588dd0","tagName":"view"}]}}
[I 2025-07-30 10:41:11,747 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3989B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398370>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3984B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A398FF0>]
[D 2025-07-30 10:41:11,748 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"288d96f6-3608-4f09-9c91-45ed5bbc6672","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"46018972-da6a-4301-bd2f-ac2d691fa1f3","pageId":9}}
[D 2025-07-30 10:41:11,755 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"288d96f6-3608-4f09-9c91-45ed5bbc6672","result":{"styles":["auto"]}}
[D 2025-07-30 10:41:11,756 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"14ee74b0-47b3-40d7-9e95-09bbdd86904b","method":"Element.tap","params":{"elementId":"46018972-da6a-4301-bd2f-ac2d691fa1f3","pageId":9}}
[D 2025-07-30 10:41:11,822 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"14ee74b0-47b3-40d7-9e95-09bbdd86904b","result":{"pageX":188,"pageY":233,"clientX":188,"clientY":233}}
[I 2025-07-30 10:41:13,824 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:41:13,825 minium minitest#799 _miniTearDown] =========Current case Down: test_06_message_and_notification_system=========
[I 2025-07-30 10:41:13,826 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:41:13,827 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"2d6f3b19-970b-4245-a910-cc84367cc1dd","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:41:13,896 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"2d6f3b19-970b-4245-a910-cc84367cc1dd","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAFdWZN/ycU3frpheWBppm37eALYIQRSejWUAQCSo6TqKYIajAJKNmjO/rgqLzfkm+GJP5BJXhi0syjmJUBBFUIIkiEWmghbAj0A10X7ob6PUudW/V8/5xquqeu3bdprtvL88vM8dfn3vqnFOHqt99zu+cqssCahgAAIAxhojEiRMn3srcUhkCgUBoC3AAYIxZfxMnTpx463KKZQgEQtuCW6zjKB9x4sS7FG+LWMZyfQgEQueCrA6tBRHLyPW2hKMBg7RKncSJE29/3hb38mXFMhSzEAjdBJcT47TQl4nRlw4x9yNOnHibcZDu+jb3ZezHL9EFKeohEDogZF2wfUyacY3DftFm9QURSE0IhE6FyA2bfH4Sd
[I 2025-07-30 10:41:13,898 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:41:13,898 minium basenative#63 wrapper] call BaseNative.get_start_up end 
