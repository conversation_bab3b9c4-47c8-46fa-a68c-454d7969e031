#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
楠楠家厨小程序测试 - 使用正确端口25209
"""

import os
import sys
import subprocess

def main():
    print("🚀 开始运行楠楠家厨小程序测试")
    print("📱 小程序AppID: wx82283b353918af82")
    print("🔌 微信开发者工具端口: 25209")
    print("=" * 50)
    
    # 检查minium
    try:
        import minium
        print(f"✅ minium框架版本: {minium.__version__}")
    except ImportError:
        print("❌ minium框架未安装")
        return False
    
    # 直接运行简化测试
    try:
        result = subprocess.run([
            sys.executable, "tests/run_simple_tests.py"
        ], capture_output=False, text=True)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎉 测试完成！")
    else:
        print("❌ 测试失败！")
