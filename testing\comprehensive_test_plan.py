#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
楠楠家厨小程序全面测试计划
针对未测试功能的详细测试
"""

import os
import sys
import time
import json
import unittest
from datetime import datetime

try:
    from minium import MiniTest
    print("✅ minium导入成功")
except ImportError as e:
    print(f"❌ minium导入失败: {e}")
    sys.exit(1)


class ComprehensiveTestPlan(MiniTest):
    """全面测试计划"""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.test_results = []
        cls.failed_features = []
        print("🔧 全面测试初始化完成")
    
    def setUp(self):
        super().setUp()
        self.app = self.mini.app
        print(f"🚀 开始测试: {self._testMethodName}")
        time.sleep(1)
    
    def tearDown(self):
        print(f"✅ 测试完成: {self._testMethodName}")
        super().tearDown()
    
    def log_result(self, test_name, success, details="", critical=False):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        self.__class__.test_results.append(result)
        
        if not success and critical:
            self.__class__.failed_features.append(test_name)
        
        status = "✅" if success else "❌"
        priority = "🔥" if critical else "📋"
        print(f"{status} {priority} {test_name}: {details}")
    
    def test_01_user_authentication_system(self):
        """测试用户认证系统"""
        print("🧪 测试用户认证系统")
        
        try:
            # 测试登录页面
            self.app.navigate_to("/pages/login/index")
            time.sleep(2)
            
            login_page = self.app.get_current_page()
            self.assertIn("login", login_page.path)
            
            # 检查登录方式
            login_methods = {
                "微信登录": ".wechat-login-btn, button[bindtap*='wechat']",
                "账号登录切换": ".tab-item, .login-tab",
                "用户名输入": "input[placeholder*='用户名']",
                "密码输入": "input[placeholder*='密码']",
                "登录按钮": ".login-btn, button[bindtap*='login']"
            }
            
            for method_name, selector in login_methods.items():
                try:
                    element = login_page.get_element(selector)
                    if element:
                        self.log_result(f"登录功能-{method_name}", True, "元素存在", critical=True)
                    else:
                        self.log_result(f"登录功能-{method_name}", False, "元素不存在", critical=True)
                except:
                    self.log_result(f"登录功能-{method_name}", False, "查找失败", critical=True)
            
            # 测试注册页面跳转
            try:
                register_link = login_page.get_element(".register-link, text[bindtap*='register']")
                if register_link:
                    register_link.click()
                    time.sleep(2)
                    
                    register_page = self.app.get_current_page()
                    if "register" in register_page.path:
                        self.log_result("注册页面跳转", True, "成功跳转到注册页", critical=True)
                        
                        # 测试注册表单
                        self._test_register_form(register_page)
                    else:
                        self.log_result("注册页面跳转", False, "未跳转到注册页", critical=True)
                else:
                    self.log_result("注册入口", False, "未找到注册入口", critical=True)
            except Exception as e:
                self.log_result("注册功能测试", False, f"异常: {e}", critical=True)
            
        except Exception as e:
            self.log_result("用户认证系统", False, f"测试异常: {e}", critical=True)
    
    def _test_register_form(self, page):
        """测试注册表单"""
        register_fields = {
            "用户名输入": "input[placeholder*='用户名']",
            "手机号输入": "input[placeholder*='手机']",
            "密码输入": "input[placeholder*='密码']",
            "确认密码": "input[placeholder*='确认']",
            "注册按钮": ".register-btn, button[bindtap*='register']",
            "微信注册": ".wechat-register-btn, button[bindtap*='wechat']"
        }
        
        for field_name, selector in register_fields.items():
            try:
                element = page.get_element(selector)
                if element:
                    self.log_result(f"注册表单-{field_name}", True, "字段存在", critical=True)
                else:
                    self.log_result(f"注册表单-{field_name}", False, "字段不存在", critical=True)
            except:
                self.log_result(f"注册表单-{field_name}", False, "查找失败", critical=True)
    
    def test_02_dish_management_system(self):
        """测试菜品管理系统"""
        print("🧪 测试菜品管理系统")
        
        try:
            # 导航到个人中心
            self.app.switch_tab("/pages/mine/index")
            time.sleep(2)
            
            mine_page = self.app.get_current_page()
            
            # 测试我的菜品功能
            self._test_my_dishes_feature(mine_page)
            
            # 测试新增菜品功能
            self._test_add_dish_feature(mine_page)
            
        except Exception as e:
            self.log_result("菜品管理系统", False, f"测试异常: {e}", critical=True)
    
    def _test_my_dishes_feature(self, page):
        """测试我的菜品功能"""
        try:
            # 查找我的菜品入口
            my_dishes_found = False
            clickable_elements = page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                try:
                    text = element.inner_text
                    if "我的菜品" in text:
                        my_dishes_found = True
                        element.click()
                        time.sleep(3)
                        
                        current_page = self.app.get_current_page()
                        if "my-dishes" in current_page.path:
                            self.log_result("我的菜品页面", True, f"跳转到: {current_page.path}", critical=True)
                            
                            # 测试菜品列表
                            dish_items = current_page.get_elements(".dish-item, .dish-card")
                            self.log_result("我的菜品列表", True, f"找到{len(dish_items)}个菜品", critical=False)
                            
                            # 测试菜品操作
                            if dish_items:
                                self._test_dish_operations(current_page, dish_items[0])
                            
                            # 返回个人中心
                            self.app.navigate_back()
                            time.sleep(1)
                        else:
                            self.log_result("我的菜品页面", False, "未跳转到菜品页面", critical=True)
                        break
                except:
                    continue
            
            if not my_dishes_found:
                self.log_result("我的菜品入口", False, "未找到我的菜品入口", critical=True)
                
        except Exception as e:
            self.log_result("我的菜品功能", False, f"测试异常: {e}", critical=True)
    
    def _test_add_dish_feature(self, page):
        """测试新增菜品功能"""
        try:
            # 查找新增菜品入口
            add_dish_found = False
            clickable_elements = page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                try:
                    text = element.inner_text
                    if "新增菜品" in text or "添加菜品" in text:
                        add_dish_found = True
                        element.click()
                        time.sleep(3)
                        
                        current_page = self.app.get_current_page()
                        if "add" in current_page.path:
                            self.log_result("新增菜品页面", True, f"跳转到: {current_page.path}", critical=True)
                            
                            # 测试新增表单
                            self._test_add_dish_form(current_page)
                            
                            # 返回个人中心
                            self.app.navigate_back()
                            time.sleep(1)
                        else:
                            self.log_result("新增菜品页面", False, "未跳转到新增页面", critical=True)
                        break
                except:
                    continue
            
            if not add_dish_found:
                self.log_result("新增菜品入口", False, "未找到新增菜品入口", critical=True)
                
        except Exception as e:
            self.log_result("新增菜品功能", False, f"测试异常: {e}", critical=True)
    
    def _test_add_dish_form(self, page):
        """测试新增菜品表单"""
        form_fields = {
            "菜品名称": "input[placeholder*='名称'], input[placeholder*='菜品']",
            "菜品描述": "textarea, input[placeholder*='描述']",
            "配料信息": "input[placeholder*='配料'], input[placeholder*='材料']",
            "分类选择": ".category-selector, picker",
            "图片上传": ".upload, .image-upload",
            "提交按钮": ".submit-btn, button[bindtap*='submit']"
        }
        
        for field_name, selector in form_fields.items():
            try:
                element = page.get_element(selector)
                if element:
                    self.log_result(f"新增表单-{field_name}", True, "字段存在", critical=True)
                else:
                    self.log_result(f"新增表单-{field_name}", False, "字段不存在", critical=True)
            except:
                self.log_result(f"新增表单-{field_name}", False, "查找失败", critical=True)
    
    def _test_dish_operations(self, page, dish_item):
        """测试菜品操作"""
        try:
            # 测试菜品编辑
            edit_btns = dish_item.get_elements("button[bindtap*='edit'], .edit-btn")
            if edit_btns:
                self.log_result("菜品编辑功能", True, "编辑按钮存在", critical=False)
            else:
                self.log_result("菜品编辑功能", False, "编辑按钮不存在", critical=False)
            
            # 测试菜品删除
            delete_btns = dish_item.get_elements("button[bindtap*='delete'], .delete-btn")
            if delete_btns:
                self.log_result("菜品删除功能", True, "删除按钮存在", critical=False)
            else:
                self.log_result("菜品删除功能", False, "删除按钮不存在", critical=False)
                
        except Exception as e:
            self.log_result("菜品操作功能", False, f"测试异常: {e}", critical=False)
    
    def test_03_complete_order_flow(self):
        """测试完整订餐流程"""
        print("🧪 测试完整订餐流程")
        
        try:
            # 进入订餐页面
            self.app.switch_tab("/pages/order/index")
            time.sleep(3)
            
            order_page = self.app.get_current_page()
            
            # 测试菜品分类
            self._test_dish_categories(order_page)
            
            # 测试购物车功能
            self._test_shopping_cart(order_page)
            
            # 测试订单创建
            self._test_order_creation(order_page)
            
        except Exception as e:
            self.log_result("完整订餐流程", False, f"测试异常: {e}", critical=True)
    
    def _test_dish_categories(self, page):
        """测试菜品分类"""
        try:
            # 查找分类选择器
            category_selectors = page.get_elements(".category-selector, .categories, .tabs")
            if category_selectors:
                self.log_result("菜品分类选择器", True, f"找到{len(category_selectors)}个分类选择器", critical=True)
                
                # 测试分类切换
                category_items = page.get_elements(".category-item, .tab-item")
                if len(category_items) > 1:
                    # 点击第二个分类
                    category_items[1].click()
                    time.sleep(2)
                    self.log_result("分类切换功能", True, "分类切换成功", critical=True)
                else:
                    self.log_result("分类切换功能", False, "分类项不足", critical=True)
            else:
                self.log_result("菜品分类选择器", False, "未找到分类选择器", critical=True)
                
        except Exception as e:
            self.log_result("菜品分类功能", False, f"测试异常: {e}", critical=True)
    
    def _test_shopping_cart(self, page):
        """测试购物车功能"""
        try:
            # 查找购物车相关元素
            cart_elements = page.get_elements(".cart, .basket, .shopping-cart, button[bindtap*='cart']")
            if cart_elements:
                self.log_result("购物车元素", True, f"找到{len(cart_elements)}个购物车元素", critical=True)
                
                # 尝试点击购物车
                cart_elements[0].click()
                time.sleep(2)
                
                current_page = self.app.get_current_page()
                if "today_order" in current_page.path or "cart" in current_page.path:
                    self.log_result("购物车页面跳转", True, f"跳转到: {current_page.path}", critical=True)
                    
                    # 测试购物车页面功能
                    self._test_cart_page_features(current_page)
                    
                    # 返回订餐页
                    self.app.navigate_back()
                    time.sleep(1)
                else:
                    self.log_result("购物车页面跳转", False, "未跳转到购物车页面", critical=True)
            else:
                self.log_result("购物车元素", False, "未找到购物车元素", critical=True)
                
        except Exception as e:
            self.log_result("购物车功能", False, f"测试异常: {e}", critical=True)
    
    def _test_cart_page_features(self, page):
        """测试购物车页面功能"""
        try:
            # 检查购物车商品列表
            cart_items = page.get_elements(".cart-item, .order-item")
            self.log_result("购物车商品列表", True, f"找到{len(cart_items)}个商品", critical=True)
            
            # 检查数量调整功能
            quantity_controls = page.get_elements(".quantity-control, .stepper, .van-stepper")
            if quantity_controls:
                self.log_result("数量调整功能", True, f"找到{len(quantity_controls)}个数量控制器", critical=True)
            else:
                self.log_result("数量调整功能", False, "未找到数量控制器", critical=True)
            
            # 检查提交订单按钮
            submit_btns = page.get_elements(".submit-btn, .confirm-btn, button[bindtap*='submit']")
            if submit_btns:
                self.log_result("提交订单按钮", True, "提交按钮存在", critical=True)
            else:
                self.log_result("提交订单按钮", False, "提交按钮不存在", critical=True)
                
        except Exception as e:
            self.log_result("购物车页面功能", False, f"测试异常: {e}", critical=True)
    
    def _test_order_creation(self, page):
        """测试订单创建"""
        try:
            # 添加菜品到购物车
            dish_elements = page.get_elements(".dish-card, .food-item")
            if dish_elements:
                # 尝试添加第一个菜品
                first_dish = dish_elements[0]
                add_btns = first_dish.get_elements(".add-btn, .plus-btn, button[bindtap*='add']")
                
                if add_btns:
                    add_btns[0].click()
                    time.sleep(1)
                    self.log_result("添加菜品到购物车", True, "菜品添加成功", critical=True)
                else:
                    self.log_result("添加菜品到购物车", False, "未找到添加按钮", critical=True)
            else:
                self.log_result("菜品列表", False, "未找到菜品", critical=True)
                
        except Exception as e:
            self.log_result("订单创建功能", False, f"测试异常: {e}", critical=True)


def run_comprehensive_test():
    """运行全面测试"""
    print("🚀 开始运行楠楠家厨小程序全面功能测试")
    print("=" * 70)
    print("📱 小程序AppID: wx82283b353918af82")
    print("🔌 微信开发者工具端口: 25209")
    print("🧪 测试重点: 未测试功能的全面验证")
    print("=" * 70)
    
    start_time = datetime.now()
    
    # 运行测试
    suite = unittest.TestLoader().loadTestsFromTestCase(ComprehensiveTestPlan)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 获取详细测试结果
    detailed_results = ComprehensiveTestPlan.test_results
    failed_features = ComprehensiveTestPlan.failed_features
    
    # 统计结果
    total_functional = len(detailed_results)
    passed_functional = sum(1 for r in detailed_results if r['success'])
    failed_functional = total_functional - passed_functional
    critical_failed = len([r for r in detailed_results if not r['success'] and r.get('critical', False)])
    
    # 生成报告
    comprehensive_report = {
        "test_time": start_time.isoformat(),
        "duration_seconds": duration,
        "framework_tests": {
            "total": result.testsRun,
            "passed": result.testsRun - len(result.failures) - len(result.errors),
            "failed": len(result.failures),
            "errors": len(result.errors)
        },
        "functional_tests": {
            "total": total_functional,
            "passed": passed_functional,
            "failed": failed_functional,
            "critical_failed": critical_failed
        },
        "overall_success": result.wasSuccessful() and critical_failed == 0,
        "failed_critical_features": failed_features,
        "detailed_results": detailed_results,
        "test_coverage": {
            "user_authentication": "用户认证系统测试",
            "dish_management": "菜品管理系统测试",
            "order_flow": "完整订餐流程测试"
        }
    }
    
    # 保存报告
    with open("testing/comprehensive_test_report.json", 'w', encoding='utf-8') as f:
        json.dump(comprehensive_report, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print("\n" + "=" * 70)
    print("🎯 全面功能测试总结")
    print("=" * 70)
    print(f"⏱️ 测试耗时: {duration:.2f} 秒")
    print(f"📊 框架测试: {comprehensive_report['framework_tests']['passed']}/{comprehensive_report['framework_tests']['total']} 通过")
    print(f"🔧 功能测试: {passed_functional}/{total_functional} 通过")
    print(f"🔥 关键功能失败: {critical_failed} 个")
    
    if total_functional > 0:
        success_rate = passed_functional / total_functional * 100
        print(f"🎯 功能成功率: {success_rate:.1f}%")
    
    if failed_features:
        print(f"\n❌ 失败的关键功能:")
        for feature in failed_features:
            print(f"  - {feature}")
    
    print(f"\n📊 详细报告: testing/comprehensive_test_report.json")
    print("=" * 70)
    
    return comprehensive_report['overall_success']


if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print("🎉 全面功能测试成功！")
    else:
        print("⚠️ 发现关键功能问题，需要修复")
