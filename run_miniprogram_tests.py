#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
楠楠家厨小程序自动化测试启动脚本
"""

import os
import sys
import subprocess
import time
import shutil
from pathlib import Path


def print_banner():
    """打印测试横幅"""
    print("=" * 70)
    print("🏠 楠楠家厨小程序自动化测试")
    print("=" * 70)
    print("📱 小程序AppID: wx82283b353918af82")
    print("🌐 测试环境: http://8.148.231.104:3000/api")
    print("🧪 测试框架: minium")
    print("=" * 70)


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True


def install_dependencies():
    """安装测试依赖"""
    print("\n📦 安装测试依赖...")
    
    requirements_file = "tests/requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"❌ 依赖文件不存在: {requirements_file}")
        return False
    
    try:
        # 安装依赖
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], check=True, capture_output=True, text=True)
        
        print("✅ 依赖安装完成")
        return True
    
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def check_minium_installation():
    """检查minium是否正确安装"""
    try:
        import minium
        print(f"✅ minium框架已安装: {minium.__version__}")
        return True
    except ImportError:
        print("❌ minium框架未安装")
        return False


def setup_test_environment():
    """设置测试环境"""
    print("\n🔧 设置测试环境...")
    
    # 创建必要的目录
    directories = [
        "tests/reports",
        "tests/reports/screenshots"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"📁 创建目录: {directory}")
    
    # 检查配置文件
    config_file = "tests/config/minium.json"
    if os.path.exists(config_file):
        print(f"✅ 配置文件存在: {config_file}")
    else:
        print(f"⚠️ 配置文件不存在: {config_file}")
    
    return True


def run_tests():
    """运行测试"""
    print("\n🚀 开始运行测试...")
    
    # 切换到项目根目录
    original_dir = os.getcwd()
    
    try:
        # 运行测试
        test_script = "tests/run_tests.py"
        if not os.path.exists(test_script):
            print(f"❌ 测试脚本不存在: {test_script}")
            return False
        
        # 执行测试
        result = subprocess.run([
            sys.executable, test_script
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print("\n✅ 测试执行完成")
            return True
        else:
            print(f"\n❌ 测试执行失败，退出码: {result.returncode}")
            return False
    
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")
        return False
    
    finally:
        os.chdir(original_dir)


def show_test_results():
    """显示测试结果"""
    print("\n📊 测试结果:")
    
    # 检查报告文件
    reports = [
        ("JSON报告", "tests/reports/test_report.json"),
        ("HTML报告", "tests/reports/test_report.html")
    ]
    
    for report_name, report_path in reports:
        if os.path.exists(report_path):
            file_size = os.path.getsize(report_path)
            print(f"✅ {report_name}: {report_path} ({file_size} bytes)")
        else:
            print(f"❌ {report_name}: {report_path} (不存在)")
    
    # 检查截图
    screenshots_dir = "tests/reports/screenshots"
    if os.path.exists(screenshots_dir):
        screenshots = list(Path(screenshots_dir).glob("*.png"))
        print(f"📸 测试截图: {len(screenshots)} 张")
    else:
        print("📸 测试截图: 0 张")


def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    # 询问用户是否要清理
    response = input("是否要删除所有测试代码和报告？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        try:
            # 删除测试目录
            if os.path.exists("tests"):
                shutil.rmtree("tests")
                print("✅ 已删除 tests/ 目录")
            
            # 删除测试相关文件
            test_files = [
                "run_miniprogram_tests.py",
                "test_plan.md"
            ]
            
            for file_path in test_files:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"✅ 已删除 {file_path}")
            
            print("🎉 测试文件清理完成")
            
        except Exception as e:
            print(f"❌ 清理失败: {e}")
    else:
        print("⏭️ 跳过清理，测试文件保留")


def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    if not install_dependencies():
        return 1
    
    # 检查minium安装
    if not check_minium_installation():
        return 1
    
    # 设置测试环境
    if not setup_test_environment():
        return 1
    
    # 运行测试
    test_success = run_tests()
    
    # 显示测试结果
    show_test_results()
    
    # 清理测试文件
    cleanup_test_files()
    
    if test_success:
        print("\n🎉 测试流程完成！")
        return 0
    else:
        print("\n❌ 测试流程失败！")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
