[I 2025-07-30 10:40:56,558 minium minitest#432 _miniSetUp] =========Current case: test_05_dish_management_functionality=========
[I 2025-07-30 10:40:56,559 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_05_dish_management_functionality
[I 2025-07-30 10:40:56,559 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:56,559 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:56,560 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:56,560 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:56,560 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:56,561 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:56,561 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"d95c4747-4a46-4c97-8ca1-b4f8d95b5f1d","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:56,657 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"d95c4747-4a46-4c97-8ca1-b4f8d95b5f1d","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:40:56,659 minium minitest#487 _miniSetUp] =========case: test_05_dish_management_functionality start=========
[I 2025-07-30 10:40:57,659 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:57,660 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [2264960766976]{"id":"1eb7600a-dcdd-4c58-8d69-afe138eba6f2","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:57,664 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:40:57,923 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843257909,"webviewId":3,"routeEventId":"3_1753843257681","renderer":"webview"},1753843257912]}}
[I 2025-07-30 10:40:57,925 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843257909, 'webviewId': 3, 'routeEventId': '3_1753843257681', 'renderer': 'webview'}, 1753843257912]}
[D 2025-07-30 10:40:57,935 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"1eb7600a-dcdd-4c58-8d69-afe138eba6f2","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:57,936 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 1eb7600a-dcdd-4c58-8d69-afe138eba6f2
[D 2025-07-30 10:40:59,941 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"1b2d98a1-4522-4653-92ac-2f418d7580f8","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:59,942 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"1b2d98a1-4522-4653-92ac-2f418d7580f8","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:40:59,943 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:59,943 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"280713fb-74b5-4492-a377-7e5744a86d12","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:59,945 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"280713fb-74b5-4492-a377-7e5744a86d12","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:59,945 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:40:59,945 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"716765dc-cb2a-4094-9f1a-05a1baa7ac9f","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 10:40:59,948 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"716765dc-cb2a-4094-9f1a-05a1baa7ac9f","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 10:40:59,950 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2F7F20>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A316510>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3809F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A320EF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2DB050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A2DAF90>]
[D 2025-07-30 10:40:59,951 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"964f5fcc-b94d-4e17-9bea-56c3a7a0a0f2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 10:40:59,955 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"964f5fcc-b94d-4e17-9bea-56c3a7a0a0f2","result":{"properties":["留言"]}}
[D 2025-07-30 10:40:59,956 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"0a40d0b7-847c-4de8-9ef4-8905c6f61a09","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 10:40:59,958 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"0a40d0b7-847c-4de8-9ef4-8905c6f61a09","result":{"properties":["用户关联"]}}
[D 2025-07-30 10:40:59,959 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"3b01979b-2df2-4dd3-95f0-328004277da5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:40:59,962 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"3b01979b-2df2-4dd3-95f0-328004277da5","result":{"properties":["我的菜品"]}}
[D 2025-07-30 10:40:59,962 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"469e24b2-bc7c-4899-8f73-4a95ce9e452d","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:40:59,965 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"469e24b2-bc7c-4899-8f73-4a95ce9e452d","result":{"styles":["auto"]}}
[D 2025-07-30 10:40:59,969 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"2047b9c1-ab20-4f57-b2f6-662b93a920f2","method":"Element.tap","params":{"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 10:41:00,029 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/dish/my-dishes/index"}]}}
[D 2025-07-30 10:41:00,064 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"2047b9c1-ab20-4f57-b2f6-662b93a920f2","result":{"pageX":187.5,"pageY":296,"clientX":187.5,"clientY":296}}
[D 2025-07-30 10:41:01,824 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/dish/my-dishes/index","query":{},"openType":"navigateTo","timeStamp":1753843261812,"webviewId":8,"routeEventId":"8_1753843261136","renderer":"webview"},1753843261814]}}
[I 2025-07-30 10:41:01,825 minium.App1680 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/dish/my-dishes/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843261812, 'webviewId': 8, 'routeEventId': '8_1753843261136', 'renderer': 'webview'}, 1753843261814]}
[D 2025-07-30 10:41:04,066 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"e532dfa4-ae08-4715-8fbf-0725decd13f5","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:41:04,070 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"e532dfa4-ae08-4715-8fbf-0725decd13f5","result":{"pageId":8,"path":"pages/dish/my-dishes/index","query":{}}}
[D 2025-07-30 10:41:04,071 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:41:04,071 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"8a55d511-1e33-4f40-a56f-65f0bfb9c876","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:41:04,074 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"8a55d511-1e33-4f40-a56f-65f0bfb9c876","result":{"result":{"pageId":8,"path":"pages/dish/my-dishes/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:41:04,074 minium page#716 _get_elements_by_css] try to get elements: .dish-item, .dish-card
[D 2025-07-30 10:41:04,075 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"6f0c7ed1-b829-4b9c-a8ab-dad0efb11029","method":"Page.getElements","params":{"selector":".dish-item, .dish-card","pageId":8}}
[D 2025-07-30 10:41:04,081 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"6f0c7ed1-b829-4b9c-a8ab-dad0efb11029","result":{"elements":[{"elementId":"fd50e1e8-f09a-43c0-af62-12b71d4acb3e","tagName":"view"}]}}
[I 2025-07-30 10:41:04,082 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000020F5A3315A0>]
[I 2025-07-30 10:41:04,083 minium page#716 _get_elements_by_css] try to get elements: button, .btn
[D 2025-07-30 10:41:04,084 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"0f188e45-88cc-4df3-a39e-c2dac6de5683","method":"Page.getElements","params":{"selector":"button, .btn","pageId":8}}
[D 2025-07-30 10:41:04,089 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"0f188e45-88cc-4df3-a39e-c2dac6de5683","result":{"elements":[]}}
[W 2025-07-30 10:41:04,089 minium page#747 _get_elements_by_css] Could not found any element 'button, .btn' you need
[I 2025-07-30 10:41:04,089 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:41:04,090 minium minitest#799 _miniTearDown] =========Current case Down: test_05_dish_management_functionality=========
[I 2025-07-30 10:41:04,090 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:41:04,090 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"f643440f-e8dc-4302-852b-e7efcba2c7c7","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:41:04,165 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"f643440f-e8dc-4302-852b-e7efcba2c7c7","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmAHHWZ//88n6OO7p47mdwJIYRDIIIIqKDcrK6Au3J4AbuogIDIoj9BXb+7uruC4NcLRAW+ysq1CwIuBFflUEFQDjkMIBCOQO5jMkdfdXyO5/dH9fR0JpNMTzITBvJ5ocW7q6qru4qpdz/P8zkK41QDAAAgIhE57bTTTo+zrruMw+FwTAQMABCx/tppp512eny1i2UcDsfEwupq8jif0047/ZbSLpZxOBwTShbLYMMap5122unx1C6WcTgcE4uryzjttNMTrF0s43A4JhQ2+i4Oh8OxHTiXcTgcE0uDyzTkUU477bTT46W3pS5THwTlcDh2NhrLuk0ixrT3SP5Cg5vG+tEOh2NS0xiO1FVmAmPymqZdhoCGD
[I 2025-07-30 10:41:04,167 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:41:04,167 minium basenative#63 wrapper] call BaseNative.get_start_up end 
