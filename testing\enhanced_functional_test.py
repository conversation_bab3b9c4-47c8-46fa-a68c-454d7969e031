#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
楠楠家厨小程序增强功能测试
包括购物车、菜品管理、用户关联等功能的深度测试
"""

import os
import sys
import time
import json
import unittest
from datetime import datetime

try:
    from minium import MiniTest
    print("✅ minium导入成功")
except ImportError as e:
    print(f"❌ minium导入失败: {e}")
    sys.exit(1)


class EnhancedFunctionalTest(MiniTest):
    """增强功能测试"""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.test_results = []
        print("🔧 增强功能测试初始化完成")
    
    def setUp(self):
        super().setUp()
        self.app = self.mini.app
        print(f"🚀 开始测试: {self._testMethodName}")
        time.sleep(1)
    
    def tearDown(self):
        print(f"✅ 测试完成: {self._testMethodName}")
        super().tearDown()
    
    def log_result(self, test_name, success, details="", critical=False):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        self.__class__.test_results.append(result)
        
        status = "✅" if success else "❌"
        priority = "🔥" if critical else "📋"
        print(f"{status} {priority} {test_name}: {details}")
    
    def test_01_enhanced_order_flow_with_cart(self):
        """测试增强的订餐流程（重点测试购物车）"""
        print("🧪 测试增强的订餐流程（重点测试购物车）")
        
        try:
            # 导航到订餐页面
            self.app.switch_tab("/pages/order/index")
            time.sleep(3)
            
            order_page = self.app.get_current_page()
            self.assertIn("order", order_page.path)
            
            # 详细测试菜品数据
            self._test_detailed_dish_data(order_page)
            
            # 重点测试购物车功能
            self._test_enhanced_cart_functionality(order_page)
            
            # 测试订单提交流程
            self._test_order_submission_flow()
            
        except Exception as e:
            self.log_result("增强订餐流程测试", False, f"测试异常: {e}", critical=True)
    
    def _test_detailed_dish_data(self, page):
        """详细测试菜品数据"""
        try:
            # 等待数据加载
            time.sleep(3)
            
            # 获取所有菜品元素
            dish_elements = page.get_elements("view, .dish-card, .food-item, .menu-item")
            self.log_result("页面元素总数", True, f"找到{len(dish_elements)}个页面元素", critical=False)
            
            # 查找具体的菜品容器
            dish_containers = []
            for element in dish_elements:
                try:
                    text = element.inner_text
                    if text and ("菜" in text or "肉" in text or "汤" in text):
                        dish_containers.append(element)
                except:
                    continue
            
            self.log_result("菜品容器识别", True, f"识别出{len(dish_containers)}个菜品容器", critical=True)
            
            if dish_containers:
                # 测试第一个菜品的交互
                first_dish = dish_containers[0]
                dish_text = first_dish.inner_text[:50] if first_dish.inner_text else "未知菜品"
                self.log_result("菜品内容", True, f"菜品信息: {dish_text}", critical=False)
                
                # 查找添加按钮
                add_buttons = first_dish.get_elements("button, view[bindtap]")
                if add_buttons:
                    self.log_result("菜品交互按钮", True, f"找到{len(add_buttons)}个交互按钮", critical=True)
                    
                    # 尝试点击添加按钮
                    for i, btn in enumerate(add_buttons[:3]):  # 只测试前3个按钮
                        try:
                            btn.click()
                            time.sleep(1)
                            self.log_result(f"按钮点击-{i+1}", True, "按钮点击成功", critical=True)
                        except Exception as e:
                            self.log_result(f"按钮点击-{i+1}", False, f"点击失败: {e}", critical=False)
                else:
                    self.log_result("菜品交互按钮", False, "未找到交互按钮", critical=True)
            else:
                self.log_result("菜品数据", False, "未找到菜品数据", critical=True)
                
        except Exception as e:
            self.log_result("详细菜品数据测试", False, f"测试异常: {e}", critical=True)
    
    def _test_enhanced_cart_functionality(self, page):
        """增强的购物车功能测试"""
        try:
            # 查找所有可能的购物车相关元素
            all_elements = page.get_elements("view, button, text")
            cart_related = []
            
            for element in all_elements:
                try:
                    text = element.inner_text
                    if text and ("购物车" in text or "cart" in text.lower() or "今日" in text or "订单" in text):
                        cart_related.append(element)
                except:
                    continue
            
            self.log_result("购物车相关元素", True, f"找到{len(cart_related)}个购物车相关元素", critical=True)
            
            if cart_related:
                # 尝试点击购物车相关元素
                for i, element in enumerate(cart_related[:2]):  # 只测试前2个
                    try:
                        element_text = element.inner_text[:20] if element.inner_text else f"元素{i+1}"
                        element.click()
                        time.sleep(3)
                        
                        # 检查页面变化
                        current_page = self.app.get_current_page()
                        self.log_result(f"购物车跳转-{i+1}", True, f"点击'{element_text}'，当前页面: {current_page.path}", critical=True)
                        
                        # 如果跳转到了新页面，测试该页面功能
                        if current_page.path != page.path:
                            self._test_cart_or_order_page(current_page)
                            
                            # 返回订餐页面
                            self.app.navigate_back()
                            time.sleep(2)
                        
                    except Exception as e:
                        self.log_result(f"购物车元素点击-{i+1}", False, f"点击失败: {e}", critical=False)
            
            # 测试底部购物车按钮
            self._test_bottom_cart_button(page)
            
        except Exception as e:
            self.log_result("增强购物车功能测试", False, f"测试异常: {e}", critical=True)
    
    def _test_bottom_cart_button(self, page):
        """测试底部购物车按钮"""
        try:
            # 查找底部固定的购物车按钮
            bottom_elements = page.get_elements(".cart-bottom, .bottom-bar, .fixed-bottom")
            if bottom_elements:
                self.log_result("底部购物车区域", True, f"找到{len(bottom_elements)}个底部区域", critical=True)
                
                for bottom in bottom_elements:
                    cart_buttons = bottom.get_elements("button, view[bindtap]")
                    if cart_buttons:
                        self.log_result("底部购物车按钮", True, f"找到{len(cart_buttons)}个底部按钮", critical=True)
                        
                        # 尝试点击
                        cart_buttons[0].click()
                        time.sleep(3)
                        
                        current_page = self.app.get_current_page()
                        self.log_result("底部购物车跳转", True, f"跳转到: {current_page.path}", critical=True)
                        break
            else:
                # 查找页面底部的任何按钮
                all_buttons = page.get_elements("button")
                if all_buttons:
                    # 尝试点击最后几个按钮（通常在底部）
                    for btn in all_buttons[-3:]:
                        try:
                            btn.click()
                            time.sleep(2)
                            
                            current_page = self.app.get_current_page()
                            if current_page.path != page.path:
                                self.log_result("底部按钮跳转", True, f"跳转到: {current_page.path}", critical=True)
                                self.app.navigate_back()
                                time.sleep(1)
                                break
                        except:
                            continue
                
        except Exception as e:
            self.log_result("底部购物车按钮测试", False, f"测试异常: {e}", critical=False)
    
    def _test_cart_or_order_page(self, page):
        """测试购物车或订单页面"""
        try:
            page_path = page.path
            
            # 检查页面类型
            if "today_order" in page_path:
                self.log_result("今日订单页面", True, "成功进入今日订单页面", critical=True)
                self._test_today_order_page(page)
            elif "order" in page_path:
                self.log_result("订单相关页面", True, f"进入订单页面: {page_path}", critical=True)
                self._test_order_page_features(page)
            else:
                self.log_result("页面识别", False, f"未知页面类型: {page_path}", critical=False)
                
        except Exception as e:
            self.log_result("购物车/订单页面测试", False, f"测试异常: {e}", critical=False)
    
    def _test_today_order_page(self, page):
        """测试今日订单页面"""
        try:
            # 检查页面元素
            order_items = page.get_elements(".order-item, .dish-item, .cart-item")
            self.log_result("今日订单商品", True, f"找到{len(order_items)}个订单商品", critical=True)
            
            # 检查操作按钮
            action_buttons = page.get_elements("button")
            self.log_result("今日订单操作按钮", True, f"找到{len(action_buttons)}个操作按钮", critical=True)
            
            # 查找提交相关按钮
            submit_buttons = []
            for btn in action_buttons:
                try:
                    text = btn.inner_text
                    if text and ("提交" in text or "确认" in text or "下单" in text):
                        submit_buttons.append(btn)
                except:
                    continue
            
            if submit_buttons:
                self.log_result("订单提交按钮", True, f"找到{len(submit_buttons)}个提交按钮", critical=True)
                
                # 尝试提交订单
                submit_buttons[0].click()
                time.sleep(3)
                self.log_result("订单提交操作", True, "订单提交操作完成", critical=True)
            else:
                self.log_result("订单提交按钮", False, "未找到提交按钮", critical=True)
                
        except Exception as e:
            self.log_result("今日订单页面测试", False, f"测试异常: {e}", critical=True)
    
    def _test_order_page_features(self, page):
        """测试订单页面功能"""
        try:
            # 检查订单列表
            order_elements = page.get_elements(".order, .order-card, .order-item")
            self.log_result("订单列表", True, f"找到{len(order_elements)}个订单", critical=True)
            
            # 检查订单详情
            if order_elements:
                first_order = order_elements[0]
                first_order.click()
                time.sleep(2)
                
                current_page = self.app.get_current_page()
                if "detail" in current_page.path:
                    self.log_result("订单详情跳转", True, f"跳转到详情页: {current_page.path}", critical=True)
                    self.app.navigate_back()
                    time.sleep(1)
                
        except Exception as e:
            self.log_result("订单页面功能测试", False, f"测试异常: {e}", critical=False)
    
    def _test_order_submission_flow(self):
        """测试订单提交流程"""
        try:
            # 检查当前页面
            current_page = self.app.get_current_page()
            
            # 查找确认或成功提示
            success_elements = current_page.get_elements(".success, .toast, .modal")
            if success_elements:
                self.log_result("订单提交反馈", True, "找到提交反馈元素", critical=True)
            
            # 检查是否有订单号或确认信息
            all_text_elements = current_page.get_elements("text, view")
            order_info_found = False
            
            for element in all_text_elements:
                try:
                    text = element.inner_text
                    if text and ("订单" in text or "成功" in text or "确认" in text):
                        order_info_found = True
                        self.log_result("订单确认信息", True, f"找到确认信息: {text[:30]}", critical=True)
                        break
                except:
                    continue
            
            if not order_info_found:
                self.log_result("订单确认信息", False, "未找到订单确认信息", critical=False)
                
        except Exception as e:
            self.log_result("订单提交流程测试", False, f"测试异常: {e}", critical=False)
    
    def test_02_dish_management_deep_test(self):
        """菜品管理深度测试"""
        print("🧪 菜品管理深度测试")
        
        try:
            # 导航到个人中心
            self.app.switch_tab("/pages/mine/index")
            time.sleep(2)
            
            mine_page = self.app.get_current_page()
            
            # 测试我的菜品功能
            self._test_my_dishes_deep(mine_page)
            
            # 测试新增菜品功能
            self._test_add_dish_deep(mine_page)
            
        except Exception as e:
            self.log_result("菜品管理深度测试", False, f"测试异常: {e}", critical=True)
    
    def _test_my_dishes_deep(self, page):
        """深度测试我的菜品功能"""
        try:
            # 查找我的菜品入口
            all_elements = page.get_elements("view, text, button")
            my_dishes_elements = []
            
            for element in all_elements:
                try:
                    text = element.inner_text
                    if text and ("我的菜品" in text or "菜品管理" in text):
                        my_dishes_elements.append(element)
                except:
                    continue
            
            if my_dishes_elements:
                self.log_result("我的菜品入口", True, f"找到{len(my_dishes_elements)}个菜品管理入口", critical=True)
                
                # 点击进入我的菜品
                my_dishes_elements[0].click()
                time.sleep(3)
                
                current_page = self.app.get_current_page()
                if "dish" in current_page.path or "my-dishes" in current_page.path:
                    self.log_result("我的菜品页面", True, f"成功进入: {current_page.path}", critical=True)
                    
                    # 测试菜品列表
                    self._test_dish_list_operations(current_page)
                    
                    # 返回个人中心
                    self.app.navigate_back()
                    time.sleep(1)
                else:
                    self.log_result("我的菜品页面", False, f"未跳转到菜品页面，当前: {current_page.path}", critical=True)
            else:
                self.log_result("我的菜品入口", False, "未找到我的菜品入口", critical=True)
                
        except Exception as e:
            self.log_result("我的菜品深度测试", False, f"测试异常: {e}", critical=True)
    
    def _test_dish_list_operations(self, page):
        """测试菜品列表操作"""
        try:
            # 检查菜品列表
            dish_items = page.get_elements(".dish-item, .dish-card, .food-item")
            self.log_result("我的菜品列表", True, f"找到{len(dish_items)}个我的菜品", critical=True)
            
            if dish_items:
                # 测试第一个菜品的操作
                first_dish = dish_items[0]
                
                # 查找操作按钮
                operation_buttons = first_dish.get_elements("button")
                self.log_result("菜品操作按钮", True, f"找到{len(operation_buttons)}个操作按钮", critical=True)
                
                # 尝试点击操作按钮
                for i, btn in enumerate(operation_buttons[:2]):  # 只测试前2个按钮
                    try:
                        btn.click()
                        time.sleep(2)
                        self.log_result(f"菜品操作-{i+1}", True, "操作执行成功", critical=False)
                    except Exception as e:
                        self.log_result(f"菜品操作-{i+1}", False, f"操作失败: {e}", critical=False)
            
            # 查找新增菜品按钮
            add_buttons = page.get_elements("button")
            add_dish_buttons = []
            
            for btn in add_buttons:
                try:
                    text = btn.inner_text
                    if text and ("新增" in text or "添加" in text or "+" in text):
                        add_dish_buttons.append(btn)
                except:
                    continue
            
            if add_dish_buttons:
                self.log_result("新增菜品按钮", True, f"找到{len(add_dish_buttons)}个新增按钮", critical=True)
                
                # 尝试点击新增
                add_dish_buttons[0].click()
                time.sleep(3)
                
                current_page = self.app.get_current_page()
                if "add" in current_page.path:
                    self.log_result("新增菜品页面", True, f"跳转到新增页面: {current_page.path}", critical=True)
                    self.app.navigate_back()
                    time.sleep(1)
            else:
                self.log_result("新增菜品按钮", False, "未找到新增菜品按钮", critical=True)
                
        except Exception as e:
            self.log_result("菜品列表操作测试", False, f"测试异常: {e}", critical=True)
    
    def _test_add_dish_deep(self, page):
        """深度测试新增菜品功能"""
        try:
            # 在个人中心查找新增菜品的直接入口
            all_elements = page.get_elements("view, text, button")
            add_dish_elements = []
            
            for element in all_elements:
                try:
                    text = element.inner_text
                    if text and ("新增菜品" in text or "添加菜品" in text):
                        add_dish_elements.append(element)
                except:
                    continue
            
            if add_dish_elements:
                self.log_result("新增菜品直接入口", True, f"找到{len(add_dish_elements)}个新增菜品入口", critical=True)
                
                # 点击新增菜品
                add_dish_elements[0].click()
                time.sleep(3)
                
                current_page = self.app.get_current_page()
                if "add" in current_page.path:
                    self.log_result("新增菜品页面跳转", True, f"成功跳转: {current_page.path}", critical=True)
                    
                    # 测试新增表单
                    self._test_add_dish_form(current_page)
                    
                    # 返回个人中心
                    self.app.navigate_back()
                    time.sleep(1)
                else:
                    self.log_result("新增菜品页面跳转", False, f"未跳转到新增页面: {current_page.path}", critical=True)
            else:
                self.log_result("新增菜品直接入口", False, "个人中心未找到新增菜品直接入口", critical=True)
                
        except Exception as e:
            self.log_result("新增菜品深度测试", False, f"测试异常: {e}", critical=True)
    
    def _test_add_dish_form(self, page):
        """测试新增菜品表单"""
        try:
            # 检查表单字段
            input_elements = page.get_elements("input, textarea")
            self.log_result("新增菜品表单字段", True, f"找到{len(input_elements)}个输入字段", critical=True)
            
            # 检查上传组件
            upload_elements = page.get_elements(".upload, .image-upload, .van-uploader")
            if upload_elements:
                self.log_result("图片上传组件", True, f"找到{len(upload_elements)}个上传组件", critical=True)
            else:
                self.log_result("图片上传组件", False, "未找到图片上传组件", critical=True)
            
            # 检查提交按钮
            submit_buttons = page.get_elements("button")
            submit_found = False
            
            for btn in submit_buttons:
                try:
                    text = btn.inner_text
                    if text and ("提交" in text or "保存" in text or "确认" in text):
                        submit_found = True
                        self.log_result("新增菜品提交按钮", True, f"找到提交按钮: {text}", critical=True)
                        break
                except:
                    continue
            
            if not submit_found:
                self.log_result("新增菜品提交按钮", False, "未找到提交按钮", critical=True)
                
        except Exception as e:
            self.log_result("新增菜品表单测试", False, f"测试异常: {e}", critical=True)
    
    def test_03_user_connection_enhanced_test(self):
        """用户关联增强测试"""
        print("🧪 用户关联增强测试")
        
        try:
            # 导航到个人中心
            self.app.switch_tab("/pages/mine/index")
            time.sleep(2)
            
            mine_page = self.app.get_current_page()
            
            # 测试用户关联入口和功能
            self._test_user_connection_enhanced(mine_page)
            
        except Exception as e:
            self.log_result("用户关联增强测试", False, f"测试异常: {e}", critical=True)
    
    def _test_user_connection_enhanced(self, page):
        """增强的用户关联测试"""
        try:
            # 查找用户关联相关入口
            all_elements = page.get_elements("view, text, button")
            connection_elements = []
            
            for element in all_elements:
                try:
                    text = element.inner_text
                    if text and ("用户关联" in text or "关联用户" in text or "家庭成员" in text):
                        connection_elements.append(element)
                except:
                    continue
            
            if connection_elements:
                self.log_result("用户关联入口", True, f"找到{len(connection_elements)}个关联入口", critical=True)
                
                # 点击进入用户关联
                connection_elements[0].click()
                time.sleep(3)
                
                current_page = self.app.get_current_page()
                if "connection" in current_page.path or "user_connection" in current_page.path:
                    self.log_result("用户关联页面", True, f"成功进入: {current_page.path}", critical=True)
                    
                    # 测试关联页面功能
                    self._test_connection_page_enhanced(current_page)
                    
                    # 返回个人中心
                    self.app.navigate_back()
                    time.sleep(1)
                else:
                    self.log_result("用户关联页面", False, f"未跳转到关联页面: {current_page.path}", critical=True)
            else:
                self.log_result("用户关联入口", False, "未找到用户关联入口", critical=True)
                
        except Exception as e:
            self.log_result("用户关联增强测试", False, f"测试异常: {e}", critical=True)
    
    def _test_connection_page_enhanced(self, page):
        """增强的关联页面测试"""
        try:
            # 检查搜索功能
            search_elements = page.get_elements("input")
            if search_elements:
                self.log_result("用户搜索输入框", True, f"找到{len(search_elements)}个搜索框", critical=True)
                
                # 尝试搜索
                search_elements[0].input("测试用户")
                time.sleep(2)
                self.log_result("用户搜索操作", True, "搜索操作完成", critical=True)
            else:
                self.log_result("用户搜索输入框", False, "未找到搜索框", critical=True)
            
            # 检查用户列表
            user_elements = page.get_elements(".user-item, .user-card, .connection-item")
            self.log_result("可关联用户列表", True, f"找到{len(user_elements)}个用户项", critical=True)
            
            if user_elements:
                # 测试用户交互
                first_user = user_elements[0]
                
                # 查找关联按钮
                action_buttons = first_user.get_elements("button")
                if action_buttons:
                    self.log_result("用户操作按钮", True, f"找到{len(action_buttons)}个操作按钮", critical=True)
                    
                    # 尝试点击关联
                    action_buttons[0].click()
                    time.sleep(2)
                    self.log_result("用户关联操作", True, "关联操作完成", critical=True)
                else:
                    self.log_result("用户操作按钮", False, "未找到操作按钮", critical=True)
            
            # 检查关联历史或已关联用户
            tab_elements = page.get_elements(".tab, .van-tab")
            if tab_elements:
                self.log_result("关联页面标签", True, f"找到{len(tab_elements)}个标签页", critical=True)
                
                # 尝试切换标签
                if len(tab_elements) > 1:
                    tab_elements[1].click()
                    time.sleep(2)
                    self.log_result("关联标签切换", True, "标签切换成功", critical=True)
            
        except Exception as e:
            self.log_result("关联页面增强测试", False, f"测试异常: {e}", critical=True)


def run_enhanced_test():
    """运行增强测试"""
    print("🚀 开始运行楠楠家厨小程序增强功能测试")
    print("=" * 70)
    print("📱 小程序AppID: wx82283b353918af82")
    print("🔌 微信开发者工具端口: 25209")
    print("🧪 测试重点: 购物车、菜品管理、用户关联深度测试")
    print("=" * 70)
    
    start_time = datetime.now()
    
    # 运行测试
    suite = unittest.TestLoader().loadTestsFromTestCase(EnhancedFunctionalTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 获取详细测试结果
    detailed_results = EnhancedFunctionalTest.test_results
    
    # 统计结果
    total_functional = len(detailed_results)
    passed_functional = sum(1 for r in detailed_results if r['success'])
    failed_functional = total_functional - passed_functional
    critical_failed = len([r for r in detailed_results if not r['success'] and r.get('critical', False)])
    
    # 生成报告
    enhanced_report = {
        "test_time": start_time.isoformat(),
        "duration_seconds": duration,
        "framework_tests": {
            "total": result.testsRun,
            "passed": result.testsRun - len(result.failures) - len(result.errors),
            "failed": len(result.failures),
            "errors": len(result.errors)
        },
        "functional_tests": {
            "total": total_functional,
            "passed": passed_functional,
            "failed": failed_functional,
            "critical_failed": critical_failed
        },
        "overall_success": result.wasSuccessful() and critical_failed <= total_functional * 0.2,
        "detailed_results": detailed_results,
        "test_focus": {
            "cart_functionality": "购物车功能深度测试",
            "dish_management": "菜品管理完整测试",
            "user_connection": "用户关联增强测试",
            "order_flow": "订餐流程完整验证"
        }
    }
    
    # 保存报告
    with open("enhanced_functional_test_report.json", 'w', encoding='utf-8') as f:
        json.dump(enhanced_report, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print("\n" + "=" * 70)
    print("🎯 增强功能测试总结")
    print("=" * 70)
    print(f"⏱️ 测试耗时: {duration:.2f} 秒")
    print(f"📊 框架测试: {enhanced_report['framework_tests']['passed']}/{enhanced_report['framework_tests']['total']} 通过")
    print(f"🔧 功能测试: {passed_functional}/{total_functional} 通过")
    print(f"🔥 关键功能失败: {critical_failed} 个")
    
    if total_functional > 0:
        success_rate = passed_functional / total_functional * 100
        print(f"🎯 功能成功率: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 测试结果优秀！")
        elif success_rate >= 60:
            print("👍 测试结果良好！")
        else:
            print("⚠️ 需要关注失败的功能")
    
    # 分类统计
    categories = {}
    for result in detailed_results:
        category = result['test_name'].split('-')[0] if '-' in result['test_name'] else result['test_name'].split('页面')[0]
        if category not in categories:
            categories[category] = {'passed': 0, 'total': 0}
        categories[category]['total'] += 1
        if result['success']:
            categories[category]['passed'] += 1
    
    print(f"\n📋 功能分类结果:")
    for category, stats in categories.items():
        if stats['total'] > 0:
            rate = stats['passed'] / stats['total'] * 100
            status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
            print(f"  {status} {category}: {stats['passed']}/{stats['total']} ({rate:.0f}%)")
    
    print(f"\n📊 详细报告: enhanced_functional_test_report.json")
    print("=" * 70)
    
    return enhanced_report['overall_success']


if __name__ == "__main__":
    success = run_enhanced_test()
    
    if success:
        print("🎉 增强功能测试成功！")
    else:
        print("⚠️ 部分功能需要优化，请查看详细报告")
