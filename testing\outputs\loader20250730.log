[I 2025-07-30 11:13:56,476 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 11:13:56,478 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 11:13:56,478 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 11:13:56,479 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan\\testing', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 11:13:56,493 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 11:13:56,494 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 11:13:58,732 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 11:13:58,733 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 11:13:58,734 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 11:13:59,074 minium.Conn9904 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 11:13:59,074 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"760c2ed4-1e5f-44dd-89e6-50bb53876b86","method":"Tool.getInfo","params":{}}
[D 2025-07-30 11:13:59,077 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"760c2ed4-1e5f-44dd-89e6-50bb53876b86","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 11:13:59,077 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"9e8cc2fb-357e-4c19-81dc-550a8b10d226","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 11:13:59,085 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"9e8cc2fb-357e-4c19-81dc-550a8b10d226","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 11:13:59,085 minium.App4608 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 11:13:59,086 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"1e94a6d4-e950-4cf1-b80d-f657f5e2667a","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 11:13:59,088 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"1e94a6d4-e950-4cf1-b80d-f657f5e2667a","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 11:13:59,089 minium.Conn9904 connection#287 remove] remove key which is not in observers
[D 2025-07-30 11:13:59,089 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"71121356-fcae-4371-8b7e-7785548caf32","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 11:13:59,091 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"71121356-fcae-4371-8b7e-7785548caf32","result":{}}
[D 2025-07-30 11:13:59,092 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"e158b93e-68c3-4ca1-b5c0-1dc77ecebc28","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 11:13:59,094 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"e158b93e-68c3-4ca1-b5c0-1dc77ecebc28","result":{}}
[D 2025-07-30 11:13:59,094 minium.App4608 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 11:13:59,095 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"f8923251-b9a8-45d4-9441-9a948d74a99e","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 11:13:59,097 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"f8923251-b9a8-45d4-9441-9a948d74a99e","result":{}}
[D 2025-07-30 11:13:59,097 minium.App4608 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 11:13:59,099 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"3d28f067-35c9-4468-bd95-fc18eb2c348f","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 11:13:59,102 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"3d28f067-35c9-4468-bd95-fc18eb2c348f","result":{}}
[D 2025-07-30 11:13:59,103 minium.App4608 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 11:13:59,104 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"e876f9a9-b091-4de5-923c-f8b8287d8fc6","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 11:13:59,106 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"e876f9a9-b091-4de5-923c-f8b8287d8fc6","result":{"result":false}}
[D 2025-07-30 11:13:59,106 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"bd395c64-6c54-4684-9c58-e82167e1d2a9","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 11:13:59,108 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"bd395c64-6c54-4684-9c58-e82167e1d2a9","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 11:13:59,109 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"6d0340c4-f911-4262-8309-bf1c7b1d9d43","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 11:13:59,112 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"6d0340c4-f911-4262-8309-bf1c7b1d9d43","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 11:13:59,112 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"b1fa0195-e84f-43ea-a88c-03b668f4ccfa","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 11:13:59,114 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"b1fa0195-e84f-43ea-a88c-03b668f4ccfa","result":{}}
[D 2025-07-30 11:13:59,114 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"6a5211af-0dc7-4684-ba5e-759823d921e9","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 11:13:59,118 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"6a5211af-0dc7-4684-ba5e-759823d921e9","result":{}}
[D 2025-07-30 11:13:59,119 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"5030ec9d-1432-4a27-b84e-df4be3279e59","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 11:13:59,121 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"5030ec9d-1432-4a27-b84e-df4be3279e59","result":{}}
[D 2025-07-30 11:13:59,121 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"57dd3d5a-c43b-4be7-8b81-7d8cb4f57117","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 11:13:59,124 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"57dd3d5a-c43b-4be7-8b81-7d8cb4f57117","result":{}}
[D 2025-07-30 11:13:59,124 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"5ef4bd27-5147-4ced-9b99-d6d483505d8b","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 11:13:59,125 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"5ef4bd27-5147-4ced-9b99-d6d483505d8b","result":{}}
[D 2025-07-30 11:13:59,126 minium.App4608 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 11:13:59,126 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"7199b178-db44-4a22-8450-8a6374a8e19b","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 11:13:59,129 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"7199b178-db44-4a22-8450-8a6374a8e19b","result":{"result":true}}
[D 2025-07-30 11:13:59,129 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"d98258e8-45b7-44e1-b54a-bfa509d534bd","method":"App.addBinding","params":{"name":"showModal_before_1753845239129"}}
[D 2025-07-30 11:13:59,132 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"d98258e8-45b7-44e1-b54a-bfa509d534bd","result":{}}
[D 2025-07-30 11:13:59,133 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"d813e3fb-4224-40e3-8392-95a8705be430","method":"App.addBinding","params":{"name":"showModal_callback_1753845239129"}}
[D 2025-07-30 11:13:59,136 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"d813e3fb-4224-40e3-8392-95a8705be430","result":{}}
[D 2025-07-30 11:13:59,136 minium.App4608 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 11:13:59,137 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"03e28f11-cc96-425d-886c-3ecdc75f12cb","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753845239129,after:undefined,callback:showModal_callback_1753845239129},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753845239129,true]}}
[D 2025-07-30 11:13:59,139 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"03e28f11-cc96-425d-886c-3ecdc75f12cb","result":{}}
[D 2025-07-30 11:13:59,139 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"bd78fab2-8bff-4200-b6a2-701bfd7368e6","method":"App.addBinding","params":{"name":"showToast_before_1753845239139"}}
[D 2025-07-30 11:13:59,141 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"bd78fab2-8bff-4200-b6a2-701bfd7368e6","result":{}}
[D 2025-07-30 11:13:59,142 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"69e9a340-7ecc-4ac6-8117-37c6eff4a65c","method":"App.addBinding","params":{"name":"showToast_callback_1753845239139"}}
[D 2025-07-30 11:13:59,143 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"69e9a340-7ecc-4ac6-8117-37c6eff4a65c","result":{}}
[D 2025-07-30 11:13:59,144 minium.App4608 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 11:13:59,144 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"e8553e86-8244-4380-b1f5-cf6457652520","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753845239139,after:undefined,callback:showToast_callback_1753845239139},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753845239139,true]}}
[D 2025-07-30 11:13:59,146 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"e8553e86-8244-4380-b1f5-cf6457652520","result":{}}
[I 2025-07-30 11:13:59,147 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x00000297635EA660>, whether should relaunch: True
[D 2025-07-30 11:13:59,147 minium.Conn9904 connection#287 remove] remove key which is not in observers
[D 2025-07-30 11:13:59,149 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"64a8d705-a973-4ca7-8352-e641f56a229c","method":"Tool.getInfo","params":{}}
[D 2025-07-30 11:13:59,152 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"64a8d705-a973-4ca7-8352-e641f56a229c","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 11:13:59,152 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 11:13:59,153 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"d7f21661-4994-4ebc-8e5a-30c63197ef05","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 11:13:59,154 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"d7f21661-4994-4ebc-8e5a-30c63197ef05","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 11:13:59,154 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 11:13:59,154 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 11:13:59,155 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"571add26-4467-4a2a-9f7b-e9d0bfb49e92","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 11:13:59,157 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"571add26-4467-4a2a-9f7b-e9d0bfb49e92","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 11:13:59,157 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"b1408c39-226a-4254-8c28-3ec3d9eac796","method":"App.enableLog","params":{}}
[D 2025-07-30 11:13:59,159 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"b1408c39-226a-4254-8c28-3ec3d9eac796","result":{}}
[D 2025-07-30 11:13:59,160 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"5b5bfda2-eabd-40ef-849c-07eb2f9b6974","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 11:13:59,162 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"5b5bfda2-eabd-40ef-849c-07eb2f9b6974","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 11:13:59,162 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"081d799d-4668-4a72-b338-d11c046940c0","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 11:13:59,168 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"081d799d-4668-4a72-b338-d11c046940c0","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 11:13:59,168 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 11:13:59,171 minium minitest#432 _miniSetUp] =========Current case: test_connection=========
[I 2025-07-30 11:13:59,172 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.connection_test, case info: ConnectionTest.test_connection
[I 2025-07-30 11:13:59,172 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:13:59,172 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:13:59,172 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:13:59,172 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:13:59,173 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:13:59,174 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:13:59,174 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"78e9095e-cfca-4ad9-b636-1e0ae73be049","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,260 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"78e9095e-cfca-4ad9-b636-1e0ae73be049","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:13:59,262 minium minitest#487 _miniSetUp] =========case: test_connection start=========
[I 2025-07-30 11:13:59,263 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 11:13:59,433 minium minitest#897 capture] capture assertIsNotNone-success.png
[D 2025-07-30 11:13:59,433 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"6cad4b15-5264-4cfe-bdae-69756b369c5f","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,499 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"6cad4b15-5264-4cfe-bdae-69756b369c5f","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[D 2025-07-30 11:13:59,501 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"7ad90619-85e9-4040-b0d0-720c0088f7b1","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:13:59,502 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"7ad90619-85e9-4040-b0d0-720c0088f7b1","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:13:59,503 minium.App4608 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:13:59,503 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"bb458d22-6829-4e11-9fce-5a6e3d7da7ef","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:13:59,505 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"bb458d22-6829-4e11-9fce-5a6e3d7da7ef","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:13:59,507 minium minitest#897 capture] capture assertIsNotNone-success_111359507868.png
[D 2025-07-30 11:13:59,508 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"24b89c3c-aea5-4c00-abf2-91205edf6449","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,583 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"24b89c3c-aea5-4c00-abf2-91205edf6449","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:13:59,585 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:13:59,585 minium minitest#799 _miniTearDown] =========Current case Down: test_connection=========
[I 2025-07-30 11:13:59,585 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:13:59,586 minium.Conn9904 connection#427 _safely_send] SEND > [*************]{"id":"0f21ad2e-422f-4821-a52a-e73de84c2a4b","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,667 minium.Conn9904 connection#660 __on_message] RECV < [*************]{"id":"0f21ad2e-422f-4821-a52a-e73de84c2a4b","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:13:59,669 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:13:59,669 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 11:14:11,999 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 11:14:12,001 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 11:14:12,001 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 11:14:12,001 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan\\testing', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 11:14:12,002 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 11:14:12,003 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 11:14:13,256 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 11:14:13,256 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 11:14:13,257 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 11:14:13,457 minium.Conn1504 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 11:14:13,457 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"427fa14b-a710-4e9c-ac8d-5ad514999db3","method":"Tool.getInfo","params":{}}
[D 2025-07-30 11:14:13,459 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"427fa14b-a710-4e9c-ac8d-5ad514999db3","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 11:14:13,459 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c7ac2815-fffb-402a-bf4d-4353f7d1216b","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 11:14:13,466 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c7ac2815-fffb-402a-bf4d-4353f7d1216b","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 11:14:13,467 minium.App6208 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 11:14:13,468 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b29c048f-4990-4f04-bee9-d8e2a98d0ef8","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 11:14:13,470 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b29c048f-4990-4f04-bee9-d8e2a98d0ef8","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 11:14:13,471 minium.Conn1504 connection#287 remove] remove key which is not in observers
[D 2025-07-30 11:14:13,471 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"3e1f83e4-69cd-4298-a789-098cc540f4f1","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 11:14:13,472 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"3e1f83e4-69cd-4298-a789-098cc540f4f1","result":{}}
[D 2025-07-30 11:14:13,473 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"5277b92e-b838-4a13-b567-4b273b41004a","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 11:14:13,475 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"5277b92e-b838-4a13-b567-4b273b41004a","result":{}}
[D 2025-07-30 11:14:13,475 minium.App6208 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 11:14:13,476 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ed759d94-40fc-4b70-bc67-edc869ea9df3","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 11:14:13,478 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ed759d94-40fc-4b70-bc67-edc869ea9df3","result":{}}
[D 2025-07-30 11:14:13,478 minium.App6208 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 11:14:13,479 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a379f4fc-2eb9-4205-812f-e4b4eda50f27","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 11:14:13,483 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a379f4fc-2eb9-4205-812f-e4b4eda50f27","result":{}}
[D 2025-07-30 11:14:13,484 minium.App6208 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 11:14:13,485 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c52a1d3d-326b-4bd4-bffe-1c96744997a2","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 11:14:13,487 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c52a1d3d-326b-4bd4-bffe-1c96744997a2","result":{"result":false}}
[D 2025-07-30 11:14:13,487 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0bb3bec9-bcfe-46cd-8b78-544d761d8d23","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 11:14:13,489 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0bb3bec9-bcfe-46cd-8b78-544d761d8d23","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 11:14:13,490 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"3bd0a0f9-b6dc-46d7-b49b-d363986177dd","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 11:14:13,492 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"3bd0a0f9-b6dc-46d7-b49b-d363986177dd","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 11:14:13,492 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"333dc1a1-b6b5-4941-8fec-142b8346933c","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 11:14:13,493 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"333dc1a1-b6b5-4941-8fec-142b8346933c","result":{}}
[D 2025-07-30 11:14:13,494 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"5ea7815e-b5f8-4272-95a9-ec37d2aa7058","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 11:14:13,495 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"5ea7815e-b5f8-4272-95a9-ec37d2aa7058","result":{}}
[D 2025-07-30 11:14:13,495 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f3afd351-b2a1-45c6-8d09-76a6335e691a","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 11:14:13,500 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f3afd351-b2a1-45c6-8d09-76a6335e691a","result":{}}
[D 2025-07-30 11:14:13,502 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"276b5a42-2a55-4026-9332-403068a48da5","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 11:14:13,504 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"276b5a42-2a55-4026-9332-403068a48da5","result":{}}
[D 2025-07-30 11:14:13,505 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"344290f0-183f-42e9-a133-1776438055d5","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 11:14:13,506 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"344290f0-183f-42e9-a133-1776438055d5","result":{}}
[D 2025-07-30 11:14:13,506 minium.App6208 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 11:14:13,507 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7167f93e-871e-49c7-8487-94e6d67a5ba4","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 11:14:13,510 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7167f93e-871e-49c7-8487-94e6d67a5ba4","result":{"result":true}}
[D 2025-07-30 11:14:13,510 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c8c239b8-c4a2-4ba8-ae49-fab99353d447","method":"App.addBinding","params":{"name":"showModal_before_1753845253510"}}
[D 2025-07-30 11:14:13,512 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c8c239b8-c4a2-4ba8-ae49-fab99353d447","result":{}}
[D 2025-07-30 11:14:13,512 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a73de0c6-fab2-4ac1-b3bd-3e72cce71cc8","method":"App.addBinding","params":{"name":"showModal_callback_1753845253510"}}
[D 2025-07-30 11:14:13,513 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a73de0c6-fab2-4ac1-b3bd-3e72cce71cc8","result":{}}
[D 2025-07-30 11:14:13,514 minium.App6208 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 11:14:13,516 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1cf854fd-f4c2-4fe0-98bd-4447786bf194","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753845253510,after:undefined,callback:showModal_callback_1753845253510},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753845253510,true]}}
[D 2025-07-30 11:14:13,520 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1cf854fd-f4c2-4fe0-98bd-4447786bf194","result":{}}
[D 2025-07-30 11:14:13,521 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a6762c4e-94a8-40ee-ac8b-c3acfcdc0b34","method":"App.addBinding","params":{"name":"showToast_before_1753845253521"}}
[D 2025-07-30 11:14:13,523 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a6762c4e-94a8-40ee-ac8b-c3acfcdc0b34","result":{}}
[D 2025-07-30 11:14:13,524 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4d30d3f2-83e5-4b4e-9f8e-e0dbede76752","method":"App.addBinding","params":{"name":"showToast_callback_1753845253521"}}
[D 2025-07-30 11:14:13,525 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4d30d3f2-83e5-4b4e-9f8e-e0dbede76752","result":{}}
[D 2025-07-30 11:14:13,527 minium.App6208 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 11:14:13,527 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"482fe1df-7477-4ced-859d-eeb3448b0c9c","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753845253521,after:undefined,callback:showToast_callback_1753845253521},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753845253521,true]}}
[D 2025-07-30 11:14:13,529 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"482fe1df-7477-4ced-859d-eeb3448b0c9c","result":{}}
[I 2025-07-30 11:14:13,529 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x0000028213F32660>, whether should relaunch: True
[D 2025-07-30 11:14:13,530 minium.Conn1504 connection#287 remove] remove key which is not in observers
[D 2025-07-30 11:14:13,530 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"27d8319b-5b44-4e5b-b139-5b75e04bc900","method":"Tool.getInfo","params":{}}
[D 2025-07-30 11:14:13,534 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"27d8319b-5b44-4e5b-b139-5b75e04bc900","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 11:14:13,535 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 11:14:13,536 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"049f9aef-6b05-4a2b-9d18-719ab4bc5ed7","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 11:14:13,537 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"049f9aef-6b05-4a2b-9d18-719ab4bc5ed7","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 11:14:13,538 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 11:14:13,538 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 11:14:13,538 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b9deaa95-7b04-43d3-b234-ad4d9ecd8e1a","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 11:14:13,540 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b9deaa95-7b04-43d3-b234-ad4d9ecd8e1a","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 11:14:13,541 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ee65860f-6669-46f5-be76-d11ec5d52208","method":"App.enableLog","params":{}}
[D 2025-07-30 11:14:13,542 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ee65860f-6669-46f5-be76-d11ec5d52208","result":{}}
[D 2025-07-30 11:14:13,543 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1c204256-77c9-42ae-ab91-7df6f70e89be","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 11:14:13,545 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1c204256-77c9-42ae-ab91-7df6f70e89be","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 11:14:13,545 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e6078b55-05ed-460f-b49e-177e2fcfda28","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 11:14:13,554 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e6078b55-05ed-460f-b49e-177e2fcfda28","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 11:14:13,555 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 11:14:13,558 minium minitest#432 _miniSetUp] =========Current case: test_01_enhanced_order_flow_with_cart=========
[I 2025-07-30 11:14:13,559 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.enhanced_functional_test, case info: EnhancedFunctionalTest.test_01_enhanced_order_flow_with_cart
[I 2025-07-30 11:14:13,559 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:14:13,559 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:14:13,560 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:14:13,560 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:14:13,560 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:14:13,576 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:14:13,576 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2ad9b461-5283-4bfe-bbef-54a252f1d96e","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:14:13,683 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2ad9b461-5283-4bfe-bbef-54a252f1d96e","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:14:13,685 minium minitest#487 _miniSetUp] =========case: test_01_enhanced_order_flow_with_cart start=========
[I 2025-07-30 11:14:14,686 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:14:14,687 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"bfff6b7a-9c6f-4dfb-93fe-d68081159893","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:14,696 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"bfff6b7a-9c6f-4dfb-93fe-d68081159893","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:14:14,697 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:14,700 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d9737771-c774-4312-87f3-a346c14afb4a","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:14,706 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d9737771-c774-4312-87f3-a346c14afb4a","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:14:14,707 minium.Conn1504 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"09addfee-0e06-4074-8281-cfead5504114","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:14:14,713 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:14:14,719 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"09addfee-0e06-4074-8281-cfead5504114","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:14:14,719 minium.Conn1504 connection#704 _handle_async_msg] received async msg: 09addfee-0e06-4074-8281-cfead5504114
[D 2025-07-30 11:14:29,726 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a2890f9a-1399-4670-ba7c-0359863631d0","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:29,728 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a2890f9a-1399-4670-ba7c-0359863631d0","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:14:29,729 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:29,729 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"daa4a233-4ea4-4b0e-84c7-33a1aece60cc","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:29,731 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"daa4a233-4ea4-4b0e-84c7-33a1aece60cc","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:14:32,732 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"84d52872-c899-40d7-bb23-d535bf0b1be0","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:32,734 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"84d52872-c899-40d7-bb23-d535bf0b1be0","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:14:32,734 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:32,735 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d9760f66-4812-4d59-adc6-3d3887d637bd","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:32,736 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d9760f66-4812-4d59-adc6-3d3887d637bd","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:14:32,918 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 11:14:32,919 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"64103bac-3fe3-4f5b-9e7c-72f76151c9e6","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:14:32,982 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"64103bac-3fe3-4f5b-9e7c-72f76151c9e6","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:14:35,984 minium page#716 _get_elements_by_css] try to get elements: view, .dish-card, .food-item, .menu-item
[D 2025-07-30 11:14:35,984 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"fd58bb14-bfda-45cb-987a-a8d0f7d20e76","method":"Page.getElements","params":{"selector":"view, .dish-card, .food-item, .menu-item","pageId":5}}
[D 2025-07-30 11:14:35,990 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"fd58bb14-bfda-45cb-987a-a8d0f7d20e76","result":{"elements":[{"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","tagName":"view"},{"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","tagName":"view"},{"elementId":"6aa13ac2-fe8f-4720-99ca-0c7bb789a391","tagName":"view"},{"elementId":"d05a9803-35be-48ff-97ac-0a8ad8929e1b","tagName":"view"},{"elementId":"6a3546b1-e824-4023-9018-a3cf2b7e0a5b","tagName":"view"},{"elementId":"d3ea0e51-f683-4125-9fc6-dfcc1940ca41","tagName":"view"},{"elementId":"a82df277-85
[I 2025-07-30 11:14:35,991 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F33E00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F8A210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F8A710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F23230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F236F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FE4A70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F03F00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F87050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F87150>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FECC80>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FECAA0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FE97F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FE98D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F62D00>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F25F10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F25FD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F6E570>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213F6D230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD630>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD4F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD450>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD770>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD8B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD9F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDA90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDB30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDBD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDC70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDD10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDDB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDE50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDEF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDF90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE030>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE0D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE170>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE2B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE350>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE3F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE490>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE530>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE5D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE670>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE7B0>]
[D 2025-07-30 11:14:35,993 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f18b9c05-ea42-4968-b9cb-7a6007835906","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:35,996 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f18b9c05-ea42-4968-b9cb-7a6007835906","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:35,997 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"47848a14-0005-4891-989d-ccc220a1820f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","pageId":5}}
[D 2025-07-30 11:14:36,002 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"47848a14-0005-4891-989d-ccc220a1820f","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,002 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"327f3fea-9856-4771-ade3-8d8e8dbef7fd","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6aa13ac2-fe8f-4720-99ca-0c7bb789a391","pageId":5}}
[D 2025-07-30 11:14:36,005 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"327f3fea-9856-4771-ade3-8d8e8dbef7fd","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃"]}}
[D 2025-07-30 11:14:36,006 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"de6c447a-2331-4362-a738-4a3a715a1125","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d05a9803-35be-48ff-97ac-0a8ad8929e1b","pageId":5}}
[D 2025-07-30 11:14:36,009 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"de6c447a-2331-4362-a738-4a3a715a1125","result":{"properties":["全部"]}}
[D 2025-07-30 11:14:36,009 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"cf920f87-dd08-44e5-b1c2-810c105b8151","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6a3546b1-e824-4023-9018-a3cf2b7e0a5b","pageId":5}}
[D 2025-07-30 11:14:36,012 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"cf920f87-dd08-44e5-b1c2-810c105b8151","result":{"properties":["热菜"]}}
[D 2025-07-30 11:14:36,012 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1896e506-4f0f-40de-8285-d78238ec3289","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d3ea0e51-f683-4125-9fc6-dfcc1940ca41","pageId":5}}
[D 2025-07-30 11:14:36,017 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1896e506-4f0f-40de-8285-d78238ec3289","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:36,018 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b9dd0486-231f-41c7-b294-16e7554775d1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a82df277-8584-4174-85c7-8523865fdcdb","pageId":5}}
[D 2025-07-30 11:14:36,021 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b9dd0486-231f-41c7-b294-16e7554775d1","result":{"properties":["江湖菜"]}}
[D 2025-07-30 11:14:36,021 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6299905a-9be8-4f02-a5d4-85474563aa9a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"39d71dd6-6395-4722-8caa-46ce57f8c96e","pageId":5}}
[D 2025-07-30 11:14:36,024 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6299905a-9be8-4f02-a5d4-85474563aa9a","result":{"properties":["小面"]}}
[D 2025-07-30 11:14:36,024 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1cb8eadb-db3d-4fc5-8f65-49d8ed7b1ec1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71b4a56a-b8d3-4480-befe-b14dc81c94c6","pageId":5}}
[D 2025-07-30 11:14:36,028 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1cb8eadb-db3d-4fc5-8f65-49d8ed7b1ec1","result":{"properties":["川菜"]}}
[D 2025-07-30 11:14:36,029 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"10b30815-fdf6-4222-82ff-9819290782c1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"87a98043-ec5f-411b-9bac-d9e0d29655e5","pageId":5}}
[D 2025-07-30 11:14:36,031 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"10b30815-fdf6-4222-82ff-9819290782c1","result":{"properties":["湘菜"]}}
[D 2025-07-30 11:14:36,033 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4fea3a74-2469-4ff6-a086-72304602d0a8","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2567dc73-a053-45ee-853d-dc1e92276cc0","pageId":5}}
[D 2025-07-30 11:14:36,038 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4fea3a74-2469-4ff6-a086-72304602d0a8","result":{"properties":["粤菜"]}}
[D 2025-07-30 11:14:36,038 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ffce9d39-db0d-4e35-9386-bb94a31be00b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c16a7e7-9098-451f-bf3b-a84e951b5d45","pageId":5}}
[D 2025-07-30 11:14:36,042 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ffce9d39-db0d-4e35-9386-bb94a31be00b","result":{"properties":["凉菜"]}}
[D 2025-07-30 11:14:36,042 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"51bd0b6f-6b5b-42fe-b81f-db078f8b1681","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f37b4a-c4d4-45f8-b571-406a5208ffaf","pageId":5}}
[D 2025-07-30 11:14:36,044 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"51bd0b6f-6b5b-42fe-b81f-db078f8b1681","result":{"properties":["汤品"]}}
[D 2025-07-30 11:14:36,045 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"278f858c-0d65-4c20-9a65-0a3c07bbf20e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"72569af5-3c3f-4747-8d7a-c92c2d98d20d","pageId":5}}
[D 2025-07-30 11:14:36,047 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"278f858c-0d65-4c20-9a65-0a3c07bbf20e","result":{"properties":["主食"]}}
[D 2025-07-30 11:14:36,049 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"de2fa12d-0d64-4194-ac9f-d299dae58ef7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"8bf3ccbb-9d5c-480a-b648-301e0598adda","pageId":5}}
[D 2025-07-30 11:14:36,053 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"de2fa12d-0d64-4194-ac9f-d299dae58ef7","result":{"properties":["素菜"]}}
[D 2025-07-30 11:14:36,054 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7a5b8c03-951a-40a4-9c34-d53f87498203","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"eb22e17e-8f98-4cec-9aaa-d7198effe874","pageId":5}}
[D 2025-07-30 11:14:36,056 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7a5b8c03-951a-40a4-9c34-d53f87498203","result":{"properties":["甜品"]}}
[D 2025-07-30 11:14:36,056 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"51eaea31-44d9-4ca4-99a3-887934c63577","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"8dc9ea6f-22af-4006-9b27-f056e564a6dc","pageId":5}}
[D 2025-07-30 11:14:36,059 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"51eaea31-44d9-4ca4-99a3-887934c63577","result":{"properties":["饮品"]}}
[D 2025-07-30 11:14:36,059 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a22e9668-bb0a-4563-9967-3a1e7a357242","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"b5092352-9ad4-4ea6-9747-28a1675288d8","pageId":5}}
[D 2025-07-30 11:14:36,062 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a22e9668-bb0a-4563-9967-3a1e7a357242","result":{"properties":["酒水"]}}
[D 2025-07-30 11:14:36,063 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"90f60c02-848c-4e1e-89bc-bdfd03017101","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"91b60564-9e82-437c-bcfa-de73d35afab7","pageId":5}}
[D 2025-07-30 11:14:36,068 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"90f60c02-848c-4e1e-89bc-bdfd03017101","result":{"properties":["小吃"]}}
[D 2025-07-30 11:14:36,068 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"355a4776-cfda-4077-a2ff-3bbbe11c0639","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a3f2b326-1fb4-498d-af6d-5342a8d1e99f","pageId":5}}
[D 2025-07-30 11:14:36,071 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"355a4776-cfda-4077-a2ff-3bbbe11c0639","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,071 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d7fa3123-55aa-4540-be72-b08f978fc2dd","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"3cd450c0-bc2a-4a20-87f1-731c1373518a","pageId":5}}
[D 2025-07-30 11:14:36,075 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d7fa3123-55aa-4540-be72-b08f978fc2dd","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,075 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a6c92969-787b-45a4-93c4-916e1b71557e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 11:14:36,077 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a6c92969-787b-45a4-93c4-916e1b71557e","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车"]}}
[D 2025-07-30 11:14:36,078 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"616c404d-b37d-4784-a45e-1f77e84deda4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"84fe3e23-2b40-4040-bb90-763278519c7f","pageId":5}}
[D 2025-07-30 11:14:36,080 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"616c404d-b37d-4784-a45e-1f77e84deda4","result":{"properties":["小菜\n今天\n火锅\n麻"]}}
[D 2025-07-30 11:14:36,081 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a50f2de7-38aa-41fc-a3d1-c4b388d87343","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"40d1a2b2-2b06-4ada-be84-b1ab1a3c3cf6","pageId":5}}
[D 2025-07-30 11:14:36,086 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a50f2de7-38aa-41fc-a3d1-c4b388d87343","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,087 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"10c03a5b-0424-4760-9acf-9577144ec8ba","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1798522a-0355-4cb9-878a-2662c62699ec","pageId":5}}
[D 2025-07-30 11:14:36,091 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"10c03a5b-0424-4760-9acf-9577144ec8ba","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,091 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a7991ae5-c636-4137-946d-7a156a9df39a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"017cda2e-06ea-4efc-8e5f-3136d6be16b9","pageId":5}}
[D 2025-07-30 11:14:36,094 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a7991ae5-c636-4137-946d-7a156a9df39a","result":{"properties":["小菜\n今天\n火锅\n麻"]}}
[D 2025-07-30 11:14:36,094 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"59fda2d2-9ca2-4e14-858e-6cb2e567c9b9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f50a54-5068-45bb-8512-2ef08a88f1a9","pageId":5}}
[D 2025-07-30 11:14:36,096 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"59fda2d2-9ca2-4e14-858e-6cb2e567c9b9","result":{"properties":["小菜\n今天"]}}
[D 2025-07-30 11:14:36,097 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"8e04d27b-9e32-463b-9d1b-21297a496641","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"afc7817c-6592-43dc-8d27-e0463ab07866","pageId":5}}
[D 2025-07-30 11:14:36,102 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"8e04d27b-9e32-463b-9d1b-21297a496641","result":{"properties":["火锅\n麻"]}}
[D 2025-07-30 11:14:36,102 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ff0d3442-5395-4962-8a15-8de1cb561b6c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"068d01f0-3325-4651-b209-322aca03477c","pageId":5}}
[D 2025-07-30 11:14:36,105 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ff0d3442-5395-4962-8a15-8de1cb561b6c","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:36,106 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4909d7a0-6599-4009-b2fd-1daadcc99578","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"56e0c9f1-bc1a-419d-a53e-dd939b61a6af","pageId":5}}
[D 2025-07-30 11:14:36,108 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4909d7a0-6599-4009-b2fd-1daadcc99578","result":{"properties":["麻"]}}
[D 2025-07-30 11:14:36,109 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"dbc1d507-b6f9-4f3f-80ab-7ec3fd74e919","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f6100758-a6d2-4540-8127-ee8389d19c6f","pageId":5}}
[D 2025-07-30 11:14:36,111 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"dbc1d507-b6f9-4f3f-80ab-7ec3fd74e919","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,112 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"97593806-e68e-41ea-a0c1-255240b0392e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"25f2dfad-4eb0-4dee-bdf6-e83e42d57839","pageId":5}}
[D 2025-07-30 11:14:36,114 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"97593806-e68e-41ea-a0c1-255240b0392e","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,116 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"9fcdb1e2-c00c-48e7-a26f-0dfdc1440a36","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"ded5dbd1-390b-4159-8440-2bc02f9e62d3","pageId":5}}
[D 2025-07-30 11:14:36,120 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"9fcdb1e2-c00c-48e7-a26f-0dfdc1440a36","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,121 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d6b4abc5-346e-43ba-97a3-a770e43db24b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"25284a2d-4a51-4d1c-b42e-4947747b403c","pageId":5}}
[D 2025-07-30 11:14:36,123 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d6b4abc5-346e-43ba-97a3-a770e43db24b","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,124 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"61283c58-7745-432d-977a-b2ba08e90efd","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 11:14:36,126 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"61283c58-7745-432d-977a-b2ba08e90efd","result":{"properties":["红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,126 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"19ab2edc-745b-42f4-8ad1-ff80d898b6e7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"fc1b3615-671c-425d-9001-663cab5fc45a","pageId":5}}
[D 2025-07-30 11:14:36,128 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"19ab2edc-745b-42f4-8ad1-ff80d898b6e7","result":{"properties":["红烧肉\n今天\n火锅\n甜"]}}
[D 2025-07-30 11:14:36,129 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1c245570-3915-484a-808e-3ad2d0f013b0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"df2d5721-82d1-4b22-b36b-ec0c4e9ef215","pageId":5}}
[D 2025-07-30 11:14:36,131 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1c245570-3915-484a-808e-3ad2d0f013b0","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,132 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"afaa9a21-b030-4fe0-a44b-1dd372ab6089","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d3ebc075-dc89-4ffb-9939-38ff6e7185b8","pageId":5}}
[D 2025-07-30 11:14:36,137 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"afaa9a21-b030-4fe0-a44b-1dd372ab6089","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,137 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2b572adf-14c2-430d-a8c9-31299d7657a4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1f47a094-5f31-4bd4-8c08-028083c58508","pageId":5}}
[D 2025-07-30 11:14:36,139 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2b572adf-14c2-430d-a8c9-31299d7657a4","result":{"properties":["红烧肉\n今天\n火锅\n甜"]}}
[D 2025-07-30 11:14:36,139 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ec27bee7-0eed-4c7b-a5e7-05b4f1781810","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"21ec1f89-1bae-4793-b8bd-6681fc47837d","pageId":5}}
[D 2025-07-30 11:14:36,142 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ec27bee7-0eed-4c7b-a5e7-05b4f1781810","result":{"properties":["红烧肉\n今天"]}}
[D 2025-07-30 11:14:36,142 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0b0b021e-26d5-4440-8c67-e569a47129b5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"eda5d04e-3faf-49ec-9a70-335496cf5012","pageId":5}}
[D 2025-07-30 11:14:36,145 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0b0b021e-26d5-4440-8c67-e569a47129b5","result":{"properties":["火锅\n甜"]}}
[D 2025-07-30 11:14:36,145 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"08eba951-3bc2-4f3c-bd93-27867cf6effd","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9fe936f4-d7ff-42c5-b801-b8cd44e89e3a","pageId":5}}
[D 2025-07-30 11:14:36,147 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"08eba951-3bc2-4f3c-bd93-27867cf6effd","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:36,150 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"45571921-c527-41a4-bb07-5931be388ddb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"19f0b00f-d416-49c8-8aba-8ec8a995348b","pageId":5}}
[D 2025-07-30 11:14:36,154 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"45571921-c527-41a4-bb07-5931be388ddb","result":{"properties":["甜"]}}
[D 2025-07-30 11:14:36,154 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f047a1f2-8afd-4e9f-9f98-d22ddf0318eb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2e0ea4b0-4a3a-451d-abd5-875e4ffce728","pageId":5}}
[D 2025-07-30 11:14:36,157 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f047a1f2-8afd-4e9f-9f98-d22ddf0318eb","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,157 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d961763e-b03c-444a-9222-ea79b5d8b6b2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"77d2c7c8-6ef8-48ae-852c-2ac885d34fc2","pageId":5}}
[D 2025-07-30 11:14:36,159 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d961763e-b03c-444a-9222-ea79b5d8b6b2","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,160 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"3c95e88c-46cc-4578-ab8d-f900367ca290","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"04514357-c37d-49e5-8d60-cd866e75a9f2","pageId":5}}
[D 2025-07-30 11:14:36,162 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"3c95e88c-46cc-4578-ab8d-f900367ca290","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,163 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"df4327c0-48c2-4715-98c0-925e6b20d2f1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"69363ee4-6dc4-4b98-8ef1-08b8fe6b1928","pageId":5}}
[D 2025-07-30 11:14:36,167 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"df4327c0-48c2-4715-98c0-925e6b20d2f1","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,168 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c4697adf-411f-49e5-94aa-95f53cdba8d3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"3a291262-f404-41f6-8cbe-fe65b5e40a95","pageId":5}}
[D 2025-07-30 11:14:36,171 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c4697adf-411f-49e5-94aa-95f53cdba8d3","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,171 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"bff2e111-3804-428e-9ecc-482b3ca57142","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"bd244f4f-22da-4ea9-9f19-16d50dbfca1b","pageId":5}}
[D 2025-07-30 11:14:36,174 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"bff2e111-3804-428e-9ecc-482b3ca57142","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,174 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"18b72f49-3dd3-4329-8e30-0f30d462a5a1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,176 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"18b72f49-3dd3-4329-8e30-0f30d462a5a1","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,177 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f91b3f81-b9e8-4dae-8487-6b7c83ebfbd9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,179 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f91b3f81-b9e8-4dae-8487-6b7c83ebfbd9","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[I 2025-07-30 11:14:36,180 minium element#521 _get_elements_by_css] try to get elements: button, view[bindtap]
[D 2025-07-30 11:14:36,181 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"055f97f3-c639-413b-a995-59ca4a8bfd40","method":"Element.getElements","params":{"selector":"button, view[bindtap]","elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,188 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"055f97f3-c639-413b-a995-59ca4a8bfd40","result":{"elements":[]}}
[D 2025-07-30 11:14:36,189 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b4bc8df5-a91d-4dd8-adce-2a4f55a992e1","method":"Element.getProperties","params":{"names":["node_id"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,193 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b4bc8df5-a91d-4dd8-adce-2a4f55a992e1","result":{"properties":[null]}}
[D 2025-07-30 11:14:36,194 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6a387a9f-0769-4432-bda1-c639ba1937ca","method":"Element.getAttributes","params":{"names":["node_id"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,196 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6a387a9f-0769-4432-bda1-c639ba1937ca","result":{"attributes":[null]}}
[W 2025-07-30 11:14:36,197 minium element#526 _get_elements_by_css] Could not found any element 'button, view[bindtap]' you need
[I 2025-07-30 11:14:36,197 minium page#716 _get_elements_by_css] try to get elements: view, button, text
[D 2025-07-30 11:14:36,200 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"04b8ead3-3a1f-4dcb-9c3f-d63aaab9a370","method":"Page.getElements","params":{"selector":"view, button, text","pageId":5}}
[D 2025-07-30 11:14:36,206 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"04b8ead3-3a1f-4dcb-9c3f-d63aaab9a370","result":{"elements":[{"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","tagName":"view"},{"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","tagName":"view"},{"elementId":"6aa13ac2-fe8f-4720-99ca-0c7bb789a391","tagName":"view"},{"elementId":"d05a9803-35be-48ff-97ac-0a8ad8929e1b","tagName":"view"},{"elementId":"9e876aa4-6044-4de7-b7b6-56caf4194741","tagName":"text"},{"elementId":"6a3546b1-e824-4023-9018-a3cf2b7e0a5b","tagName":"view"},{"elementId":"ce3eea75-2a
[I 2025-07-30 11:14:36,207 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD950>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD770>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDF90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD9F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDEF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD450>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE170>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD8B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDA90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDB30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDBD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDC70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDD10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDDB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDE50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE030>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE0D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE2B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE350>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE3F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE490>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE530>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE5D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE670>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEA30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE8F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD590>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE990>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEAD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEB70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEC10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCECB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCED50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEDF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEE90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEF30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEFD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF070>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF110>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF1B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF250>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF2F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF390>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF430>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF4D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF570>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF610>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF6B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF750>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF7F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF890>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF930>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF9D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFA70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFB10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFBB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFC50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFCF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFD90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFE30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFED0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFF70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000282143840F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000282143842D0>]
[D 2025-07-30 11:14:36,208 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4ce37ead-d3e3-4f6f-a644-850e3cd5acdb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,210 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4ce37ead-d3e3-4f6f-a644-850e3cd5acdb","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,211 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"049cb744-4c72-43ef-a37d-94189502751a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","pageId":5}}
[D 2025-07-30 11:14:36,213 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"049cb744-4c72-43ef-a37d-94189502751a","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,213 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7bc3e3ab-8c3f-4e7e-abf8-4cded3f4cf1b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6aa13ac2-fe8f-4720-99ca-0c7bb789a391","pageId":5}}
[D 2025-07-30 11:14:36,220 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7bc3e3ab-8c3f-4e7e-abf8-4cded3f4cf1b","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃"]}}
[D 2025-07-30 11:14:36,220 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0d0e4abf-5643-4e0e-8d53-b8cef703a813","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d05a9803-35be-48ff-97ac-0a8ad8929e1b","pageId":5}}
[D 2025-07-30 11:14:36,223 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0d0e4abf-5643-4e0e-8d53-b8cef703a813","result":{"properties":["全部"]}}
[D 2025-07-30 11:14:36,224 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"01d7a1b7-bee2-4739-83fa-3317d3a496fc","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9e876aa4-6044-4de7-b7b6-56caf4194741","pageId":5}}
[D 2025-07-30 11:14:36,226 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"01d7a1b7-bee2-4739-83fa-3317d3a496fc","result":{"properties":["全部"]}}
[D 2025-07-30 11:14:36,226 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"53f7b052-6c14-4bba-9c8e-227faf4bb547","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6a3546b1-e824-4023-9018-a3cf2b7e0a5b","pageId":5}}
[D 2025-07-30 11:14:36,229 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"53f7b052-6c14-4bba-9c8e-227faf4bb547","result":{"properties":["热菜"]}}
[D 2025-07-30 11:14:36,229 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"8077c8fe-193a-4c63-a53e-f5e911e64397","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"ce3eea75-2a04-4dea-8b86-e4320ba46955","pageId":5}}
[D 2025-07-30 11:14:36,235 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"8077c8fe-193a-4c63-a53e-f5e911e64397","result":{"properties":["热菜"]}}
[D 2025-07-30 11:14:36,236 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"36eb400a-dcf3-4f73-845a-e83ebb12e484","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d3ea0e51-f683-4125-9fc6-dfcc1940ca41","pageId":5}}
[D 2025-07-30 11:14:36,238 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"36eb400a-dcf3-4f73-845a-e83ebb12e484","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:36,238 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"38d8184c-22a9-4637-8bbb-00517b7c8514","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2e66a2f8-7df0-450d-b916-006ad6d4a551","pageId":5}}
[D 2025-07-30 11:14:36,240 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"38d8184c-22a9-4637-8bbb-00517b7c8514","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:36,241 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b9e0cc3c-84f8-4455-bbc6-7d477906f3a1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a82df277-8584-4174-85c7-8523865fdcdb","pageId":5}}
[D 2025-07-30 11:14:36,243 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b9e0cc3c-84f8-4455-bbc6-7d477906f3a1","result":{"properties":["江湖菜"]}}
[D 2025-07-30 11:14:36,243 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2addd012-9628-43b2-9495-d9592813caf6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"99387b0e-b628-42ee-8f2f-0edbbef12daf","pageId":5}}
[D 2025-07-30 11:14:36,245 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2addd012-9628-43b2-9495-d9592813caf6","result":{"properties":["江湖菜"]}}
[D 2025-07-30 11:14:36,246 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"073f3097-a585-4fc1-a1aa-bfda0350a359","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"39d71dd6-6395-4722-8caa-46ce57f8c96e","pageId":5}}
[D 2025-07-30 11:14:36,250 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"073f3097-a585-4fc1-a1aa-bfda0350a359","result":{"properties":["小面"]}}
[D 2025-07-30 11:14:36,250 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0c1de0f5-936e-47e0-b756-0a96fa1944f9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"7595326b-67fd-4146-a9be-69d55cdc31f9","pageId":5}}
[D 2025-07-30 11:14:36,253 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0c1de0f5-936e-47e0-b756-0a96fa1944f9","result":{"properties":["小面"]}}
[D 2025-07-30 11:14:36,254 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b530d301-edd0-423a-8594-456df9f38ade","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71b4a56a-b8d3-4480-befe-b14dc81c94c6","pageId":5}}
[D 2025-07-30 11:14:36,256 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b530d301-edd0-423a-8594-456df9f38ade","result":{"properties":["川菜"]}}
[D 2025-07-30 11:14:36,256 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6a367452-9632-4691-bd97-699a150994dc","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1b2b04bd-c31d-4e62-8810-7ebc790fec1e","pageId":5}}
[D 2025-07-30 11:14:36,258 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6a367452-9632-4691-bd97-699a150994dc","result":{"properties":["川菜"]}}
[D 2025-07-30 11:14:36,258 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2ace694e-0f23-4912-95e6-7bf4075534a2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"87a98043-ec5f-411b-9bac-d9e0d29655e5","pageId":5}}
[D 2025-07-30 11:14:36,261 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2ace694e-0f23-4912-95e6-7bf4075534a2","result":{"properties":["湘菜"]}}
[D 2025-07-30 11:14:36,261 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"65cff7a4-b5e6-498c-ac1d-7e50b309ca66","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"89b1736e-13f0-44d9-8fb5-4aa0a4d4fb82","pageId":5}}
[D 2025-07-30 11:14:36,264 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"65cff7a4-b5e6-498c-ac1d-7e50b309ca66","result":{"properties":["湘菜"]}}
[D 2025-07-30 11:14:36,264 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b4290ddb-ec69-4e74-9d91-21f71f0280e9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2567dc73-a053-45ee-853d-dc1e92276cc0","pageId":5}}
[D 2025-07-30 11:14:36,269 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b4290ddb-ec69-4e74-9d91-21f71f0280e9","result":{"properties":["粤菜"]}}
[D 2025-07-30 11:14:36,269 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ffd23e96-32c4-46e3-a8d0-cf502017b1a8","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5367d370-10c1-417e-90e3-1e186e89a77b","pageId":5}}
[D 2025-07-30 11:14:36,272 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ffd23e96-32c4-46e3-a8d0-cf502017b1a8","result":{"properties":["粤菜"]}}
[D 2025-07-30 11:14:36,273 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c4929c2d-32fa-471d-98da-6a05aa8fa946","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c16a7e7-9098-451f-bf3b-a84e951b5d45","pageId":5}}
[D 2025-07-30 11:14:36,275 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c4929c2d-32fa-471d-98da-6a05aa8fa946","result":{"properties":["凉菜"]}}
[D 2025-07-30 11:14:36,276 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1466920f-ef17-4c05-aa60-af7688c3e5e1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"685f1b82-6fd3-4b48-aeb8-8a0c649a5be6","pageId":5}}
[D 2025-07-30 11:14:36,278 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1466920f-ef17-4c05-aa60-af7688c3e5e1","result":{"properties":["凉菜"]}}
[D 2025-07-30 11:14:36,278 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e8f48048-fc55-4196-b9dc-96c944879008","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f37b4a-c4d4-45f8-b571-406a5208ffaf","pageId":5}}
[D 2025-07-30 11:14:36,280 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e8f48048-fc55-4196-b9dc-96c944879008","result":{"properties":["汤品"]}}
[D 2025-07-30 11:14:36,282 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d6a42f28-4e6b-4929-a836-dff21952c285","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"fee1bd18-ee93-4146-9dfd-61cb52065268","pageId":5}}
[D 2025-07-30 11:14:36,286 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d6a42f28-4e6b-4929-a836-dff21952c285","result":{"properties":["汤品"]}}
[D 2025-07-30 11:14:36,286 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"222376fd-7c71-44f5-ab6f-fd1955d50d0f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"72569af5-3c3f-4747-8d7a-c92c2d98d20d","pageId":5}}
[D 2025-07-30 11:14:36,289 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"222376fd-7c71-44f5-ab6f-fd1955d50d0f","result":{"properties":["主食"]}}
[D 2025-07-30 11:14:36,289 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2b497c71-736c-4f7d-a8b2-e3415f9f25f0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d9c4a079-6422-4dff-8f42-2ffbbb180522","pageId":5}}
[D 2025-07-30 11:14:36,291 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2b497c71-736c-4f7d-a8b2-e3415f9f25f0","result":{"properties":["主食"]}}
[D 2025-07-30 11:14:36,292 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"eda4cf07-9d75-4e27-b118-9d7db249d60b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"8bf3ccbb-9d5c-480a-b648-301e0598adda","pageId":5}}
[D 2025-07-30 11:14:36,294 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"eda4cf07-9d75-4e27-b118-9d7db249d60b","result":{"properties":["素菜"]}}
[D 2025-07-30 11:14:36,295 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c1ac0609-58a4-468f-8c52-ba29905b9291","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"66b04483-aeea-4db3-aeee-c8203fd76077","pageId":5}}
[D 2025-07-30 11:14:36,297 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c1ac0609-58a4-468f-8c52-ba29905b9291","result":{"properties":["素菜"]}}
[D 2025-07-30 11:14:36,297 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"109c4e70-751f-470c-94d3-87c6d1c513f6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"eb22e17e-8f98-4cec-9aaa-d7198effe874","pageId":5}}
[D 2025-07-30 11:14:36,302 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"109c4e70-751f-470c-94d3-87c6d1c513f6","result":{"properties":["甜品"]}}
[D 2025-07-30 11:14:36,303 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"461e202c-7779-4436-98d8-3f13fa62c026","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"92c3c1ce-284b-4b56-a3ce-4d5e67fdafd1","pageId":5}}
[D 2025-07-30 11:14:36,305 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"461e202c-7779-4436-98d8-3f13fa62c026","result":{"properties":["甜品"]}}
[D 2025-07-30 11:14:36,305 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ea576b92-8624-4d3c-a269-a4a55667718c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"8dc9ea6f-22af-4006-9b27-f056e564a6dc","pageId":5}}
[D 2025-07-30 11:14:36,308 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ea576b92-8624-4d3c-a269-a4a55667718c","result":{"properties":["饮品"]}}
[D 2025-07-30 11:14:36,308 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"fd0f755d-821c-4d35-8141-67f324204e58","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9fa23ae6-1039-4913-9245-46168563384e","pageId":5}}
[D 2025-07-30 11:14:36,311 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"fd0f755d-821c-4d35-8141-67f324204e58","result":{"properties":["饮品"]}}
[D 2025-07-30 11:14:36,312 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a360b458-6d3b-4ff5-be88-df973e844754","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"b5092352-9ad4-4ea6-9747-28a1675288d8","pageId":5}}
[D 2025-07-30 11:14:36,314 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a360b458-6d3b-4ff5-be88-df973e844754","result":{"properties":["酒水"]}}
[D 2025-07-30 11:14:36,316 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"244bb7f1-23ed-4b7f-bddb-9157ca792a04","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"4710ff73-e66c-4c5b-8c43-b8c111459c44","pageId":5}}
[D 2025-07-30 11:14:36,320 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"244bb7f1-23ed-4b7f-bddb-9157ca792a04","result":{"properties":["酒水"]}}
[D 2025-07-30 11:14:36,320 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e46ef05d-b1fd-40ed-b86e-e4a3f0d09f23","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"91b60564-9e82-437c-bcfa-de73d35afab7","pageId":5}}
[D 2025-07-30 11:14:36,323 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e46ef05d-b1fd-40ed-b86e-e4a3f0d09f23","result":{"properties":["小吃"]}}
[D 2025-07-30 11:14:36,323 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"95bedad0-7419-4bb0-a150-7ad3b82f9314","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f5b3d78b-7064-4105-9945-2c10f4de4aad","pageId":5}}
[D 2025-07-30 11:14:36,325 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"95bedad0-7419-4bb0-a150-7ad3b82f9314","result":{"properties":["小吃"]}}
[D 2025-07-30 11:14:36,326 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"bb539f97-c9ff-490a-bcb5-0d3970fd4499","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a3f2b326-1fb4-498d-af6d-5342a8d1e99f","pageId":5}}
[D 2025-07-30 11:14:36,328 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"bb539f97-c9ff-490a-bcb5-0d3970fd4499","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,329 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f258ee3b-c62b-452a-8011-96d90b2e1c2f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"3cd450c0-bc2a-4a20-87f1-731c1373518a","pageId":5}}
[D 2025-07-30 11:14:36,331 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f258ee3b-c62b-452a-8011-96d90b2e1c2f","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,333 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6c29b73e-e76b-4d5f-9d2b-62c55fa3b0d4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 11:14:36,336 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6c29b73e-e76b-4d5f-9d2b-62c55fa3b0d4","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车"]}}
[D 2025-07-30 11:14:36,337 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"87592f32-991d-44f3-b876-e111b9e68c5b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"84fe3e23-2b40-4040-bb90-763278519c7f","pageId":5}}
[D 2025-07-30 11:14:36,339 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"87592f32-991d-44f3-b876-e111b9e68c5b","result":{"properties":["小菜\n今天\n火锅\n麻"]}}
[D 2025-07-30 11:14:36,340 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f8d178c8-c5dc-4a59-b91a-ec352c890832","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"40d1a2b2-2b06-4ada-be84-b1ab1a3c3cf6","pageId":5}}
[D 2025-07-30 11:14:36,343 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f8d178c8-c5dc-4a59-b91a-ec352c890832","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,343 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"203b1b8d-80c7-421c-8e61-0f92b13e2b42","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1798522a-0355-4cb9-878a-2662c62699ec","pageId":5}}
[D 2025-07-30 11:14:36,345 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"203b1b8d-80c7-421c-8e61-0f92b13e2b42","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,346 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"48d47001-f130-42bd-b99d-ae97bb811692","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"017cda2e-06ea-4efc-8e5f-3136d6be16b9","pageId":5}}
[D 2025-07-30 11:14:36,350 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"48d47001-f130-42bd-b99d-ae97bb811692","result":{"properties":["小菜\n今天\n火锅\n麻"]}}
[D 2025-07-30 11:14:36,351 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2d14f7d4-7ac0-4710-92fc-59d33b5f6e03","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f50a54-5068-45bb-8512-2ef08a88f1a9","pageId":5}}
[D 2025-07-30 11:14:36,353 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2d14f7d4-7ac0-4710-92fc-59d33b5f6e03","result":{"properties":["小菜\n今天"]}}
[D 2025-07-30 11:14:36,354 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"98983a9d-3dab-4e25-b3ce-db4515e7d60b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5e5a5b9d-ebba-48fd-8e8c-7427083a3431","pageId":5}}
[D 2025-07-30 11:14:36,356 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"98983a9d-3dab-4e25-b3ce-db4515e7d60b","result":{"properties":["小菜"]}}
[D 2025-07-30 11:14:36,357 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"8ce59f5f-e3f4-4022-8870-58b85b2eae38","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2eddfa6d-4c54-45fe-8273-abf4f76beb61","pageId":5}}
[D 2025-07-30 11:14:36,359 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"8ce59f5f-e3f4-4022-8870-58b85b2eae38","result":{"properties":["今天"]}}
[D 2025-07-30 11:14:36,359 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4dca2b45-4b43-460d-9b5c-e9ba69261a0b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"afc7817c-6592-43dc-8d27-e0463ab07866","pageId":5}}
[D 2025-07-30 11:14:36,362 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4dca2b45-4b43-460d-9b5c-e9ba69261a0b","result":{"properties":["火锅\n麻"]}}
[D 2025-07-30 11:14:36,362 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"de6778fa-8221-4f42-b123-67ae06698f91","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"068d01f0-3325-4651-b209-322aca03477c","pageId":5}}
[D 2025-07-30 11:14:36,365 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"de6778fa-8221-4f42-b123-67ae06698f91","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:36,366 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f442426f-53a5-4d02-ba15-b4309dfe8c6d","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"56e0c9f1-bc1a-419d-a53e-dd939b61a6af","pageId":5}}
[D 2025-07-30 11:14:36,370 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f442426f-53a5-4d02-ba15-b4309dfe8c6d","result":{"properties":["麻"]}}
[D 2025-07-30 11:14:36,370 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"03b02a17-05fc-4352-9f0d-eb609c24169d","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f6100758-a6d2-4540-8127-ee8389d19c6f","pageId":5}}
[D 2025-07-30 11:14:36,373 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"03b02a17-05fc-4352-9f0d-eb609c24169d","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,373 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0893abb9-4bd3-485a-bb25-e0a6cadaeb61","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"25f2dfad-4eb0-4dee-bdf6-e83e42d57839","pageId":5}}
[D 2025-07-30 11:14:36,375 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0893abb9-4bd3-485a-bb25-e0a6cadaeb61","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,375 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2a1e7750-e3c6-4815-9dc0-6ba8e48bc9dc","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"ded5dbd1-390b-4159-8440-2bc02f9e62d3","pageId":5}}
[D 2025-07-30 11:14:36,377 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2a1e7750-e3c6-4815-9dc0-6ba8e48bc9dc","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,378 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"9b72bc3a-7908-4a93-b59e-de3ee9c31b44","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"25284a2d-4a51-4d1c-b42e-4947747b403c","pageId":5}}
[D 2025-07-30 11:14:36,380 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"9b72bc3a-7908-4a93-b59e-de3ee9c31b44","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,380 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4fa7acdc-2d13-44b4-83aa-bfcf0d754bc3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"347410d1-a4ef-4571-bbb0-2333bae662c6","pageId":5}}
[D 2025-07-30 11:14:36,385 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4fa7acdc-2d13-44b4-83aa-bfcf0d754bc3","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,386 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c1b764b7-e2f1-439e-886f-3ef93e4fb712","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 11:14:36,390 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c1b764b7-e2f1-439e-886f-3ef93e4fb712","result":{"properties":["红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,390 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"413eb9d2-4c2e-4140-89bd-7c12131c130f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"fc1b3615-671c-425d-9001-663cab5fc45a","pageId":5}}
[D 2025-07-30 11:14:36,392 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"413eb9d2-4c2e-4140-89bd-7c12131c130f","result":{"properties":["红烧肉\n今天\n火锅\n甜"]}}
[D 2025-07-30 11:14:36,392 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d17f4ac1-0556-4c06-b9cc-e83f69216d14","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"df2d5721-82d1-4b22-b36b-ec0c4e9ef215","pageId":5}}
[D 2025-07-30 11:14:36,395 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d17f4ac1-0556-4c06-b9cc-e83f69216d14","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,395 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"89bd5a45-1ca6-46d2-9211-c77b24630300","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d3ebc075-dc89-4ffb-9939-38ff6e7185b8","pageId":5}}
[D 2025-07-30 11:14:36,397 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"89bd5a45-1ca6-46d2-9211-c77b24630300","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,400 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"038e4f13-ebe3-4860-b4b7-297d6b93a7d7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1f47a094-5f31-4bd4-8c08-028083c58508","pageId":5}}
[D 2025-07-30 11:14:36,403 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"038e4f13-ebe3-4860-b4b7-297d6b93a7d7","result":{"properties":["红烧肉\n今天\n火锅\n甜"]}}
[D 2025-07-30 11:14:36,404 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c67f35a5-9369-42f9-b63d-7075c4a77fcd","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"21ec1f89-1bae-4793-b8bd-6681fc47837d","pageId":5}}
[D 2025-07-30 11:14:36,407 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c67f35a5-9369-42f9-b63d-7075c4a77fcd","result":{"properties":["红烧肉\n今天"]}}
[D 2025-07-30 11:14:36,407 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"02d38995-c5a3-4ac7-8d06-a6c50b97ebc7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"7683ff7c-c5b6-40a8-9251-ce0260ef735e","pageId":5}}
[D 2025-07-30 11:14:36,409 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"02d38995-c5a3-4ac7-8d06-a6c50b97ebc7","result":{"properties":["红烧肉"]}}
[D 2025-07-30 11:14:36,409 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"01a3fe7b-cf05-4f0a-aad1-710c46556a0c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c9471ed-51de-4591-96b9-f3bcc042dbac","pageId":5}}
[D 2025-07-30 11:14:36,411 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"01a3fe7b-cf05-4f0a-aad1-710c46556a0c","result":{"properties":["今天"]}}
[D 2025-07-30 11:14:36,412 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0dd929bb-8b4c-4e60-8a2a-0ed79fc6a281","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"eda5d04e-3faf-49ec-9a70-335496cf5012","pageId":5}}
[D 2025-07-30 11:14:36,414 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0dd929bb-8b4c-4e60-8a2a-0ed79fc6a281","result":{"properties":["火锅\n甜"]}}
[D 2025-07-30 11:14:36,414 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"baad4a14-ddea-4f4a-8ed6-cded454ce5d5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9fe936f4-d7ff-42c5-b801-b8cd44e89e3a","pageId":5}}
[D 2025-07-30 11:14:36,420 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"baad4a14-ddea-4f4a-8ed6-cded454ce5d5","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:36,420 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"5332afa0-5598-42de-ad33-3a42f41979c6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"19f0b00f-d416-49c8-8aba-8ec8a995348b","pageId":5}}
[D 2025-07-30 11:14:36,422 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"5332afa0-5598-42de-ad33-3a42f41979c6","result":{"properties":["甜"]}}
[D 2025-07-30 11:14:36,423 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d359c5c3-f1a6-40e8-ab50-c216565d6687","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2e0ea4b0-4a3a-451d-abd5-875e4ffce728","pageId":5}}
[D 2025-07-30 11:14:36,425 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d359c5c3-f1a6-40e8-ab50-c216565d6687","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,425 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"8d2b6e61-25c4-4ebc-af30-e3e93f4b366f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"77d2c7c8-6ef8-48ae-852c-2ac885d34fc2","pageId":5}}
[D 2025-07-30 11:14:36,427 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"8d2b6e61-25c4-4ebc-af30-e3e93f4b366f","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,428 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"62b734ca-d0c4-4ba5-97a9-f906e21e4611","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"04514357-c37d-49e5-8d60-cd866e75a9f2","pageId":5}}
[D 2025-07-30 11:14:36,430 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"62b734ca-d0c4-4ba5-97a9-f906e21e4611","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,430 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7d5a06aa-9063-41e5-a2c5-790f98a4b082","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"69363ee4-6dc4-4b98-8ef1-08b8fe6b1928","pageId":5}}
[D 2025-07-30 11:14:36,436 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7d5a06aa-9063-41e5-a2c5-790f98a4b082","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,436 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"acd58f9e-bda0-4395-bb0e-fb788085685a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"fcddb264-b180-4dc5-a2fd-c34c7e9f3075","pageId":5}}
[D 2025-07-30 11:14:36,438 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"acd58f9e-bda0-4395-bb0e-fb788085685a","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:36,438 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2e2df7b5-5300-4a8e-b4ba-072b8bdad66b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"3a291262-f404-41f6-8cbe-fe65b5e40a95","pageId":5}}
[D 2025-07-30 11:14:36,440 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2e2df7b5-5300-4a8e-b4ba-072b8bdad66b","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,441 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2284c460-0f1a-482b-96f4-faf6acddd8cf","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"bd244f4f-22da-4ea9-9f19-16d50dbfca1b","pageId":5}}
[D 2025-07-30 11:14:36,443 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2284c460-0f1a-482b-96f4-faf6acddd8cf","result":{"properties":[""]}}
[D 2025-07-30 11:14:36,444 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"fb8ae1f1-7abe-403f-b39e-c550de9d39ed","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,445 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"fb8ae1f1-7abe-403f-b39e-c550de9d39ed","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,446 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b8d42b57-8de7-43ee-8a8d-0644d26e78d4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,452 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b8d42b57-8de7-43ee-8a8d-0644d26e78d4","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:36,453 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"bf6091b5-fcea-44a7-b50a-3be01d7e60e8","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,455 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"bf6091b5-fcea-44a7-b50a-3be01d7e60e8","result":{"styles":["auto"]}}
[D 2025-07-30 11:14:36,455 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b9a4d48b-9118-485e-b549-5c1227fa267d","method":"Element.tap","params":{"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:36,528 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b9a4d48b-9118-485e-b549-5c1227fa267d","result":{"pageX":188,"pageY":321,"clientX":188,"clientY":321}}
[D 2025-07-30 11:14:40,529 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6bedcf4b-4c94-4594-9ee6-60dc45a4cdf7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:40,530 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6bedcf4b-4c94-4594-9ee6-60dc45a4cdf7","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:14:40,532 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:40,532 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"029876b7-2dd8-433a-8477-16bcb2eb85ee","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:40,535 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"029876b7-2dd8-433a-8477-16bcb2eb85ee","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:14:40,535 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"8b886a5b-0b9c-46a0-9c92-9ed6991c9552","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","pageId":5}}
[D 2025-07-30 11:14:40,538 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"8b886a5b-0b9c-46a0-9c92-9ed6991c9552","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:40,539 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4b530c77-22ee-415b-89c9-f354458e52d3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","pageId":5}}
[D 2025-07-30 11:14:40,541 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4b530c77-22ee-415b-89c9-f354458e52d3","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:40,541 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2b072a58-5e97-4b5c-b68b-8faa40d759ba","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","pageId":5}}
[D 2025-07-30 11:14:40,544 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2b072a58-5e97-4b5c-b68b-8faa40d759ba","result":{"styles":["auto"]}}
[D 2025-07-30 11:14:40,545 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"687386ee-16c1-44c7-baf5-7be6089eac95","method":"Element.tap","params":{"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","pageId":5}}
[D 2025-07-30 11:14:40,601 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"687386ee-16c1-44c7-baf5-7be6089eac95","result":{"pageX":188,"pageY":321,"clientX":188,"clientY":321}}
[D 2025-07-30 11:14:44,602 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"50e6630b-da8b-4553-b869-c9202ee236af","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:44,604 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"50e6630b-da8b-4553-b869-c9202ee236af","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:14:44,604 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:44,605 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b4e01780-f756-4262-85cf-6b567ae47f15","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:44,607 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b4e01780-f756-4262-85cf-6b567ae47f15","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:14:44,608 minium page#716 _get_elements_by_css] try to get elements: .cart-bottom, .bottom-bar, .fixed-bottom
[D 2025-07-30 11:14:44,608 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1dda1475-62bf-47b6-8e5c-709c65dfae08","method":"Page.getElements","params":{"selector":".cart-bottom, .bottom-bar, .fixed-bottom","pageId":5}}
[D 2025-07-30 11:14:44,611 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1dda1475-62bf-47b6-8e5c-709c65dfae08","result":{"elements":[]}}
[W 2025-07-30 11:14:44,612 minium page#747 _get_elements_by_css] Could not found any element '.cart-bottom, .bottom-bar, .fixed-bottom' you need
[I 2025-07-30 11:14:44,612 minium page#716 _get_elements_by_css] try to get elements: button
[D 2025-07-30 11:14:44,612 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"9c8cb44f-2e33-41b1-b0c0-86e680a1c10b","method":"Page.getElements","params":{"selector":"button","pageId":5}}
[D 2025-07-30 11:14:44,617 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"9c8cb44f-2e33-41b1-b0c0-86e680a1c10b","result":{"elements":[]}}
[W 2025-07-30 11:14:44,618 minium page#747 _get_elements_by_css] Could not found any element 'button' you need
[D 2025-07-30 11:14:44,619 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0edb84fe-b414-436f-89c8-c12d92828568","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:44,620 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0edb84fe-b414-436f-89c8-c12d92828568","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:14:44,621 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:44,621 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"816376a5-51be-4e82-bca2-bdd059c61dd1","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:44,623 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"816376a5-51be-4e82-bca2-bdd059c61dd1","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:14:44,624 minium page#716 _get_elements_by_css] try to get elements: .success, .toast, .modal
[D 2025-07-30 11:14:44,624 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"44832fb7-9625-4a74-99b0-1f5f9ad2b5e2","method":"Page.getElements","params":{"selector":".success, .toast, .modal","pageId":5}}
[D 2025-07-30 11:14:44,627 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"44832fb7-9625-4a74-99b0-1f5f9ad2b5e2","result":{"elements":[]}}
[W 2025-07-30 11:14:44,627 minium page#747 _get_elements_by_css] Could not found any element '.success, .toast, .modal' you need
[I 2025-07-30 11:14:44,627 minium page#716 _get_elements_by_css] try to get elements: text, view
[D 2025-07-30 11:14:44,628 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"06a8db7f-10dd-4099-8d26-9524c56e8cf3","method":"Page.getElements","params":{"selector":"text, view","pageId":5}}
[D 2025-07-30 11:14:44,634 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"06a8db7f-10dd-4099-8d26-9524c56e8cf3","result":{"elements":[{"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","tagName":"view"},{"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","tagName":"view"},{"elementId":"6aa13ac2-fe8f-4720-99ca-0c7bb789a391","tagName":"view"},{"elementId":"d05a9803-35be-48ff-97ac-0a8ad8929e1b","tagName":"view"},{"elementId":"9e876aa4-6044-4de7-b7b6-56caf4194741","tagName":"text"},{"elementId":"6a3546b1-e824-4023-9018-a3cf2b7e0a5b","tagName":"view"},{"elementId":"ce3eea75-2a
[I 2025-07-30 11:14:44,635 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF750>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFED0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF4D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD9F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFF70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDF90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCECB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDEF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD450>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE170>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD8B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDA90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDB30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDBD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDC70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDD10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDDB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDE50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE030>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE0D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE2B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE350>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE3F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE490>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE530>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE5D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE670>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEA30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE8F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD590>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE990>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEAD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEB70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEC10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEE90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEF30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEFD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF070>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF110>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF1B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF250>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF2F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF390>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF430>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF6B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF890>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF930>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF9D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFA70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFB10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFBB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFC50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFCF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFD90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFE30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384050>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384190>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000282143840F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384230>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000282143842D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384550>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384910>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000282143847D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000282143844B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000282143849B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384AF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028214384A50>]
[D 2025-07-30 11:14:44,636 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"673c09eb-07ea-4a7e-8a3c-f7a9987124d7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c11fbcd-92e8-4f6a-8874-d2bae4ab9914","pageId":5}}
[D 2025-07-30 11:14:44,640 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"673c09eb-07ea-4a7e-8a3c-f7a9987124d7","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:44,640 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"bfddd02d-39ad-4c9d-950a-3b41015be53f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71691f24-020e-4b43-9211-822bb37b83ae","pageId":5}}
[D 2025-07-30 11:14:44,644 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"bfddd02d-39ad-4c9d-950a-3b41015be53f","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃\n小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:44,645 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"999b58b1-d79d-482c-bfce-a23afda76b67","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6aa13ac2-fe8f-4720-99ca-0c7bb789a391","pageId":5}}
[D 2025-07-30 11:14:44,648 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"999b58b1-d79d-482c-bfce-a23afda76b67","result":{"properties":["全部\n热菜\n火锅\n江湖菜\n小面\n川菜\n湘菜\n粤菜\n凉菜\n汤品\n主食\n素菜\n甜品\n饮品\n酒水\n小吃"]}}
[D 2025-07-30 11:14:44,650 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"202e738f-f63d-45da-b864-bb4a4be4b036","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d05a9803-35be-48ff-97ac-0a8ad8929e1b","pageId":5}}
[D 2025-07-30 11:14:44,654 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"202e738f-f63d-45da-b864-bb4a4be4b036","result":{"properties":["全部"]}}
[D 2025-07-30 11:14:44,654 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7ba12624-ed4b-474c-aef9-e8a38b6dd8e4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9e876aa4-6044-4de7-b7b6-56caf4194741","pageId":5}}
[D 2025-07-30 11:14:44,657 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7ba12624-ed4b-474c-aef9-e8a38b6dd8e4","result":{"properties":["全部"]}}
[D 2025-07-30 11:14:44,657 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"54af8320-4f21-420a-b5d6-eb9d42b2063b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6a3546b1-e824-4023-9018-a3cf2b7e0a5b","pageId":5}}
[D 2025-07-30 11:14:44,660 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"54af8320-4f21-420a-b5d6-eb9d42b2063b","result":{"properties":["热菜"]}}
[D 2025-07-30 11:14:44,661 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a4ffe046-4c29-4273-9470-65dfa6bee047","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"ce3eea75-2a04-4dea-8b86-e4320ba46955","pageId":5}}
[D 2025-07-30 11:14:44,663 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a4ffe046-4c29-4273-9470-65dfa6bee047","result":{"properties":["热菜"]}}
[D 2025-07-30 11:14:44,663 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2a2f72de-51ce-4f79-99b7-7c705b82b67c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d3ea0e51-f683-4125-9fc6-dfcc1940ca41","pageId":5}}
[D 2025-07-30 11:14:44,670 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2a2f72de-51ce-4f79-99b7-7c705b82b67c","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:44,670 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a48a3473-b006-4d99-9989-f6f019d3677f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2e66a2f8-7df0-450d-b916-006ad6d4a551","pageId":5}}
[D 2025-07-30 11:14:44,673 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a48a3473-b006-4d99-9989-f6f019d3677f","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:44,673 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"3bf5ffc9-cd7b-41e8-b0f2-5a636e429da0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a82df277-8584-4174-85c7-8523865fdcdb","pageId":5}}
[D 2025-07-30 11:14:44,676 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"3bf5ffc9-cd7b-41e8-b0f2-5a636e429da0","result":{"properties":["江湖菜"]}}
[D 2025-07-30 11:14:44,676 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"83779b70-f8bc-4d12-b7ed-1e6f37b7e7eb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"99387b0e-b628-42ee-8f2f-0edbbef12daf","pageId":5}}
[D 2025-07-30 11:14:44,678 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"83779b70-f8bc-4d12-b7ed-1e6f37b7e7eb","result":{"properties":["江湖菜"]}}
[D 2025-07-30 11:14:44,679 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a2d1873b-b09f-42a4-85b8-b98bdc9c3ae1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"39d71dd6-6395-4722-8caa-46ce57f8c96e","pageId":5}}
[D 2025-07-30 11:14:44,684 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a2d1873b-b09f-42a4-85b8-b98bdc9c3ae1","result":{"properties":["小面"]}}
[D 2025-07-30 11:14:44,685 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7f049498-c5f2-4c05-9fb3-cbada5dc995b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"7595326b-67fd-4146-a9be-69d55cdc31f9","pageId":5}}
[D 2025-07-30 11:14:44,688 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7f049498-c5f2-4c05-9fb3-cbada5dc995b","result":{"properties":["小面"]}}
[D 2025-07-30 11:14:44,688 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"dc0ead70-0e7d-41b7-9279-34aec6a58d38","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71b4a56a-b8d3-4480-befe-b14dc81c94c6","pageId":5}}
[D 2025-07-30 11:14:44,690 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"dc0ead70-0e7d-41b7-9279-34aec6a58d38","result":{"properties":["川菜"]}}
[D 2025-07-30 11:14:44,691 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d96f82cc-af86-4d80-96dc-7915473a20a6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1b2b04bd-c31d-4e62-8810-7ebc790fec1e","pageId":5}}
[D 2025-07-30 11:14:44,693 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d96f82cc-af86-4d80-96dc-7915473a20a6","result":{"properties":["川菜"]}}
[D 2025-07-30 11:14:44,694 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"eea51a0c-fe48-40b6-9279-e0ff9e239dbb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"87a98043-ec5f-411b-9bac-d9e0d29655e5","pageId":5}}
[D 2025-07-30 11:14:44,697 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"eea51a0c-fe48-40b6-9279-e0ff9e239dbb","result":{"properties":["湘菜"]}}
[D 2025-07-30 11:14:44,697 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a990ace9-78cd-4733-b325-8d7652e090e2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"89b1736e-13f0-44d9-8fb5-4aa0a4d4fb82","pageId":5}}
[D 2025-07-30 11:14:44,703 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a990ace9-78cd-4733-b325-8d7652e090e2","result":{"properties":["湘菜"]}}
[D 2025-07-30 11:14:44,704 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"687dc213-680d-4115-a692-d56fe04586f8","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2567dc73-a053-45ee-853d-dc1e92276cc0","pageId":5}}
[D 2025-07-30 11:14:44,706 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"687dc213-680d-4115-a692-d56fe04586f8","result":{"properties":["粤菜"]}}
[D 2025-07-30 11:14:44,706 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a6edac27-f685-4def-98bd-134afacea67f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5367d370-10c1-417e-90e3-1e186e89a77b","pageId":5}}
[D 2025-07-30 11:14:44,709 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a6edac27-f685-4def-98bd-134afacea67f","result":{"properties":["粤菜"]}}
[D 2025-07-30 11:14:44,709 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0de2b57c-38dc-4be4-9a61-5e56c691563f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c16a7e7-9098-451f-bf3b-a84e951b5d45","pageId":5}}
[D 2025-07-30 11:14:44,712 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0de2b57c-38dc-4be4-9a61-5e56c691563f","result":{"properties":["凉菜"]}}
[D 2025-07-30 11:14:44,712 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"73299a5c-2a4f-4e69-921e-be23e47ccf01","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"685f1b82-6fd3-4b48-aeb8-8a0c649a5be6","pageId":5}}
[D 2025-07-30 11:14:44,716 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"73299a5c-2a4f-4e69-921e-be23e47ccf01","result":{"properties":["凉菜"]}}
[D 2025-07-30 11:14:44,718 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"8764182c-db21-4cf2-bec7-b45d07615eb5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f37b4a-c4d4-45f8-b571-406a5208ffaf","pageId":5}}
[D 2025-07-30 11:14:44,721 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"8764182c-db21-4cf2-bec7-b45d07615eb5","result":{"properties":["汤品"]}}
[D 2025-07-30 11:14:44,722 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"9f46b7d4-98ef-4cb1-b2a3-41a002895e7d","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"fee1bd18-ee93-4146-9dfd-61cb52065268","pageId":5}}
[D 2025-07-30 11:14:44,724 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"9f46b7d4-98ef-4cb1-b2a3-41a002895e7d","result":{"properties":["汤品"]}}
[D 2025-07-30 11:14:44,725 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7c294bf9-849b-48d5-816d-3272bebc1faf","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"72569af5-3c3f-4747-8d7a-c92c2d98d20d","pageId":5}}
[D 2025-07-30 11:14:44,727 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7c294bf9-849b-48d5-816d-3272bebc1faf","result":{"properties":["主食"]}}
[D 2025-07-30 11:14:44,727 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f0ae1171-5559-42e4-a67f-cc1ed2281578","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d9c4a079-6422-4dff-8f42-2ffbbb180522","pageId":5}}
[D 2025-07-30 11:14:44,729 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f0ae1171-5559-42e4-a67f-cc1ed2281578","result":{"properties":["主食"]}}
[D 2025-07-30 11:14:44,730 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"80f0bb81-b216-406b-a618-1affb749be66","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"8bf3ccbb-9d5c-480a-b648-301e0598adda","pageId":5}}
[D 2025-07-30 11:14:44,735 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"80f0bb81-b216-406b-a618-1affb749be66","result":{"properties":["素菜"]}}
[D 2025-07-30 11:14:44,736 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"31fa74cb-d9c9-430c-a773-cb6bc4c773ab","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"66b04483-aeea-4db3-aeee-c8203fd76077","pageId":5}}
[D 2025-07-30 11:14:44,739 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"31fa74cb-d9c9-430c-a773-cb6bc4c773ab","result":{"properties":["素菜"]}}
[D 2025-07-30 11:14:44,740 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7b695f61-150f-404e-81ed-4bcc89d99c98","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"eb22e17e-8f98-4cec-9aaa-d7198effe874","pageId":5}}
[D 2025-07-30 11:14:44,742 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7b695f61-150f-404e-81ed-4bcc89d99c98","result":{"properties":["甜品"]}}
[D 2025-07-30 11:14:44,742 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"97404bcc-5585-475c-b411-374d98e0be6f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"92c3c1ce-284b-4b56-a3ce-4d5e67fdafd1","pageId":5}}
[D 2025-07-30 11:14:44,745 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"97404bcc-5585-475c-b411-374d98e0be6f","result":{"properties":["甜品"]}}
[D 2025-07-30 11:14:44,745 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e1bfafa5-2951-4290-a8c0-f626db5a54cb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"8dc9ea6f-22af-4006-9b27-f056e564a6dc","pageId":5}}
[D 2025-07-30 11:14:44,747 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e1bfafa5-2951-4290-a8c0-f626db5a54cb","result":{"properties":["饮品"]}}
[D 2025-07-30 11:14:44,747 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a8bdfdfe-1778-482c-bc60-2df8857459c9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9fa23ae6-1039-4913-9245-46168563384e","pageId":5}}
[D 2025-07-30 11:14:44,753 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a8bdfdfe-1778-482c-bc60-2df8857459c9","result":{"properties":["饮品"]}}
[D 2025-07-30 11:14:44,753 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"daa53dc1-6005-4967-80a5-22dd84334cc4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"b5092352-9ad4-4ea6-9747-28a1675288d8","pageId":5}}
[D 2025-07-30 11:14:44,755 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"daa53dc1-6005-4967-80a5-22dd84334cc4","result":{"properties":["酒水"]}}
[D 2025-07-30 11:14:44,756 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f9564c78-bf4c-46ab-84e9-6a44db433349","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"4710ff73-e66c-4c5b-8c43-b8c111459c44","pageId":5}}
[D 2025-07-30 11:14:44,758 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f9564c78-bf4c-46ab-84e9-6a44db433349","result":{"properties":["酒水"]}}
[D 2025-07-30 11:14:44,759 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"10226d6a-eec5-428e-b76d-82fd57b91769","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"91b60564-9e82-437c-bcfa-de73d35afab7","pageId":5}}
[D 2025-07-30 11:14:44,761 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"10226d6a-eec5-428e-b76d-82fd57b91769","result":{"properties":["小吃"]}}
[D 2025-07-30 11:14:44,762 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ea0ef71c-03bf-449d-8191-4dfd54b08d09","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f5b3d78b-7064-4105-9945-2c10f4de4aad","pageId":5}}
[D 2025-07-30 11:14:44,764 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ea0ef71c-03bf-449d-8191-4dfd54b08d09","result":{"properties":["小吃"]}}
[D 2025-07-30 11:14:44,766 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d929f121-34fc-46f9-a1c3-451e0f91f3c6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a3f2b326-1fb4-498d-af6d-5342a8d1e99f","pageId":5}}
[D 2025-07-30 11:14:44,770 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d929f121-34fc-46f9-a1c3-451e0f91f3c6","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:44,771 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1eca9419-e547-4ecd-8564-9cda268322b6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"3cd450c0-bc2a-4a20-87f1-731c1373518a","pageId":5}}
[D 2025-07-30 11:14:44,772 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1eca9419-e547-4ecd-8564-9cda268322b6","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车\n红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:44,773 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"be2cedab-9175-438e-bd62-b022495cb501","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 11:14:44,775 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"be2cedab-9175-438e-bd62-b022495cb501","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车"]}}
[D 2025-07-30 11:14:44,776 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"3840c558-3139-4de1-997c-89cf7f0a48e9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"84fe3e23-2b40-4040-bb90-763278519c7f","pageId":5}}
[D 2025-07-30 11:14:44,778 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"3840c558-3139-4de1-997c-89cf7f0a48e9","result":{"properties":["小菜\n今天\n火锅\n麻"]}}
[D 2025-07-30 11:14:44,779 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4174ffa9-a541-427c-bcc5-83b4821557de","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"40d1a2b2-2b06-4ada-be84-b1ab1a3c3cf6","pageId":5}}
[D 2025-07-30 11:14:44,782 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4174ffa9-a541-427c-bcc5-83b4821557de","result":{"properties":[""]}}
[D 2025-07-30 11:14:44,783 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"79847bc7-fb4e-4bee-9e1d-c47008906892","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1798522a-0355-4cb9-878a-2662c62699ec","pageId":5}}
[D 2025-07-30 11:14:44,786 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"79847bc7-fb4e-4bee-9e1d-c47008906892","result":{"properties":[""]}}
[D 2025-07-30 11:14:44,787 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"83aeecf4-a79f-42e9-9364-6ed0300d8102","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"017cda2e-06ea-4efc-8e5f-3136d6be16b9","pageId":5}}
[D 2025-07-30 11:14:44,789 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"83aeecf4-a79f-42e9-9364-6ed0300d8102","result":{"properties":["小菜\n今天\n火锅\n麻"]}}
[D 2025-07-30 11:14:44,790 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"8d1ce82f-7105-4a43-b92b-41c79e73028e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f50a54-5068-45bb-8512-2ef08a88f1a9","pageId":5}}
[D 2025-07-30 11:14:44,792 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"8d1ce82f-7105-4a43-b92b-41c79e73028e","result":{"properties":["小菜\n今天"]}}
[D 2025-07-30 11:14:44,792 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"eaeaf457-f477-4c92-9c52-7f1840a49199","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5e5a5b9d-ebba-48fd-8e8c-7427083a3431","pageId":5}}
[D 2025-07-30 11:14:44,794 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"eaeaf457-f477-4c92-9c52-7f1840a49199","result":{"properties":["小菜"]}}
[D 2025-07-30 11:14:44,795 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0898b18b-0380-44e3-8d73-26575c51ac3a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2eddfa6d-4c54-45fe-8273-abf4f76beb61","pageId":5}}
[D 2025-07-30 11:14:44,797 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0898b18b-0380-44e3-8d73-26575c51ac3a","result":{"properties":["今天"]}}
[D 2025-07-30 11:14:44,797 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"46973d35-44ad-4e83-892e-1dd70277dbd7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"afc7817c-6592-43dc-8d27-e0463ab07866","pageId":5}}
[D 2025-07-30 11:14:44,802 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"46973d35-44ad-4e83-892e-1dd70277dbd7","result":{"properties":["火锅\n麻"]}}
[D 2025-07-30 11:14:44,803 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"321adb86-7c1d-4d5e-ad2c-4fd7ee7d6f89","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"068d01f0-3325-4651-b209-322aca03477c","pageId":5}}
[D 2025-07-30 11:14:44,806 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"321adb86-7c1d-4d5e-ad2c-4fd7ee7d6f89","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:44,807 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e1141197-ac2b-470f-bd28-7a2e1354a715","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"56e0c9f1-bc1a-419d-a53e-dd939b61a6af","pageId":5}}
[D 2025-07-30 11:14:44,809 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e1141197-ac2b-470f-bd28-7a2e1354a715","result":{"properties":["麻"]}}
[D 2025-07-30 11:14:44,810 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2efbca41-3cd5-43bc-98d8-df23db505bd2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f6100758-a6d2-4540-8127-ee8389d19c6f","pageId":5}}
[D 2025-07-30 11:14:44,813 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2efbca41-3cd5-43bc-98d8-df23db505bd2","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,813 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"52540c0b-2380-446e-9cf2-c9bed38447ff","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"25f2dfad-4eb0-4dee-bdf6-e83e42d57839","pageId":5}}
[D 2025-07-30 11:14:44,819 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"52540c0b-2380-446e-9cf2-c9bed38447ff","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,819 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"79ac485a-a90c-4886-ab00-7802ae21eb8d","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"ded5dbd1-390b-4159-8440-2bc02f9e62d3","pageId":5}}
[D 2025-07-30 11:14:44,821 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"79ac485a-a90c-4886-ab00-7802ae21eb8d","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,822 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"713448b3-a3b2-43e2-b825-3538bb1c4b9a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"25284a2d-4a51-4d1c-b42e-4947747b403c","pageId":5}}
[D 2025-07-30 11:14:44,824 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"713448b3-a3b2-43e2-b825-3538bb1c4b9a","result":{"properties":[""]}}
[D 2025-07-30 11:14:44,824 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"508ae5cc-3994-45c8-b6f1-65f34f48cf21","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"347410d1-a4ef-4571-bbb0-2333bae662c6","pageId":5}}
[D 2025-07-30 11:14:44,827 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"508ae5cc-3994-45c8-b6f1-65f34f48cf21","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,827 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4d24bb5c-338f-4568-b9c3-42127c726250","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 11:14:44,829 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4d24bb5c-338f-4568-b9c3-42127c726250","result":{"properties":["红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 11:14:44,830 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c1da8054-c8c0-472c-8363-dd592e623977","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"fc1b3615-671c-425d-9001-663cab5fc45a","pageId":5}}
[D 2025-07-30 11:14:44,835 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c1da8054-c8c0-472c-8363-dd592e623977","result":{"properties":["红烧肉\n今天\n火锅\n甜"]}}
[D 2025-07-30 11:14:44,836 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"014ae1fc-22a6-4f45-b79b-82f7452d4cb9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"df2d5721-82d1-4b22-b36b-ec0c4e9ef215","pageId":5}}
[D 2025-07-30 11:14:44,838 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"014ae1fc-22a6-4f45-b79b-82f7452d4cb9","result":{"properties":[""]}}
[D 2025-07-30 11:14:44,839 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"5ea10127-dca3-4e09-995d-3ebf8dcc345b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d3ebc075-dc89-4ffb-9939-38ff6e7185b8","pageId":5}}
[D 2025-07-30 11:14:44,841 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"5ea10127-dca3-4e09-995d-3ebf8dcc345b","result":{"properties":[""]}}
[D 2025-07-30 11:14:44,841 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"5d52e14b-326b-4cde-a77f-e952542098f9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"1f47a094-5f31-4bd4-8c08-028083c58508","pageId":5}}
[D 2025-07-30 11:14:44,843 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"5d52e14b-326b-4cde-a77f-e952542098f9","result":{"properties":["红烧肉\n今天\n火锅\n甜"]}}
[D 2025-07-30 11:14:44,844 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"de29a681-ff5e-4134-9229-f42927af6c46","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"21ec1f89-1bae-4793-b8bd-6681fc47837d","pageId":5}}
[D 2025-07-30 11:14:44,845 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"de29a681-ff5e-4134-9229-f42927af6c46","result":{"properties":["红烧肉\n今天"]}}
[D 2025-07-30 11:14:44,846 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"da09988d-f62a-4d9a-a55c-9c771e8fec07","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"7683ff7c-c5b6-40a8-9251-ce0260ef735e","pageId":5}}
[D 2025-07-30 11:14:44,850 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"da09988d-f62a-4d9a-a55c-9c771e8fec07","result":{"properties":["红烧肉"]}}
[D 2025-07-30 11:14:44,851 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b4f4b8df-0a1b-4be9-ba67-c0fcb8878d24","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9c9471ed-51de-4591-96b9-f3bcc042dbac","pageId":5}}
[D 2025-07-30 11:14:44,853 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b4f4b8df-0a1b-4be9-ba67-c0fcb8878d24","result":{"properties":["今天"]}}
[D 2025-07-30 11:14:44,854 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"59d52892-b102-465e-8d82-27e4c42231cf","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"eda5d04e-3faf-49ec-9a70-335496cf5012","pageId":5}}
[D 2025-07-30 11:14:44,856 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"59d52892-b102-465e-8d82-27e4c42231cf","result":{"properties":["火锅\n甜"]}}
[D 2025-07-30 11:14:44,857 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"940402f8-89ee-4748-a58e-5f6d27e8a668","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9fe936f4-d7ff-42c5-b801-b8cd44e89e3a","pageId":5}}
[D 2025-07-30 11:14:44,859 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"940402f8-89ee-4748-a58e-5f6d27e8a668","result":{"properties":["火锅"]}}
[D 2025-07-30 11:14:44,859 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d8c9e450-6866-4a2b-a589-c0c6149be96b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"19f0b00f-d416-49c8-8aba-8ec8a995348b","pageId":5}}
[D 2025-07-30 11:14:44,861 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d8c9e450-6866-4a2b-a589-c0c6149be96b","result":{"properties":["甜"]}}
[D 2025-07-30 11:14:44,862 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"193450a6-1e19-4f78-93da-660402e52ea1","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2e0ea4b0-4a3a-451d-abd5-875e4ffce728","pageId":5}}
[D 2025-07-30 11:14:44,864 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"193450a6-1e19-4f78-93da-660402e52ea1","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,866 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e987625b-3daa-4395-aab5-57500eea6f2b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"77d2c7c8-6ef8-48ae-852c-2ac885d34fc2","pageId":5}}
[D 2025-07-30 11:14:44,869 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e987625b-3daa-4395-aab5-57500eea6f2b","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,870 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d4af6700-3499-4001-a409-075285e574b2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"04514357-c37d-49e5-8d60-cd866e75a9f2","pageId":5}}
[D 2025-07-30 11:14:44,872 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d4af6700-3499-4001-a409-075285e574b2","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,872 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6bd2dc5b-c167-42c4-a8eb-25b24c784378","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"69363ee4-6dc4-4b98-8ef1-08b8fe6b1928","pageId":5}}
[D 2025-07-30 11:14:44,875 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6bd2dc5b-c167-42c4-a8eb-25b24c784378","result":{"properties":[""]}}
[D 2025-07-30 11:14:44,875 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"086c8ea7-2d93-4c8a-b61e-97689ffdcaf3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"fcddb264-b180-4dc5-a2fd-c34c7e9f3075","pageId":5}}
[D 2025-07-30 11:14:44,877 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"086c8ea7-2d93-4c8a-b61e-97689ffdcaf3","result":{"properties":["加入购物车"]}}
[D 2025-07-30 11:14:44,878 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ad0d05d3-b363-4800-87ab-e46d139dd547","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"3a291262-f404-41f6-8cbe-fe65b5e40a95","pageId":5}}
[D 2025-07-30 11:14:44,880 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ad0d05d3-b363-4800-87ab-e46d139dd547","result":{"properties":[""]}}
[D 2025-07-30 11:14:44,880 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e8234416-bb1b-4e45-9ad6-0c7dc8faf524","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"bd244f4f-22da-4ea9-9f19-16d50dbfca1b","pageId":5}}
[D 2025-07-30 11:14:44,885 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e8234416-bb1b-4e45-9ad6-0c7dc8faf524","result":{"properties":[""]}}
[I 2025-07-30 11:14:44,885 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:14:44,886 minium minitest#799 _miniTearDown] =========Current case Down: test_01_enhanced_order_flow_with_cart=========
[I 2025-07-30 11:14:44,886 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:14:44,886 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f7f7af35-8d2a-48b7-9e06-953e2ab3898c","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:14:44,949 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f7f7af35-8d2a-48b7-9e06-953e2ab3898c","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:14:44,951 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:14:44,951 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 11:14:44,955 minium minitest#432 _miniSetUp] =========Current case: test_02_dish_management_deep_test=========
[I 2025-07-30 11:14:44,955 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.enhanced_functional_test, case info: EnhancedFunctionalTest.test_02_dish_management_deep_test
[I 2025-07-30 11:14:44,956 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:14:44,956 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:14:44,956 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:14:44,956 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:14:44,957 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:14:44,974 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:14:44,974 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"87d6bfb0-4198-4030-9a7d-03138aabcf83","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:14:45,048 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"87d6bfb0-4198-4030-9a7d-03138aabcf83","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:14:45,050 minium minitest#487 _miniSetUp] =========case: test_02_dish_management_deep_test start=========
[I 2025-07-30 11:14:46,051 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:14:46,051 minium.Conn1504 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"4fb06377-243d-422e-a2bc-0ed70eb975fe","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:14:46,053 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:14:46,299 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753845286294,"webviewId":3,"routeEventId":"3_1753845286092","renderer":"webview"},1753845286295]}}
[D 2025-07-30 11:14:46,300 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4fb06377-243d-422e-a2bc-0ed70eb975fe","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:14:46,300 minium.App6208 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753845286294, 'webviewId': 3, 'routeEventId': '3_1753845286092', 'renderer': 'webview'}, 1753845286295]}
[I 2025-07-30 11:14:46,300 minium.Conn1504 connection#704 _handle_async_msg] received async msg: 4fb06377-243d-422e-a2bc-0ed70eb975fe
[D 2025-07-30 11:14:48,301 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b1945ff0-3c17-4ec0-a750-d425f7278ebd","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:48,303 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b1945ff0-3c17-4ec0-a750-d425f7278ebd","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:14:48,303 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:48,304 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1e8a0b63-5bc6-4a2f-9bd9-4a438670ba54","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:48,305 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1e8a0b63-5bc6-4a2f-9bd9-4a438670ba54","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:14:48,306 minium page#716 _get_elements_by_css] try to get elements: view, text, button
[D 2025-07-30 11:14:48,306 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"dd412129-7f0a-4d84-8c6d-b515eca35a51","method":"Page.getElements","params":{"selector":"view, text, button","pageId":3}}
[D 2025-07-30 11:14:48,310 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"dd412129-7f0a-4d84-8c6d-b515eca35a51","result":{"elements":[{"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","tagName":"view"},{"elementId":"62d59b6d-1d04-4e5c-8dcb-d75d1f732104","tagName":"view"},{"elementId":"cca213e8-b08e-4419-9ce0-fc2085aae648","tagName":"view"},{"elementId":"d5c87356-e7c7-48dd-8a3c-fc78d291fd5d","tagName":"view"},{"elementId":"4c2a227c-5d1e-4831-937e-ce951978dfe7","tagName":"view"},{"elementId":"bfd2023f-cfaa-42dd-8cb4-61118a76f7ab","tagName":"view"},{"elementId":"a06aa7be-cd
[I 2025-07-30 11:14:48,310 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDC70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDEF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDBD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCECB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDB30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE0D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDD10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD450>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE170>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDDB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE3F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDE50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE350>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE2B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE490>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE530>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE5D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE670>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEA30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE8F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD590>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE990>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEAD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEB70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEC10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEE90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEF30>]
[D 2025-07-30 11:14:48,311 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2d306c00-54f3-4cf4-8bf1-4fe6ff68102b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","pageId":3}}
[D 2025-07-30 11:14:48,314 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2d306c00-54f3-4cf4-8bf1-4fe6ff68102b","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b\n留言\n用户关联\n我的菜品\n通知中心\n我的订单\n退出登录"]}}
[D 2025-07-30 11:14:48,316 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"369c680d-7372-43a2-903e-a2b2bb063bc3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"62d59b6d-1d04-4e5c-8dcb-d75d1f732104","pageId":3}}
[D 2025-07-30 11:14:48,320 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"369c680d-7372-43a2-903e-a2b2bb063bc3","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:14:48,321 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b12f0b58-ae9f-4895-9bef-ed15d6cdd125","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"cca213e8-b08e-4419-9ce0-fc2085aae648","pageId":3}}
[D 2025-07-30 11:14:48,323 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b12f0b58-ae9f-4895-9bef-ed15d6cdd125","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,324 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"fc81741e-2bd5-4d88-8875-e020c9dcd026","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d5c87356-e7c7-48dd-8a3c-fc78d291fd5d","pageId":3}}
[D 2025-07-30 11:14:48,326 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"fc81741e-2bd5-4d88-8875-e020c9dcd026","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:14:48,326 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"272ae568-cb14-42d9-9172-b8dc1d3184cf","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"4c2a227c-5d1e-4831-937e-ce951978dfe7","pageId":3}}
[D 2025-07-30 11:14:48,329 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"272ae568-cb14-42d9-9172-b8dc1d3184cf","result":{"properties":["李虎"]}}
[D 2025-07-30 11:14:48,329 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"96d1ec56-9207-4d3c-a1c4-22c4f8181704","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"bfd2023f-cfaa-42dd-8cb4-61118a76f7ab","pageId":3}}
[D 2025-07-30 11:14:48,335 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"96d1ec56-9207-4d3c-a1c4-22c4f8181704","result":{"properties":["📱 182****5876"]}}
[D 2025-07-30 11:14:48,336 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0cdfafa6-db10-4bac-9598-03dcf34e5129","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a06aa7be-cd94-42f9-85d0-d4f8c18ee1a0","pageId":3}}
[D 2025-07-30 11:14:48,338 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0cdfafa6-db10-4bac-9598-03dcf34e5129","result":{"properties":["👤 普通用户"]}}
[D 2025-07-30 11:14:48,339 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"9e455fe4-f404-4098-b8e9-fda75a2b2c7f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"82406421-5605-4d41-8da3-a5c588c6f9cd","pageId":3}}
[D 2025-07-30 11:14:48,341 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"9e455fe4-f404-4098-b8e9-fda75a2b2c7f","result":{"properties":["🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:14:48,342 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"578997b2-c53d-4105-ae41-34db0c2ba0a5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a044fd6-f930-4b40-8cf5-ca8672b7fd52","pageId":3}}
[D 2025-07-30 11:14:48,344 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"578997b2-c53d-4105-ae41-34db0c2ba0a5","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,344 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"24cc2218-70e9-42a6-90f6-4a9f640971d3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"7183cb63-77b4-4ff3-abc3-be780072ca0c","pageId":3}}
[D 2025-07-30 11:14:48,348 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"24cc2218-70e9-42a6-90f6-4a9f640971d3","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,349 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1659660c-c6c8-4c54-beae-e184bdab2bd2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"041d2d1c-202f-4dc8-b7cf-0f9fba0a2244","pageId":3}}
[D 2025-07-30 11:14:48,352 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1659660c-c6c8-4c54-beae-e184bdab2bd2","result":{"properties":["留言\n用户关联\n我的菜品\n通知中心\n我的订单"]}}
[D 2025-07-30 11:14:48,353 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e6b38cf2-1625-4650-ab01-0127a2378c2e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 11:14:48,355 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e6b38cf2-1625-4650-ab01-0127a2378c2e","result":{"properties":["留言"]}}
[D 2025-07-30 11:14:48,357 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"066231df-5e6f-4578-b807-e6c122ab623c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"50ab7522-3a54-468c-a4a1-c8a2884f9aa3","pageId":3}}
[D 2025-07-30 11:14:48,418 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"066231df-5e6f-4578-b807-e6c122ab623c","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,419 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f2c622ff-9308-4714-9c73-3b8b6ee4a14b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9913e4cd-4ade-46b7-8dca-1337338842f7","pageId":3}}
[D 2025-07-30 11:14:48,421 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f2c622ff-9308-4714-9c73-3b8b6ee4a14b","result":{"properties":["留言"]}}
[D 2025-07-30 11:14:48,422 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"94cf5785-a333-4685-ab38-9a2bd808be3b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:14:48,436 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"94cf5785-a333-4685-ab38-9a2bd808be3b","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:14:48,437 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"36bac293-fb47-4cad-8670-98318cd52506","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5690ad5d-f33c-4e5b-a4fa-01ebf8f9def4","pageId":3}}
[D 2025-07-30 11:14:48,440 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"36bac293-fb47-4cad-8670-98318cd52506","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,441 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c254c72d-8c15-4e15-85ab-f7f2458e55fe","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5897e5a6-69bc-4259-9ca5-0a9906119a7a","pageId":3}}
[D 2025-07-30 11:14:48,443 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c254c72d-8c15-4e15-85ab-f7f2458e55fe","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:14:48,444 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ae931ff1-332b-49c3-b7f4-99f0f4516d8d","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 11:14:48,446 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ae931ff1-332b-49c3-b7f4-99f0f4516d8d","result":{"properties":["我的菜品"]}}
[D 2025-07-30 11:14:48,446 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6fed8d02-51f6-4c6a-9f80-9ffab03b849e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"801572f6-2bd9-49cd-98cc-53f4837aa625","pageId":3}}
[D 2025-07-30 11:14:48,450 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6fed8d02-51f6-4c6a-9f80-9ffab03b849e","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,451 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f8106377-324a-4381-9453-fc81ba5f14b6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2cd57840-f06d-4044-a0d5-7ae0f6c222df","pageId":3}}
[D 2025-07-30 11:14:48,453 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f8106377-324a-4381-9453-fc81ba5f14b6","result":{"properties":["我的菜品"]}}
[D 2025-07-30 11:14:48,454 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"28fda8fe-57e8-4275-8ed3-96ec1a0f7752","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 11:14:48,456 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"28fda8fe-57e8-4275-8ed3-96ec1a0f7752","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:14:48,457 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"51c88c6d-5fcc-4a9d-9350-7a5caac99029","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"405df110-91a3-425f-8ea3-5b8e5b65e444","pageId":3}}
[D 2025-07-30 11:14:48,459 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"51c88c6d-5fcc-4a9d-9350-7a5caac99029","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:14:48,459 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4d9cfb4d-b2bc-4574-9cbc-b286092bd197","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"91e3160e-5195-4bc4-b35a-433a23689638","pageId":3}}
[D 2025-07-30 11:14:48,461 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4d9cfb4d-b2bc-4574-9cbc-b286092bd197","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,462 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d21454ea-2b32-4dc1-9d95-f4e689da691e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49001f66-e99d-4c94-ab20-19a0fa927b27","pageId":3}}
[D 2025-07-30 11:14:48,464 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d21454ea-2b32-4dc1-9d95-f4e689da691e","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:14:48,466 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d2ddb553-7806-420f-9f33-9f8049633a09","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","pageId":3}}
[D 2025-07-30 11:14:48,470 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d2ddb553-7806-420f-9f33-9f8049633a09","result":{"properties":["我的订单"]}}
[D 2025-07-30 11:14:48,471 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"99e28b67-2454-463e-a88e-c8f98cf7874d","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"c25d1ce9-b27e-4092-8be5-11d9e8620182","pageId":3}}
[D 2025-07-30 11:14:48,473 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"99e28b67-2454-463e-a88e-c8f98cf7874d","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,473 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6357dedf-81b3-42f5-8cda-8265774bc384","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"47b46222-d3a6-474d-8a03-e5fc8ca0a224","pageId":3}}
[D 2025-07-30 11:14:48,475 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6357dedf-81b3-42f5-8cda-8265774bc384","result":{"properties":["我的订单"]}}
[D 2025-07-30 11:14:48,476 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"65aafaef-bd40-464e-b230-d0c8f7f3fb7f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"c82b007b-99b3-4813-9e35-22716ec83de0","pageId":3}}
[D 2025-07-30 11:14:48,478 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"65aafaef-bd40-464e-b230-d0c8f7f3fb7f","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:14:48,478 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"60c852d4-e3b2-4ee5-b057-994d70bda7b0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","pageId":3}}
[D 2025-07-30 11:14:48,480 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"60c852d4-e3b2-4ee5-b057-994d70bda7b0","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:14:48,481 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"28f2f7af-20a9-4543-9b88-86375c2dba69","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f5058c52-f8f0-49da-9888-727ef9ca9ecc","pageId":3}}
[D 2025-07-30 11:14:48,486 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"28f2f7af-20a9-4543-9b88-86375c2dba69","result":{"properties":[""]}}
[D 2025-07-30 11:14:48,486 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"491dc67e-8fa1-4db0-bf56-943e47483600","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6edc6b03-bc80-420f-9daf-c21f7f31eaeb","pageId":3}}
[D 2025-07-30 11:14:48,488 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"491dc67e-8fa1-4db0-bf56-943e47483600","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:14:48,489 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ede332ca-9afd-466a-84e8-abbe8be5aa10","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","pageId":3}}
[D 2025-07-30 11:14:48,491 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ede332ca-9afd-466a-84e8-abbe8be5aa10","result":{"styles":["auto"]}}
[D 2025-07-30 11:14:48,491 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d318ae69-d1ce-4c27-a7d1-e3d65a8a5648","method":"Element.tap","params":{"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","pageId":3}}
[D 2025-07-30 11:14:48,544 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d318ae69-d1ce-4c27-a7d1-e3d65a8a5648","result":{"pageX":188,"pageY":321,"clientX":188,"clientY":321}}
[D 2025-07-30 11:14:52,545 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b9769abf-cbd1-451d-ba7f-90f9d3961ed7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:14:52,547 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b9769abf-cbd1-451d-ba7f-90f9d3961ed7","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:14:52,547 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:14:52,547 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c4eca274-a343-40f9-ad5c-55067d6f0ba9","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:14:52,551 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c4eca274-a343-40f9-ad5c-55067d6f0ba9","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:14:52,551 minium page#716 _get_elements_by_css] try to get elements: view, text, button
[D 2025-07-30 11:14:52,552 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"12041d7d-a565-4369-aec7-c3ba1e9b3ec5","method":"Page.getElements","params":{"selector":"view, text, button","pageId":3}}
[D 2025-07-30 11:14:52,557 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"12041d7d-a565-4369-aec7-c3ba1e9b3ec5","result":{"elements":[{"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","tagName":"view"},{"elementId":"62d59b6d-1d04-4e5c-8dcb-d75d1f732104","tagName":"view"},{"elementId":"cca213e8-b08e-4419-9ce0-fc2085aae648","tagName":"view"},{"elementId":"d5c87356-e7c7-48dd-8a3c-fc78d291fd5d","tagName":"view"},{"elementId":"4c2a227c-5d1e-4831-937e-ce951978dfe7","tagName":"view"},{"elementId":"bfd2023f-cfaa-42dd-8cb4-61118a76f7ab","tagName":"view"},{"elementId":"a06aa7be-cd
[I 2025-07-30 11:14:52,558 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDC70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD810>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCECB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDEF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDBD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE670>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDB30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE0D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDD10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD450>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDDB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE3F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDE50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE350>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE2B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE490>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE5D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE710>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEA30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE8F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD590>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE990>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEAD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEB70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEC10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEE90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDA90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF070>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEFD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF250>]
[D 2025-07-30 11:14:52,559 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"52b71f42-c149-4681-86b2-96413acfd5f4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","pageId":3}}
[D 2025-07-30 11:14:52,562 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"52b71f42-c149-4681-86b2-96413acfd5f4","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b\n留言\n用户关联\n我的菜品\n通知中心\n我的订单\n退出登录"]}}
[D 2025-07-30 11:14:52,563 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"372af72b-ce06-4b62-8b4a-226dfa06bf02","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"62d59b6d-1d04-4e5c-8dcb-d75d1f732104","pageId":3}}
[D 2025-07-30 11:14:52,569 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"372af72b-ce06-4b62-8b4a-226dfa06bf02","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:14:52,569 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d3fd74f5-ba70-49ab-a949-9ae44957e1cb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"cca213e8-b08e-4419-9ce0-fc2085aae648","pageId":3}}
[D 2025-07-30 11:14:52,573 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d3fd74f5-ba70-49ab-a949-9ae44957e1cb","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,574 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"56c96e40-148a-416e-bb45-ed4ac851c48f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d5c87356-e7c7-48dd-8a3c-fc78d291fd5d","pageId":3}}
[D 2025-07-30 11:14:52,576 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"56c96e40-148a-416e-bb45-ed4ac851c48f","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:14:52,577 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"17287454-fbcd-4066-bd6a-050643fd592d","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"4c2a227c-5d1e-4831-937e-ce951978dfe7","pageId":3}}
[D 2025-07-30 11:14:52,579 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"17287454-fbcd-4066-bd6a-050643fd592d","result":{"properties":["李虎"]}}
[D 2025-07-30 11:14:52,580 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"eeed3e5f-8060-4e7c-9a34-3783a57f04b0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"bfd2023f-cfaa-42dd-8cb4-61118a76f7ab","pageId":3}}
[D 2025-07-30 11:14:52,590 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"eeed3e5f-8060-4e7c-9a34-3783a57f04b0","result":{"properties":["📱 182****5876"]}}
[D 2025-07-30 11:14:52,590 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"3f4eb61b-c23a-4445-8d3e-7096212e4f52","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a06aa7be-cd94-42f9-85d0-d4f8c18ee1a0","pageId":3}}
[D 2025-07-30 11:14:52,594 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"3f4eb61b-c23a-4445-8d3e-7096212e4f52","result":{"properties":["👤 普通用户"]}}
[D 2025-07-30 11:14:52,595 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"bbce1260-9c2e-4533-819f-d6dd5741b7b0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"82406421-5605-4d41-8da3-a5c588c6f9cd","pageId":3}}
[D 2025-07-30 11:14:52,599 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"bbce1260-9c2e-4533-819f-d6dd5741b7b0","result":{"properties":["🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:14:52,602 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ef51f6f4-999c-4f2b-8323-886be3252e79","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a044fd6-f930-4b40-8cf5-ca8672b7fd52","pageId":3}}
[D 2025-07-30 11:14:52,606 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ef51f6f4-999c-4f2b-8323-886be3252e79","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,607 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b664affd-3c5a-45b0-8328-49a90a28ff15","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"7183cb63-77b4-4ff3-abc3-be780072ca0c","pageId":3}}
[D 2025-07-30 11:14:52,609 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b664affd-3c5a-45b0-8328-49a90a28ff15","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,610 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2f8c7591-9e5f-4222-baf3-2c4f4e2ac6b4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"041d2d1c-202f-4dc8-b7cf-0f9fba0a2244","pageId":3}}
[D 2025-07-30 11:14:52,612 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2f8c7591-9e5f-4222-baf3-2c4f4e2ac6b4","result":{"properties":["留言\n用户关联\n我的菜品\n通知中心\n我的订单"]}}
[D 2025-07-30 11:14:52,613 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"2a50a17f-55ab-485d-bbfa-b65f352ef70a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 11:14:52,622 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"2a50a17f-55ab-485d-bbfa-b65f352ef70a","result":{"properties":["留言"]}}
[D 2025-07-30 11:14:52,623 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1ccace41-dbff-4ea9-8436-7bab0615e924","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"50ab7522-3a54-468c-a4a1-c8a2884f9aa3","pageId":3}}
[D 2025-07-30 11:14:52,626 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1ccace41-dbff-4ea9-8436-7bab0615e924","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,627 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"28eddb4e-583d-4ec5-ab28-198e41694214","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9913e4cd-4ade-46b7-8dca-1337338842f7","pageId":3}}
[D 2025-07-30 11:14:52,629 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"28eddb4e-583d-4ec5-ab28-198e41694214","result":{"properties":["留言"]}}
[D 2025-07-30 11:14:52,629 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"dd6148f9-d62f-49a3-ae5b-b37dbcd1e886","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:14:52,634 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"dd6148f9-d62f-49a3-ae5b-b37dbcd1e886","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:14:52,635 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"0af66fad-3f28-47d5-a649-5ba644ee2d15","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5690ad5d-f33c-4e5b-a4fa-01ebf8f9def4","pageId":3}}
[D 2025-07-30 11:14:52,639 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"0af66fad-3f28-47d5-a649-5ba644ee2d15","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,639 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"30096b14-64f7-4aa6-afe9-8d865a4331d3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5897e5a6-69bc-4259-9ca5-0a9906119a7a","pageId":3}}
[D 2025-07-30 11:14:52,642 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"30096b14-64f7-4aa6-afe9-8d865a4331d3","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:14:52,642 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f2e1a860-8f22-4376-b6b3-540b5c7c46d8","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 11:14:52,645 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f2e1a860-8f22-4376-b6b3-540b5c7c46d8","result":{"properties":["我的菜品"]}}
[D 2025-07-30 11:14:52,645 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"73a12e44-087d-444a-9eb6-1c75c9ec3c9a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"801572f6-2bd9-49cd-98cc-53f4837aa625","pageId":3}}
[D 2025-07-30 11:14:52,648 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"73a12e44-087d-444a-9eb6-1c75c9ec3c9a","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,650 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1a4c376b-f6a3-4b92-887e-ca22f4f2a10b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2cd57840-f06d-4044-a0d5-7ae0f6c222df","pageId":3}}
[D 2025-07-30 11:14:52,654 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1a4c376b-f6a3-4b92-887e-ca22f4f2a10b","result":{"properties":["我的菜品"]}}
[D 2025-07-30 11:14:52,655 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1e049a47-8683-4072-adf7-8e46806d45a7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 11:14:52,657 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1e049a47-8683-4072-adf7-8e46806d45a7","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:14:52,658 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"99694af9-a133-4d2f-8428-bffb5b3f72b0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"405df110-91a3-425f-8ea3-5b8e5b65e444","pageId":3}}
[D 2025-07-30 11:14:52,660 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"99694af9-a133-4d2f-8428-bffb5b3f72b0","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:14:52,660 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"e1bea9b8-e743-4d1f-b128-84dac028558b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"91e3160e-5195-4bc4-b35a-433a23689638","pageId":3}}
[D 2025-07-30 11:14:52,662 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"e1bea9b8-e743-4d1f-b128-84dac028558b","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,663 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"03cf23ab-d31e-41fd-9b0c-fcb1c04ad170","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49001f66-e99d-4c94-ab20-19a0fa927b27","pageId":3}}
[D 2025-07-30 11:14:52,666 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"03cf23ab-d31e-41fd-9b0c-fcb1c04ad170","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:14:52,667 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"975f9ebb-22c8-4d46-9a84-37db2b836d5c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","pageId":3}}
[D 2025-07-30 11:14:52,671 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"975f9ebb-22c8-4d46-9a84-37db2b836d5c","result":{"properties":["我的订单"]}}
[D 2025-07-30 11:14:52,671 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ff8c2b0d-88f3-45a1-8c0a-aebe7a9f80c0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"c25d1ce9-b27e-4092-8be5-11d9e8620182","pageId":3}}
[D 2025-07-30 11:14:52,674 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ff8c2b0d-88f3-45a1-8c0a-aebe7a9f80c0","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,674 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"be50106e-6340-4753-bc45-9d8c0b5bcca5","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"47b46222-d3a6-474d-8a03-e5fc8ca0a224","pageId":3}}
[D 2025-07-30 11:14:52,676 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"be50106e-6340-4753-bc45-9d8c0b5bcca5","result":{"properties":["我的订单"]}}
[D 2025-07-30 11:14:52,677 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"43d1a63f-b842-4ac4-ac86-5edec169b876","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"c82b007b-99b3-4813-9e35-22716ec83de0","pageId":3}}
[D 2025-07-30 11:14:52,679 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"43d1a63f-b842-4ac4-ac86-5edec169b876","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:14:52,680 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d6f90863-d512-406f-9586-0e6a4bfc807a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","pageId":3}}
[D 2025-07-30 11:14:52,684 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d6f90863-d512-406f-9586-0e6a4bfc807a","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:14:52,685 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"1f290405-6cb9-4807-adc2-5de8f04a114e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f5058c52-f8f0-49da-9888-727ef9ca9ecc","pageId":3}}
[D 2025-07-30 11:14:52,688 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"1f290405-6cb9-4807-adc2-5de8f04a114e","result":{"properties":[""]}}
[D 2025-07-30 11:14:52,689 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"790102ed-27c2-4fc7-98d9-22265974cad4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6edc6b03-bc80-420f-9daf-c21f7f31eaeb","pageId":3}}
[D 2025-07-30 11:14:52,691 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"790102ed-27c2-4fc7-98d9-22265974cad4","result":{"properties":["退出登录"]}}
[I 2025-07-30 11:14:52,691 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:14:52,692 minium minitest#799 _miniTearDown] =========Current case Down: test_02_dish_management_deep_test=========
[I 2025-07-30 11:14:52,692 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:14:52,693 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"cc3c896e-09cd-40fd-9880-4a38429c7d67","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:14:52,754 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"cc3c896e-09cd-40fd-9880-4a38429c7d67","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:14:52,756 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:14:52,756 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 11:14:52,760 minium minitest#432 _miniSetUp] =========Current case: test_03_user_connection_enhanced_test=========
[I 2025-07-30 11:14:52,760 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.enhanced_functional_test, case info: EnhancedFunctionalTest.test_03_user_connection_enhanced_test
[I 2025-07-30 11:14:52,761 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:14:52,761 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:14:52,761 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:14:52,761 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:14:52,761 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:14:52,779 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:14:52,779 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"56504fba-bf54-47ce-88c9-9db23900e972","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:14:52,843 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"56504fba-bf54-47ce-88c9-9db23900e972","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:14:52,844 minium minitest#487 _miniSetUp] =========case: test_03_user_connection_enhanced_test start=========
[I 2025-07-30 11:14:53,845 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:14:53,845 minium.Conn1504 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"70c7e1ed-5930-4a3a-bd13-0bbd2c5378e3","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:14:53,847 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:14:53,851 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"70c7e1ed-5930-4a3a-bd13-0bbd2c5378e3","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:14:53,851 minium.Conn1504 connection#704 _handle_async_msg] received async msg: 70c7e1ed-5930-4a3a-bd13-0bbd2c5378e3
[D 2025-07-30 11:15:08,859 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d39163e1-200e-4f27-9ae5-fa9322db945c","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:15:08,860 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d39163e1-200e-4f27-9ae5-fa9322db945c","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:15:08,861 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:15:08,861 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"5404f772-0195-4754-a8fb-b6253fe003b9","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:15:08,863 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"5404f772-0195-4754-a8fb-b6253fe003b9","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:15:10,863 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"daa3d999-e64f-4545-8375-58c8d8441f47","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:15:10,867 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"daa3d999-e64f-4545-8375-58c8d8441f47","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:15:10,868 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:15:10,869 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"3f0acfe7-9564-4ece-af3c-dc6f57d1e80f","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:15:10,871 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"3f0acfe7-9564-4ece-af3c-dc6f57d1e80f","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:15:10,872 minium page#716 _get_elements_by_css] try to get elements: view, text, button
[D 2025-07-30 11:15:10,872 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a5c4619c-6f90-4110-82d1-907f1a7509ca","method":"Page.getElements","params":{"selector":"view, text, button","pageId":3}}
[D 2025-07-30 11:15:10,875 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a5c4619c-6f90-4110-82d1-907f1a7509ca","result":{"elements":[{"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","tagName":"view"},{"elementId":"62d59b6d-1d04-4e5c-8dcb-d75d1f732104","tagName":"view"},{"elementId":"cca213e8-b08e-4419-9ce0-fc2085aae648","tagName":"view"},{"elementId":"d5c87356-e7c7-48dd-8a3c-fc78d291fd5d","tagName":"view"},{"elementId":"4c2a227c-5d1e-4831-937e-ce951978dfe7","tagName":"view"},{"elementId":"bfd2023f-cfaa-42dd-8cb4-61118a76f7ab","tagName":"view"},{"elementId":"a06aa7be-cd
[I 2025-07-30 11:15:10,876 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE3F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE8F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE990>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCD590>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE850>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE670>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEAD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEB70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEC10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEE90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCDA90>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF070>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEFD0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCEF30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE210>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCE170>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF390>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF1B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF110>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF2F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF430>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF6B0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF890>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF930>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCF9D0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFA70>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFB10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFBB0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFC50>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFCF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000028213FCFD90>]
[D 2025-07-30 11:15:10,876 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"32d7839b-4ee9-403a-8fb5-4049ff194917","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","pageId":3}}
[D 2025-07-30 11:15:10,879 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"32d7839b-4ee9-403a-8fb5-4049ff194917","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b\n留言\n用户关联\n我的菜品\n通知中心\n我的订单\n退出登录"]}}
[D 2025-07-30 11:15:10,879 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b43003f9-9c0f-44cb-8094-40e7a2ba8034","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"62d59b6d-1d04-4e5c-8dcb-d75d1f732104","pageId":3}}
[D 2025-07-30 11:15:10,886 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b43003f9-9c0f-44cb-8094-40e7a2ba8034","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:15:10,886 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"81d208e2-e740-4835-9259-399a2681c87c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"cca213e8-b08e-4419-9ce0-fc2085aae648","pageId":3}}
[D 2025-07-30 11:15:10,890 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"81d208e2-e740-4835-9259-399a2681c87c","result":{"properties":[""]}}
[D 2025-07-30 11:15:10,890 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b6b885c0-ed1c-4225-8603-6524c673cc38","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"d5c87356-e7c7-48dd-8a3c-fc78d291fd5d","pageId":3}}
[D 2025-07-30 11:15:10,893 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b6b885c0-ed1c-4225-8603-6524c673cc38","result":{"properties":["李虎\n📱 182****5876\n👤 普通用户\n🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:15:10,894 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"54945f7f-f2ff-46f8-baaa-62fa3ef51c31","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"4c2a227c-5d1e-4831-937e-ce951978dfe7","pageId":3}}
[D 2025-07-30 11:15:10,902 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"54945f7f-f2ff-46f8-baaa-62fa3ef51c31","result":{"properties":["李虎"]}}
[D 2025-07-30 11:15:10,903 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f84b295e-f658-4a6f-9dfd-74401b5cffe6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"bfd2023f-cfaa-42dd-8cb4-61118a76f7ab","pageId":3}}
[D 2025-07-30 11:15:10,907 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f84b295e-f658-4a6f-9dfd-74401b5cffe6","result":{"properties":["📱 182****5876"]}}
[D 2025-07-30 11:15:10,907 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"39d6a1d4-de5b-4e21-a141-bf6e4ccf9084","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a06aa7be-cd94-42f9-85d0-d4f8c18ee1a0","pageId":3}}
[D 2025-07-30 11:15:10,910 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"39d6a1d4-de5b-4e21-a141-bf6e4ccf9084","result":{"properties":["👤 普通用户"]}}
[D 2025-07-30 11:15:10,910 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"ec31948b-b349-4b58-8195-e7efecac52a6","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"82406421-5605-4d41-8da3-a5c588c6f9cd","pageId":3}}
[D 2025-07-30 11:15:10,913 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"ec31948b-b349-4b58-8195-e7efecac52a6","result":{"properties":["🆔 ID: cmdo5jahi0000skd8xaghog3b"]}}
[D 2025-07-30 11:15:10,914 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7581609e-6649-4d76-a2aa-050d5cfe84e0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a044fd6-f930-4b40-8cf5-ca8672b7fd52","pageId":3}}
[D 2025-07-30 11:15:10,919 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7581609e-6649-4d76-a2aa-050d5cfe84e0","result":{"properties":[""]}}
[D 2025-07-30 11:15:10,919 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"476784da-e5dd-48a4-9cde-7fbba8bf8af4","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"7183cb63-77b4-4ff3-abc3-be780072ca0c","pageId":3}}
[D 2025-07-30 11:15:10,961 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"476784da-e5dd-48a4-9cde-7fbba8bf8af4","result":{"properties":[""]}}
[D 2025-07-30 11:15:10,961 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7285b73c-556d-46e3-ae26-b84882003570","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"041d2d1c-202f-4dc8-b7cf-0f9fba0a2244","pageId":3}}
[D 2025-07-30 11:15:10,964 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7285b73c-556d-46e3-ae26-b84882003570","result":{"properties":["留言\n用户关联\n我的菜品\n通知中心\n我的订单"]}}
[D 2025-07-30 11:15:10,964 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"7da17752-f1b4-4823-bfba-539fb23adf76","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 11:15:10,975 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"7da17752-f1b4-4823-bfba-539fb23adf76","result":{"properties":["留言"]}}
[D 2025-07-30 11:15:10,976 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"38331923-8339-4528-8a2d-34da66ce7569","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"50ab7522-3a54-468c-a4a1-c8a2884f9aa3","pageId":3}}
[D 2025-07-30 11:15:10,979 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"38331923-8339-4528-8a2d-34da66ce7569","result":{"properties":[""]}}
[D 2025-07-30 11:15:10,979 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d9431e17-1ea5-4341-b34f-af5011822d83","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9913e4cd-4ade-46b7-8dca-1337338842f7","pageId":3}}
[D 2025-07-30 11:15:10,982 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d9431e17-1ea5-4341-b34f-af5011822d83","result":{"properties":["留言"]}}
[D 2025-07-30 11:15:10,983 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"31bdb02a-3c30-49a3-b0f8-fcc4b857f2e2","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:15:10,986 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"31bdb02a-3c30-49a3-b0f8-fcc4b857f2e2","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:15:10,986 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"951c6370-f45c-4730-9ea4-5f6136a78d7f","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5690ad5d-f33c-4e5b-a4fa-01ebf8f9def4","pageId":3}}
[D 2025-07-30 11:15:10,989 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"951c6370-f45c-4730-9ea4-5f6136a78d7f","result":{"properties":[""]}}
[D 2025-07-30 11:15:10,989 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"34b1ea18-5d2a-4b40-aa2a-f21d2c67927e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"5897e5a6-69bc-4259-9ca5-0a9906119a7a","pageId":3}}
[D 2025-07-30 11:15:10,991 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"34b1ea18-5d2a-4b40-aa2a-f21d2c67927e","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:15:10,992 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4ec44a4d-c41a-470d-86e9-0136f0ffd384","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"71327459-6375-4580-be0b-b903370354ec","pageId":3}}
[D 2025-07-30 11:15:10,994 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4ec44a4d-c41a-470d-86e9-0136f0ffd384","result":{"properties":["我的菜品"]}}
[D 2025-07-30 11:15:10,994 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"4f60c90f-0052-400a-829c-e523d8b22c1a","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"801572f6-2bd9-49cd-98cc-53f4837aa625","pageId":3}}
[D 2025-07-30 11:15:10,996 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"4f60c90f-0052-400a-829c-e523d8b22c1a","result":{"properties":[""]}}
[D 2025-07-30 11:15:10,997 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"6be43433-0a02-44e5-a3f3-67e8f69371eb","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2cd57840-f06d-4044-a0d5-7ae0f6c222df","pageId":3}}
[D 2025-07-30 11:15:11,001 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"6be43433-0a02-44e5-a3f3-67e8f69371eb","result":{"properties":["我的菜品"]}}
[D 2025-07-30 11:15:11,001 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"9e46b991-08ed-4119-b504-5a308b3eb8b9","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","pageId":3}}
[D 2025-07-30 11:15:11,004 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"9e46b991-08ed-4119-b504-5a308b3eb8b9","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:15:11,004 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c810e497-fe2a-44b6-9690-dee760f0a4e0","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"405df110-91a3-425f-8ea3-5b8e5b65e444","pageId":3}}
[D 2025-07-30 11:15:11,008 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c810e497-fe2a-44b6-9690-dee760f0a4e0","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:15:11,008 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"63a68883-ee36-440e-8b1e-3d79f280e3f3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"91e3160e-5195-4bc4-b35a-433a23689638","pageId":3}}
[D 2025-07-30 11:15:11,010 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"63a68883-ee36-440e-8b1e-3d79f280e3f3","result":{"properties":[""]}}
[D 2025-07-30 11:15:11,011 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"36505d15-111a-4bbf-9bee-88a4a4f03eb7","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49001f66-e99d-4c94-ab20-19a0fa927b27","pageId":3}}
[D 2025-07-30 11:15:11,012 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"36505d15-111a-4bbf-9bee-88a4a4f03eb7","result":{"properties":["通知中心"]}}
[D 2025-07-30 11:15:11,013 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"d4764f59-0049-4d3d-bbe7-8d560df12a09","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","pageId":3}}
[D 2025-07-30 11:15:11,021 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"d4764f59-0049-4d3d-bbe7-8d560df12a09","result":{"properties":["我的订单"]}}
[D 2025-07-30 11:15:11,021 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"b9a45cfd-076a-46ef-abbb-42a693162d3c","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"c25d1ce9-b27e-4092-8be5-11d9e8620182","pageId":3}}
[D 2025-07-30 11:15:11,023 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"b9a45cfd-076a-46ef-abbb-42a693162d3c","result":{"properties":[""]}}
[D 2025-07-30 11:15:11,023 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"9a6db1e9-e59d-4555-8c62-6300d195e715","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"47b46222-d3a6-474d-8a03-e5fc8ca0a224","pageId":3}}
[D 2025-07-30 11:15:11,025 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"9a6db1e9-e59d-4555-8c62-6300d195e715","result":{"properties":["我的订单"]}}
[D 2025-07-30 11:15:11,026 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f4cdfeff-3b8d-4c7d-bda9-96cf4003e185","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"c82b007b-99b3-4813-9e35-22716ec83de0","pageId":3}}
[D 2025-07-30 11:15:11,028 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f4cdfeff-3b8d-4c7d-bda9-96cf4003e185","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:15:11,028 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"cf477812-21d0-49f7-9f32-d3f3a66a3934","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","pageId":3}}
[D 2025-07-30 11:15:11,030 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"cf477812-21d0-49f7-9f32-d3f3a66a3934","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:15:11,031 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"234fc9e3-e1a0-441c-bdbf-7619a300ab6b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"f5058c52-f8f0-49da-9888-727ef9ca9ecc","pageId":3}}
[D 2025-07-30 11:15:11,037 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"234fc9e3-e1a0-441c-bdbf-7619a300ab6b","result":{"properties":[""]}}
[D 2025-07-30 11:15:11,037 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"cbcc1816-b478-4a93-bb29-99ac825d1793","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"6edc6b03-bc80-420f-9daf-c21f7f31eaeb","pageId":3}}
[D 2025-07-30 11:15:11,039 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"cbcc1816-b478-4a93-bb29-99ac825d1793","result":{"properties":["退出登录"]}}
[D 2025-07-30 11:15:11,040 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"47a4363b-2a36-41a7-ab6d-4b55aab8a95a","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","pageId":3}}
[D 2025-07-30 11:15:11,042 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"47a4363b-2a36-41a7-ab6d-4b55aab8a95a","result":{"styles":["auto"]}}
[D 2025-07-30 11:15:11,042 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"a1b43738-e0a5-43a1-ab85-a30a605df060","method":"Element.tap","params":{"elementId":"b67df24a-ec00-459b-9f17-7f131400ce1b","pageId":3}}
[D 2025-07-30 11:15:11,097 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"a1b43738-e0a5-43a1-ab85-a30a605df060","result":{"pageX":188,"pageY":321,"clientX":188,"clientY":321}}
[D 2025-07-30 11:15:15,103 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"f15de7db-1499-492a-b9ed-89bb684926eb","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:15:15,108 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"f15de7db-1499-492a-b9ed-89bb684926eb","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:15:15,108 minium.App6208 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:15:15,109 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"c4a613b3-117e-4ce7-a933-0a298e9a32d2","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:15:15,112 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"c4a613b3-117e-4ce7-a933-0a298e9a32d2","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:15:15,112 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:15:15,113 minium minitest#799 _miniTearDown] =========Current case Down: test_03_user_connection_enhanced_test=========
[I 2025-07-30 11:15:15,113 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:15:15,114 minium.Conn1504 connection#427 _safely_send] SEND > [*************]{"id":"887afed4-7e49-4fe0-9501-bc4e85e7fdf2","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:15:15,203 minium.Conn1504 connection#660 __on_message] RECV < [*************]{"id":"887afed4-7e49-4fe0-9501-bc4e85e7fdf2","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:15:15,204 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:15:15,204 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 11:15:45,646 minium minitest#202 init_miniprogram] start init miniprogram
[I 2025-07-30 11:15:45,648 minium basenative#59 wrapper] call IdeNative.start_wechat
[I 2025-07-30 11:15:45,649 minium basenative#63 wrapper] call IdeNative.start_wechat end None
[D 2025-07-30 11:15:45,650 minium wx_minium#118 __init__] {'debug': False, 'base_dir': 'E:\\wx-nan\\testing', 'platform': 'ide', 'app': 'wx', 'debug_mode': 'debug', 'close_ide': False, 'auto_capture': 'auto', 'check_mp_foreground': True, 'auto_relaunch': False, 'device_desire': {}, 'account_info': {}, 'report_usage': True, 'remote_connect_timeout': 180, 'request_timeout': 60, 'use_push': True, 'full_reset': False, 'outputs': 'outputs', 'enable_app_log': True, 'enable_network_panel': False, 'project_path': 'E:/wx-nan', 'dev_tool_path': 'D:/微信web开发者工具/cli.bat', 'test_port': 25209, 'mock_native_modal': {'wx.showModal': {'confirm': True, 'cancel': False}, 'wx.showToast': {'duration': 1000}}, 'mock_request': [], 'auto_authorize': False, 'audits': None, 'teardown_snapshot': False, 'mock_images_dir': '', 'mock_images': {}, 'need_perf': False, 'appid': None, 'enable_h5': True, 'autofix': False, 'app_id': 'wx82283b353918af82', 'assert_capture': True, 'project_name': '楠楠家厨测试', 'test_timeout': 60, 'push_mini_program': True, 'framework_capture': True, 'error_capture': False, 'create_time': '**************'}
[I 2025-07-30 11:15:45,651 minium wx_minium#738 launch_dev_tool] Starting dev tool and launch MiniProgram project ...
[I 2025-07-30 11:15:45,652 minium minium_object#83 _do_shell] de shell: cli.bat auto --project "E:/wx-nan" --auto-port 25209
[I 2025-07-30 11:15:47,049 minium minium_object#101 _do_shell] err:
- initialize

✔ IDE server has started, listening on http://127.0.0.1:25209
- preparing
- Fetching AppID (wx82283b353918af82) permissions
✔ Using AppID: wx82283b353918af82
✖ Fetching AppID (wx82283b353918af82) detailed information

[I 2025-07-30 11:15:47,049 minium minium_object#102 _do_shell] out:
[error] {
  code: 10,
  message: 'Error: 错误 Error: Port 25209 is in use (code 10)Error: Port 25209 is in use\n' +
    '    at Object.exports.auto [as method] (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\1d3c44fb45826718b091c4f56f6d9ab0.js:2:1236)\n' +
    '    at processTicksAndRejections (node:internal/process/task_queues:96:5)\n' +
    '    at async C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3779\n' +
    '    at async m.<anonymous> (C:\\Users\\<USER>\\AppData\\Local\\微信开发者工具\\User Data\\97c2c3ed9e9b4a343745d4ac3603eef1\\WeappCode\\package.nw\\core.wxvpkg\\7a975443f52e91830ae1d65cdf8c86db.js:2:3012)'
}

[I 2025-07-30 11:15:47,050 minium wx_minium#835 connect_dev_tool] Trying to connect Dev tool ...
[I 2025-07-30 11:15:47,327 minium.Conn6128 connection#352 _connect] connect to WebChatTools successfully
[D 2025-07-30 11:15:47,328 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"b9c87c76-d53f-4289-8009-55c0f649bf9a","method":"Tool.getInfo","params":{}}
[D 2025-07-30 11:15:47,329 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"b9c87c76-d53f-4289-8009-55c0f649bf9a","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[D 2025-07-30 11:15:47,330 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"78004031-3c02-49ce-8ef3-f7c02c1e7344","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 11:15:47,336 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"78004031-3c02-49ce-8ef3-f7c02c1e7344","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[D 2025-07-30 11:15:47,336 minium.App0832 app#618 _evaluate_js] evaluate js file checkEnv [ALL]
[D 2025-07-30 11:15:47,337 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"697928f8-a4ba-44e7-9462-8234e9c20751","method":"App.callFunction","params":{"functionDeclaration":"function checkInject(){var _=!1;return global.__minium__?_=!0:(global.__minium__={},_=!1),{injected:_,isThirdApp:!!wx._MINI_WX_PROXY_}}","args":[]}}
[D 2025-07-30 11:15:47,340 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"697928f8-a4ba-44e7-9462-8234e9c20751","result":{"result":{"injected":true,"isThirdApp":false}}}
[D 2025-07-30 11:15:47,340 minium.Conn6128 connection#287 remove] remove key which is not in observers
[D 2025-07-30 11:15:47,340 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"f3eb4dfb-2e5f-4d63-b19f-c5ccb23b1425","method":"App.addBinding","params":{"name":"onAppRouteDone"}}
[D 2025-07-30 11:15:47,342 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"f3eb4dfb-2e5f-4d63-b19f-c5ccb23b1425","result":{}}
[D 2025-07-30 11:15:47,342 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"1ca7bce6-ce4d-4c7c-899b-2d61191399cb","method":"App.callFunction","params":{"functionDeclaration":"function () {\n    if (!global.__minium__.onAppRouteDone) {\n        wx.onAppRouteDone(function (options) {\n            var c = getCurrentPages().pop()\n            if (c && c.__wxWebviewId__ == options.webviewId) options.renderer = c.renderer\n            onAppRouteDone(options, Date.now())\n        })\n        global.__minium__.onAppRouteDone = true\n    }\n}","args":[]}}
[D 2025-07-30 11:15:47,344 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"1ca7bce6-ce4d-4c7c-899b-2d61191399cb","result":{}}
[D 2025-07-30 11:15:47,345 minium.App0832 app#618 _evaluate_js] evaluate js file utils [ALL]
[D 2025-07-30 11:15:47,345 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"3e368659-2cd9-4cb4-b5f6-db52b0c5a3c6","method":"App.callFunction","params":{"functionDeclaration":"function utils(){const e=new Map;function t(t,r,i){i||(i=e),i.has(t)||i.set(t,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:n,originWxMethodsGetterMap:o,originWxMethodsValMap:s}=i.get(t),l=Object.getOwnPropertyDescriptor(t,r);let u=!1;if(n[r])o[r]&&(u=!0);else{let e,i=!1;l?l.get?(u=!0,e=l.get):(i=!0,e=l.value):e=t[r],e&&(s[r]=i,o[r]=u,n[r]=e)}return u?n[r]():n[r]}global.__minium__.restoreMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);if(!n[r])return;Object.getOwnPropertyDescriptor(t,r)?o[r]?Object.defineProperty(t,r,{get:n[r]}):Object.defineProperty(t,r,{value:n[r]}):t[r]=n[r],delete n[r]},global.__minium__.replaceMethod=function(r,i,n,o){o||(o=e),o.has(r)||o.set(r,{originWxMethods:{},originWxMethodsGetterMap:{},originWxMethodsValMap:{}});const{originWxMethods:s}=o.get(r),l=Object.getOwnPropertyDescriptor(r,i);s[i]||t(r,i,o),l?Object.defineProperty(r,i,{get:()=>n}):r[i]=n},global.__minium__.getSrcMethod=function(t,r,i){if(i||(i=e),!i.has(t))return;const{originWxMethods:n,originWxMethodsGetterMap:o}=i.get(t);return o[r]?n[r]():n[r]},global.__minium__.setSrcMethod=t,global.__minium__.defineMethod=function(e,t,r){const i=Object.getOwnPropertyDescriptor(e,t);i?i.get?Object.defineProperty(e,t,{get:()=>r}):Object.defineProperty(e,t,{value:r}):e[t]=r};const r=new Map;class i{static isRuleMatched(e,t){if(typeof e!=typeof t)return!1;switch(typeof e){case\"string\":if(e===t)return!0;\"*\"===e&&(e=\".*\");try{return!!new RegExp(e).exec(t)}catch(e){return!1}case\"number\":return e===t;case\"object\":if(e instanceof Array){if(e.length!=t.length)return!1;for(var r=0;r<e.length;r++)if(!i.isRuleMatched(e[r],t[r]))return!1;return!0}for(var n in e)if(\"success\"!=n&&\"fail\"!=n&&\"complete\"!=n&&\"_miniMockType\"!=n){if(void 0===t[n])return!1;if(!i.isRuleMatched(e[n],t[n]))return!1}return!0}return!1}constructor(e){r.has(e)||r.set(e,[]),this.rule_list=r.get(e)}length(){return this.rule_list.length}clean(){this.rule_list.splice(0)}push(e){this.rule_list.push(e)}unshift(e){this.rule_list.unshift(e)}pick(e){return this.rule_list.splice(e,1)[0]}search(...e){for(let o of e)for(var t=0,r=this.rule_list.length;t<r;t++){var n=this.rule_list[t];if(i.isRuleMatched(n,o))return 1===n._miniMockType&&(n=this.pick(t)),n}return null}}global.__minium__.MockRule=i,global.__minium__.bind=function(e,t,...r){return function(...i){return e.apply(t,r.concat(i))}}}","args":[]}}
[D 2025-07-30 11:15:47,348 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"3e368659-2cd9-4cb4-b5f6-db52b0c5a3c6","result":{}}
[D 2025-07-30 11:15:47,350 minium.App0832 app#618 _evaluate_js] evaluate js file hijackWxMethod [ALL]
[D 2025-07-30 11:15:47,352 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"05e17282-5930-4260-abe1-94dafc99e3cc","method":"App.callFunction","params":{"functionDeclaration":"function hijackWxMethod(){if(!global.__minium__.initedHijack){global.__minium__.initedHijack=!0;const o=wx._MINI_WX_PROXY_,a=new Map,l=new Map,r=new Map,u=new Map,_=new Map;function e(e,t,n,c){let i=n.get(e);return i[t]||(i[t]=c),i[t]}_.set(wx,{}),a.set(wx,{}),l.set(wx,{}),r.set(wx,{}),u.set(wx,{}),o&&(_.set(o,{}),a.set(o,{}),l.set(o,{}),r.set(o,{}),u.set(o,{})),global.__minium__.hijackTest=function(e){return{hijacked:_.get(e),hijack_before:a.get(e),hijack_after:l.get(e),hijack_callback:r.get(e),hijack_mock:u.get(e)}};const f={},s=new Set([\"request\",\"downloadFile\",\"uploadFile\",\"connectSocket\"]);function t(e){return!(!e.endsWith(\"Sync\")&&!e.startsWith(\"create\"))||s.has(e)}function n(n,c,i){if(!n)return;if(_.get(n)[c])return;let o=e(n,c,a,{}),m=e(n,c,l,{}),h=e(n,c,r,{});!u.has(n)&&u.set(n,{});let g=global.__minium__.getSrcMethod(n,c);if(!g&&(g=global.__minium__.setSrcMethod(n,c),void 0===g))return void(_.get(n)[c]=!0);global.__minium__.defineMethod(n,c,function(e,t){let n;return n=f[t]?f[t]:f[t]=[],function(...t){n.push(e);try{return e(...t)}finally{n.pop()}}}((function(...e){var i=e[0],a=!1,l=t(c),r=!l||function(e){return s.has(e)}(c);let _=u.get(n)[c];l||i&&(i.success||i.fail||i.complete)||(a=!0);if(f[c].length>1)return g(...e);const b=Math.random();var d;if(Object.values(o).forEach((t=>t(...e,b))),l){if(r&&i&&Object.keys(h).length){const e=i.complete;i.complete=t=>{Object.values(h).forEach((e=>e(t,b))),e&&e(t)}}d=(_||g)(...e)}else if(_||Object.keys(h).length)if(a||_)d=(_||g)(...e).then((function(e){return Object.values(h).forEach((t=>t(e,b))),i&&(i.success&&i.success(e),i.complete&&i.complete(e)),e})).catch((function(e){throw console.warn(`[minium] ${c} ${_?\"mockFn\":\"origin\"} catch ${e}`),Object.values(h).forEach((t=>t(e,b))),i&&(i.fail&&i.fail(e),i.complete&&i.complete(e)),e}));else{if(i){var p=i.complete;i.complete=function(e){Object.values(h).forEach((t=>t(e,b))),p&&p(e)}}d=g(...e)}else d=g(...e);return Object.values(m).forEach((e=>e(d,b))),d}),c)),_.get(n)[c]=!0}function c(e){return function(...t){try{return e.call(this,...t)}catch(e){return e}}}function i(e){return function(){return c(e).apply(this,Array.from(arguments).slice(0,-1))}}global.__minium__.setCall=function(e,t,{before:u,after:_,callback:f},s=!1){(o?[wx,o]:[wx]).forEach(((o,m)=>{n(o,e),u&&(a.get(o)[e][`${t}`]=s?c(u):i(u)),_&&(l.get(o)[e][`${t}`]=s?c(_):i(_)),f&&(r.get(o)[e][`${t}`]=s?c(f):i(f))}))},global.__minium__.delCall=function(e,t){(o?[wx,o]:[wx]).forEach((n=>{if(!_.get(n)[e])return;const c=a.get(n)[e],i=l.get(n)[e],o=r.get(n)[e];t?(delete c[`${t}`],delete i[`${t}`],delete o[`${t}`]):(Object.entries(c).forEach((([e])=>{delete c[e]})),Object.entries(i).forEach((([e])=>{delete i[e]})),Object.entries(o).forEach((([e])=>{delete o[e]})))}))},global.__minium__.canHook=!0,global.__minium__.setMock=function(e,c,i){(o?[wx,o]:[wx]).forEach((o=>{n(o,e);const a=global.__minium__.getSrcMethod(o,e),l={origin:global.__minium__.bind(a,o)};t(e)?u.get(o)[e]=function(){return c.apply(l,Array.from(arguments).concat(i))}:u.get(o)[e]=function(){return new Promise((e=>{e(c.apply(l,Array.from(arguments).concat(i)))}))}}))},global.__minium__.delMock=function(e){(o?[wx,o]:[wx]).forEach((t=>{u.get(t)[e]=void 0}))},global.__minium__.canMock=!0}}","args":[]}}
[D 2025-07-30 11:15:47,354 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"05e17282-5930-4260-abe1-94dafc99e3cc","result":{}}
[D 2025-07-30 11:15:47,354 minium.App0832 app#618 _evaluate_js] evaluate js file requestStack [ALL]
[D 2025-07-30 11:15:47,355 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"37df76a4-cf21-4399-b272-38848a881ccf","method":"App.callFunction","params":{"functionDeclaration":"function miniumRequestStack(){if(!global.__minium__.request_stack){const e=global.__minium__.request_stack=new Map;global.__minium__.wait_util=function(t=0,a=20){var r=(getCurrentPages().pop()||{}).__wxWebviewId__;return new Promise((n=>{var s=2*a,i=setInterval((()=>{s--;var a=e.has(r)?e.get(r).size:0;console.debug(\"[minitest] current stack size: \",a),a<=t?(clearInterval(i),n(!0)):s<=0&&(clearInterval(i),console.warn(`${e.has(r)?Array.from(e.get(r)):\"empty stack\"}`),n(!1))}),500)}))};const t=new Map;return global.__minium__.setCall(\"request\",\"requestStack\",{before:function(a,r){if(\"https://weapp.tencent.com/jscov_driver/CollectCovTimer\"!=a.url){var n,s=(getCurrentPages().pop()||{}).__wxWebviewId__;if(s)e.has(s)?n=e.get(s):(n=new Map,e.set(s,n)),n.set(r,[Date.now(),a.url]),t.set(r,{pageId:s,requestMap:n})}},callback:function(a,r){if(!t.has(r))return;const{pageId:n,requestMap:s}=t.get(r);t.delete(r),s.delete(r),0===s.size&&e.delete(n)}},!0),!0}return!1}","args":[]}}
[D 2025-07-30 11:15:47,357 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"37df76a4-cf21-4399-b272-38848a881ccf","result":{"result":false}}
[D 2025-07-30 11:15:47,358 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"31cbaccd-2a1d-4ac8-9f39-76db381de674","method":"App.callFunction","params":{"functionDeclaration":"function(){return {\"pages\": __wxConfig.pages, \"tabBar\": __wxConfig.tabBar && __wxConfig.tabBar.list} }","args":[]}}
[D 2025-07-30 11:15:47,359 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"31cbaccd-2a1d-4ac8-9f39-76db381de674","result":{"result":{"pages":["pages/home/<USER>","pages/mine/index","pages/order/index","pages/statistics/index","pages/today_order/index","pages/login/index","pages/register/index","pages/message/index","pages/family_message/index","pages/notification_center/index","pages/history_menu/index","pages/detail/index","pages/user_connection/index","pages/connection_history/index","pages/user_profile/index","pages/recommended_menu/index","pages/order_list/index","pages/
[D 2025-07-30 11:15:47,360 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"3f5556ca-7f4c-4a53-9cc8-c92b4f4e029e","method":"App.callWxMethod","params":{"method":"getAccountInfoSync","args":[]}}
[D 2025-07-30 11:15:47,361 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"3f5556ca-7f4c-4a53-9cc8-c92b4f4e029e","result":{"result":{"miniProgram":{"appId":"wx82283b353918af82","envVersion":"develop","version":""}}}}
[D 2025-07-30 11:15:47,362 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"095643b3-ea7c-4d45-8b0c-1d5fac4b21e9","method":"App.addBinding","params":{"name":"navigateTo_before_global"}}
[D 2025-07-30 11:15:47,363 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"095643b3-ea7c-4d45-8b0c-1d5fac4b21e9","result":{}}
[D 2025-07-30 11:15:47,364 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"8ca9cea1-77f5-4c91-8a9d-1511ade7e3c3","method":"App.addBinding","params":{"name":"redirectTo_before_global"}}
[D 2025-07-30 11:15:47,367 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"8ca9cea1-77f5-4c91-8a9d-1511ade7e3c3","result":{}}
[D 2025-07-30 11:15:47,367 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"fb3c4346-9d41-4b3a-b9f9-c5255c424496","method":"App.addBinding","params":{"name":"switchTab_before_global"}}
[D 2025-07-30 11:15:47,369 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"fb3c4346-9d41-4b3a-b9f9-c5255c424496","result":{}}
[D 2025-07-30 11:15:47,370 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"16020b24-a168-409d-a737-ca3f759c3ea4","method":"App.addBinding","params":{"name":"navigateBack_before_global"}}
[D 2025-07-30 11:15:47,371 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"16020b24-a168-409d-a737-ca3f759c3ea4","result":{}}
[D 2025-07-30 11:15:47,372 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"2181a9aa-bff1-495d-9ae8-e98ff14e624f","method":"App.addBinding","params":{"name":"reLaunch_before_global"}}
[D 2025-07-30 11:15:47,373 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"2181a9aa-bff1-495d-9ae8-e98ff14e624f","result":{}}
[D 2025-07-30 11:15:47,374 minium.App0832 app#618 _evaluate_js] evaluate js file hookNavigation [ALL]
[D 2025-07-30 11:15:47,374 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"7998f7c6-6113-4611-8eb2-f93a7455f1bc","method":"App.callFunction","params":{"functionDeclaration":"function hookNavigation(){if(global.__minium__.navigationHooked)return!0;global.__minium__.navigationHooked=!0;const e=\"global\",a=Object.defineProperties;function o(e){switch(e){case\"navigateTo_before_global\":return\"function\"==typeof navigateTo_before_global?navigateTo_before_global:void 0;case\"redirectTo_before_global\":return\"function\"==typeof redirectTo_before_global?redirectTo_before_global:void 0;case\"switchTab_before_global\":return\"function\"==typeof switchTab_before_global?switchTab_before_global:void 0;case\"navigateBack_before_global\":return\"function\"==typeof navigateBack_before_global?navigateBack_before_global:void 0;case\"reLaunch_before_global\":return\"function\"==typeof reLaunch_before_global?reLaunch_before_global:void 0;default:return}}function t(e){const a=o(e);return a||function(){const a=o(e);return a&&a(...arguments)}}global.__minium__.getRealBindingMethod=o,global.__minium__.getBindingMethod=t;[\"navigateTo\",\"redirectTo\",\"switchTab\",\"navigateBack\",\"reLaunch\"].forEach((a=>{global.__minium__.setCall(a,e,{before:t(`${a}_before_${e}`)})})),wx.onAppRouteDone((function(){const o=getCurrentPages().pop();o&&function(o,r){if(!o||!0===o.__page_router_rewrited__)return;const n={navigateTo:o.navigateTo,redirectTo:o.redirectTo,switchTab:o.switchTab,navigateBack:o.navigateBack,reLaunch:o.reLaunch};a(o,{__page_router_rewrited__:{value:!0,writable:!0},navigateTo:{value(a={}){const i=t(`navigateTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.navigateTo.call(o,a)},writable:!0},redirectTo:{value(a={}){const i=t(`redirectTo_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.redirectTo.call(o,a)},writable:!0},switchTab:{value(a={}){const i=t(`switchTab_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.switchTab.call(o,a)},writable:!0},navigateBack:{value(a={}){const r=t(`navigateBack_before_${e}`);return r&&r(a),n.navigateBack.call(o,a)},writable:!0},reLaunch:{value(a={}){const i=t(`reLaunch_before_${e}`);return i&&i(Object.assign({oriPath:r},a)),n.reLaunch.call(o,a)},writable:!0}}),console.warn(\"hook pageRouter succ\")}(o.pageRouter,o.route||o.__route__)}))}","args":[]}}
[D 2025-07-30 11:15:47,376 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"7998f7c6-6113-4611-8eb2-f93a7455f1bc","result":{"result":true}}
[D 2025-07-30 11:15:47,377 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"5ca53bd9-8cde-47b8-9795-68a347d9ddaf","method":"App.addBinding","params":{"name":"showModal_before_1753845347377"}}
[D 2025-07-30 11:15:47,378 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"5ca53bd9-8cde-47b8-9795-68a347d9ddaf","result":{}}
[D 2025-07-30 11:15:47,378 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"3e1a3826-73c5-4153-ba54-abc60fed9fbf","method":"App.addBinding","params":{"name":"showModal_callback_1753845347377"}}
[D 2025-07-30 11:15:47,380 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"3e1a3826-73c5-4153-ba54-abc60fed9fbf","result":{}}
[D 2025-07-30 11:15:47,380 minium.App0832 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 11:15:47,381 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"61e3bc87-4344-43d3-939a-c5e4e3e5542c","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showModal_before_1753845347377,after:undefined,callback:showModal_callback_1753845347377},!0):global.__minium__.delCall(_,l)}","args":["showModal",1753845347377,true]}}
[D 2025-07-30 11:15:47,386 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"61e3bc87-4344-43d3-939a-c5e4e3e5542c","result":{}}
[D 2025-07-30 11:15:47,386 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"c3e92753-7e8b-4215-af55-56ebc780c9e2","method":"App.addBinding","params":{"name":"showToast_before_1753845347386"}}
[D 2025-07-30 11:15:47,387 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"c3e92753-7e8b-4215-af55-56ebc780c9e2","result":{}}
[D 2025-07-30 11:15:47,388 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"89b17410-426b-4b30-8b84-e64d87ed1510","method":"App.addBinding","params":{"name":"showToast_callback_1753845347386"}}
[D 2025-07-30 11:15:47,389 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"89b17410-426b-4b30-8b84-e64d87ed1510","result":{}}
[D 2025-07-30 11:15:47,389 minium.App0832 app#618 _evaluate_js] evaluate js file hookWxMethodWithId [ALL]
[D 2025-07-30 11:15:47,390 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"95c7b9fa-8c86-488a-a4bb-cbdb176a0d32","method":"App.callFunction","params":{"functionDeclaration":"function hookWxMethodWithId(_,l,o){if(!global.__minium__.canHook)throw new Error(\"hook\u65b9\u6cd5\u672a\u5b9e\u73b0\");o?global.__minium__.setCall(_,l,{before:showToast_before_1753845347386,after:undefined,callback:showToast_callback_1753845347386},!0):global.__minium__.delCall(_,l)}","args":["showToast",1753845347386,true]}}
[D 2025-07-30 11:15:47,392 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"95c7b9fa-8c86-488a-a4bb-cbdb176a0d32","result":{}}
[I 2025-07-30 11:15:47,392 minium wx_minium#893 _instantiate_app] new app instanst <minium.miniprogram.base_driver.app.App object at 0x000002667472A7B0>, whether should relaunch: True
[D 2025-07-30 11:15:47,393 minium.Conn6128 connection#287 remove] remove key which is not in observers
[D 2025-07-30 11:15:47,393 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"862a8ad4-6301-4bb2-8aaf-e59d40abc44b","method":"Tool.getInfo","params":{}}
[D 2025-07-30 11:15:47,394 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"862a8ad4-6301-4bb2-8aaf-e59d40abc44b","result":{"version":"1.06.2504010","SDKVersion":"3.8.12"}}
[I 2025-07-30 11:15:47,394 minium basenative#59 wrapper] call IdeNative.send
[D 2025-07-30 11:15:47,395 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"e034eb59-184f-4f4b-817c-f5f9995fde5f","method":"Tool.native","params":{"method":"clickCoverView","data":{"data":{"nodetype":"text","text":"\u540c\u610f"}}}}
[D 2025-07-30 11:15:47,395 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"e034eb59-184f-4f4b-817c-f5f9995fde5f","result":{"error":{"message":"clickCoverView failed"}}}
[W 2025-07-30 11:15:47,396 minium idenative#77 _send] handle clickCoverView error: clickCoverView failed
[I 2025-07-30 11:15:47,396 minium basenative#63 wrapper] call IdeNative.send end False
[D 2025-07-30 11:15:47,396 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"0768fc15-293e-426a-aa7b-8265d917bca8","method":"App.callWxMethod","params":{"method":"getPrivacySetting","args":[]}}
[D 2025-07-30 11:15:47,404 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"0768fc15-293e-426a-aa7b-8265d917bca8","result":{"result":{"needAuthorization":false,"privacyContractName":"《小楠N小程序隐私保护指引》","errMsg":"getPrivacySetting:ok"}}}
[D 2025-07-30 11:15:47,405 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"d924983f-cfc8-4a00-83fc-cbcfd663d05b","method":"App.enableLog","params":{}}
[D 2025-07-30 11:15:47,407 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"d924983f-cfc8-4a00-83fc-cbcfd663d05b","result":{}}
[D 2025-07-30 11:15:47,408 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"ceac2209-ab09-4ac7-86db-d386f3d40b23","method":"App.callFunction","params":{"functionDeclaration":"function getAppConfig(){if(0===arguments.length)return __wxConfig;{const n={};return Array.from(arguments).forEach((r=>{n[r]=__wxConfig[r]})),n}}","args":["accountInfo"]}}
[D 2025-07-30 11:15:47,409 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"ceac2209-ab09-4ac7-86db-d386f3d40b23","result":{"result":{"accountInfo":{"appId":"wx82283b353918af82","nickname":"小楠N","icon":"http://wx.qlogo.cn/mmhead/LIND77SSex9jJInQpdDsb0YD5EuVbkiaK0lFwrCJs3NyZjtp4MzibMw8IQvb1F38rm2UtykgmJniao/0"}}}}
[D 2025-07-30 11:15:47,410 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"e30caf6f-8bf5-4d67-bda7-278861450edc","method":"App.callWxMethod","params":{"method":"getSystemInfoSync","args":[]}}
[D 2025-07-30 11:15:47,413 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"e30caf6f-8bf5-4d67-bda7-278861450edc","result":{"result":{"brand":"devtools","model":"iPhone X","system":"iOS 10.0.1","platform":"devtools","benchmarkLevel":-1,"pixelRatio":3,"screenWidth":375,"screenHeight":812,"windowWidth":375,"windowHeight":642,"statusBarHeight":44,"safeArea":{"top":44,"left":0,"right":375,"bottom":778,"width":375,"height":734},"language":"zh_CN","version":"8.0.5","fontSizeSetting":16,"SDKVersion":"3.8.12","theme":"light","host":{"env":"WeChat"},"enableDebug":false,"mode":"defaul
[I 2025-07-30 11:15:47,414 minium minitest#218 init_miniprogram] end init miniprogram
[I 2025-07-30 11:15:47,858 minium minitest#432 _miniSetUp] =========Current case: test_01_user_registration_and_login=========
[I 2025-07-30 11:15:48,092 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.complete_functional_test, case info: CompleteFunctionalTest.test_01_user_registration_and_login
[I 2025-07-30 11:15:48,094 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:15:48,096 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:15:48,097 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:15:48,098 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:15:48,103 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:15:48,113 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:15:48,119 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"c760937b-3e11-47b5-b076-1265ee37aa62","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:15:48,197 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"c760937b-3e11-47b5-b076-1265ee37aa62","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:15:48,200 minium minitest#487 _miniSetUp] =========case: test_01_user_registration_and_login start=========
[I 2025-07-30 11:15:49,201 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 11:15:49,202 minium.App0832 app#891 navigate_to] NavigateTo: /pages/login/index
[D 2025-07-30 11:15:49,203 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"8e5c2931-3189-4136-b85c-7dbeeaf319b7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:15:49,204 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"8e5c2931-3189-4136-b85c-7dbeeaf319b7","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:15:49,205 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:15:49,206 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"a2292249-ef9e-4cc7-a890-af2a7f89c0b1","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:15:49,208 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"a2292249-ef9e-4cc7-a890-af2a7f89c0b1","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:15:49,208 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"4cc43246-95ef-4591-9247-c20554244c97","method":"App.callWxMethod","params":{"method":"navigateTo","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 11:15:49,210 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/login/index"}]}}
[D 2025-07-30 11:15:59,234 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"4cc43246-95ef-4591-9247-c20554244c97","error":{"message":"timeout"}}
[E 2025-07-30 11:15:59,235 minium.Conn6128 connection#668 __on_message] [4cc43246-95ef-4591-9247-c20554244c97]: timeout
[I 2025-07-30 11:15:59,236 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 4cc43246-95ef-4591-9247-c20554244c97
[W 2025-07-30 11:15:59,237 minium.App0832 app#1172 _change_route_async] 可能因频繁调用navigateTo导致timeout
[I 2025-07-30 11:16:14,252 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:16:14,252 minium minitest#799 _miniTearDown] =========Current case Down: test_01_user_registration_and_login=========
[I 2025-07-30 11:16:14,253 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:16:14,253 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"7c4dff0e-bd33-4d63-88d1-7bd85436d949","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:30,018 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"7c4dff0e-bd33-4d63-88d1-7bd85436d949","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzs3XlcE3f+P/BXEpCgCCgBIiIeWKgHAkq9peuBt67VWls81gO21UXrT9rVFqpfLbiyra5Fqm3BY7HaWpVaL0A8urZeFTlEa6H1Rg2XgIKEI8nvj0kmkwMI1OGI7+ejD5tMJjOfAPPK+/P5zCSCyqpqEEIIb4TN3QBCiJmjlCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL
[I 2025-07-30 11:16:30,023 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:16:30,026 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 11:16:30,043 minium minitest#432 _miniSetUp] =========Current case: test_02_user_connection_flow=========
[I 2025-07-30 11:16:30,044 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.complete_functional_test, case info: CompleteFunctionalTest.test_02_user_connection_flow
[I 2025-07-30 11:16:30,044 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:16:30,044 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:16:30,044 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:16:30,045 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:16:30,045 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:16:30,046 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:16:30,046 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"1791bd97-139c-4d7a-8689-69e6907b5c1d","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:30,122 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"1791bd97-139c-4d7a-8689-69e6907b5c1d","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzs3XlcE3f+P/BXEpCgCCgBIiIeWKgHAkq9peuBt67VWls81gO21UXrT9rVFqpfLbiyra5Fqm3BY7HaWpVaL0A8urZeFTlEa6H1Rg2XgIKEI8nvj0kmkwMI1OGI7+ejD5tMJjOfAPPK+/P5zCSCyqpqEEIIb4TN3QBCiJmjlCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL
[I 2025-07-30 11:16:30,123 minium minitest#487 _miniSetUp] =========case: test_02_user_connection_flow start=========
[I 2025-07-30 11:16:31,124 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:16:31,125 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"94684aa4-9888-497a-98a5-8222ab8f0523","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:31,129 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"94684aa4-9888-497a-98a5-8222ab8f0523","result":{"pageId":13,"path":"pages/login/index","query":{}}}
[D 2025-07-30 11:16:31,129 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:31,130 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"75f9a0be-f24c-4b1c-82ea-e7cec8f1ce6c","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:31,136 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"75f9a0be-f24c-4b1c-82ea-e7cec8f1ce6c","result":{"result":{"pageId":13,"path":"pages/login/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:31,137 minium page#716 _get_elements_by_css] try to get elements: .tab-item[data-type='password'], .password-tab
[D 2025-07-30 11:16:31,137 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"e8d4bd4b-676a-482a-b4fd-7d13e2809267","method":"Page.getElements","params":{"selector":".tab-item[data-type='password'], .password-tab","pageId":13}}
[D 2025-07-30 11:16:31,147 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"e8d4bd4b-676a-482a-b4fd-7d13e2809267","result":{"elements":[{"elementId":"b4d1adec-c590-4af3-ac51-085170c319d5","tagName":"view"}]}}
[I 2025-07-30 11:16:31,147 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000002667472BE00>]
[D 2025-07-30 11:16:31,147 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"feadc43a-45cf-4525-b648-f9024fc400b3","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"b4d1adec-c590-4af3-ac51-085170c319d5","pageId":13}}
[D 2025-07-30 11:16:31,162 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"feadc43a-45cf-4525-b648-f9024fc400b3","result":{"styles":["auto"]}}
[D 2025-07-30 11:16:31,162 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"2f1fdb32-8a11-486a-9043-a7cf2af073b5","method":"Element.tap","params":{"elementId":"b4d1adec-c590-4af3-ac51-085170c319d5","pageId":13}}
[D 2025-07-30 11:16:31,228 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"2f1fdb32-8a11-486a-9043-a7cf2af073b5","result":{"pageX":262.5,"pageY":170,"clientX":262.5,"clientY":170}}
[I 2025-07-30 11:16:33,230 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='用户名'], input[placeholder*='账号']
[D 2025-07-30 11:16:33,230 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"5b11ad51-0532-4bf7-8607-0ba6abf6f464","method":"Page.getElements","params":{"selector":"input[placeholder*='\u7528\u6237\u540d'], input[placeholder*='\u8d26\u53f7']","pageId":13}}
[D 2025-07-30 11:16:33,236 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"5b11ad51-0532-4bf7-8607-0ba6abf6f464","result":{"elements":[{"elementId":"6f043ae6-4d8f-4bd0-9614-17ec6d5e7df6","tagName":"input"}]}}
[I 2025-07-30 11:16:33,236 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.FormElement object at 0x000002667472B770>]
[I 2025-07-30 11:16:33,237 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='密码']
[D 2025-07-30 11:16:33,238 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"fee045f0-de31-4cfc-af6e-95901b603c45","method":"Page.getElements","params":{"selector":"input[placeholder*='\u5bc6\u7801']","pageId":13}}
[D 2025-07-30 11:16:33,241 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"fee045f0-de31-4cfc-af6e-95901b603c45","result":{"elements":[{"elementId":"e7f7a828-04c0-49bd-b776-50491c4f95e4","tagName":"input"}]}}
[I 2025-07-30 11:16:33,242 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.FormElement object at 0x0000026674761D10>]
[D 2025-07-30 11:16:33,242 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"fa4f7dcf-e64f-4a1c-b418-8e8654397e0a","method":"Element.callFunction","params":{"functionName":"input.input","args":["13800000001",false],"elementId":"6f043ae6-4d8f-4bd0-9614-17ec6d5e7df6","pageId":13}}
[D 2025-07-30 11:16:33,253 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"fa4f7dcf-e64f-4a1c-b418-8e8654397e0a","result":{}}
[D 2025-07-30 11:16:33,253 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"dbfcdb73-bf6e-452e-99b8-df48b9526ab9","method":"Element.callFunction","params":{"functionName":"input.input","args":["test123456",false],"elementId":"e7f7a828-04c0-49bd-b776-50491c4f95e4","pageId":13}}
[D 2025-07-30 11:16:33,259 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"dbfcdb73-bf6e-452e-99b8-df48b9526ab9","result":{}}
[I 2025-07-30 11:16:33,260 minium page#716 _get_elements_by_css] try to get elements: button[bindtap*='login'], .login-btn
[D 2025-07-30 11:16:33,260 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"1fce81a5-6382-415d-9d3d-fcccc5dde85b","method":"Page.getElements","params":{"selector":"button[bindtap*='login'], .login-btn","pageId":13}}
[D 2025-07-30 11:16:33,263 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"1fce81a5-6382-415d-9d3d-fcccc5dde85b","result":{"elements":[]}}
[W 2025-07-30 11:16:33,264 minium page#747 _get_elements_by_css] Could not found any element 'button[bindtap*='login'], .login-btn' you need
[D 2025-07-30 11:16:33,267 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"5fc528a1-b08c-49eb-89a0-505a03fd9c2e","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:16:33,273 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:16:33,509 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753845393505,"webviewId":3,"routeEventId":"3_1753845393292","renderer":"webview"},1753845393506]}}
[D 2025-07-30 11:16:33,509 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"5fc528a1-b08c-49eb-89a0-505a03fd9c2e","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:16:33,510 minium.App0832 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753845393505, 'webviewId': 3, 'routeEventId': '3_1753845393292', 'renderer': 'webview'}, 1753845393506]}
[I 2025-07-30 11:16:33,510 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 5fc528a1-b08c-49eb-89a0-505a03fd9c2e
[D 2025-07-30 11:16:35,511 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"0c5132ab-4ca9-404d-ad7b-689cf54df991","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:35,513 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"0c5132ab-4ca9-404d-ad7b-689cf54df991","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:16:35,513 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:35,514 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"f1e0c412-5885-4592-845f-59bc32037435","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:35,517 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"f1e0c412-5885-4592-845f-59bc32037435","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:35,517 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 11:16:35,517 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"04623392-715f-413e-9df1-d8788f966f50","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 11:16:35,521 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"04623392-715f-413e-9df1-d8788f966f50","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 11:16:35,521 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674761D10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674763110>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674723820>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000266747236F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000266746F3BF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000266747038A0>]
[D 2025-07-30 11:16:35,522 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"ca3b8720-debd-46cf-8ba9-8ee4c2cb9b02","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 11:16:35,524 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"ca3b8720-debd-46cf-8ba9-8ee4c2cb9b02","result":{"properties":["留言"]}}
[D 2025-07-30 11:16:35,525 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"9e93200d-d473-49bf-8f95-6db2a5ee010e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:16:35,528 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"9e93200d-d473-49bf-8f95-6db2a5ee010e","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:16:35,528 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"beb8bf14-a7ab-4a75-b0b3-a91d10f6a7c4","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:16:35,531 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"beb8bf14-a7ab-4a75-b0b3-a91d10f6a7c4","result":{"styles":["auto"]}}
[D 2025-07-30 11:16:35,533 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"32bef056-0604-4f23-9b34-4140ff9d3e05","method":"Element.tap","params":{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:16:35,591 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/user_connection/index"}]}}
[D 2025-07-30 11:16:35,628 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"32bef056-0604-4f23-9b34-4140ff9d3e05","result":{"pageX":187.5,"pageY":244,"clientX":187.5,"clientY":244}}
[D 2025-07-30 11:16:36,734 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,735 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,738 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,739 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,740 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,740 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,742 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,743 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"name\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,762 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,762 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,763 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,764 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,766 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,767 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,768 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:36,769 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.logAdded","params":{"type":"warn","args":["[Component] property \"color\" of \"miniprogram_npm/@vant/weapp/icon/index\" received type-uncompatible value: expected <String> but get null value. Use empty string instead."]}}
[D 2025-07-30 11:16:39,629 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"65554ad7-f077-4282-83b7-a4d27524f2dc","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:39,632 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"65554ad7-f077-4282-83b7-a4d27524f2dc","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:16:39,633 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:39,633 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"f4c2c78a-122f-4b81-b59d-4b45d99a78ee","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:39,635 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"f4c2c78a-122f-4b81-b59d-4b45d99a78ee","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:39,636 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='搜索'], .search-input
[D 2025-07-30 11:16:39,636 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"0032c29b-7b60-4d24-993a-53937526f68b","method":"Page.getElements","params":{"selector":"input[placeholder*='\u641c\u7d22'], .search-input","pageId":14}}
[D 2025-07-30 11:16:39,641 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"0032c29b-7b60-4d24-993a-53937526f68b","result":{"elements":[]}}
[W 2025-07-30 11:16:39,641 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='搜索'], .search-input' you need
[I 2025-07-30 11:16:39,642 minium page#716 _get_elements_by_css] try to get elements: .user-item, .user-card, .connection-item
[D 2025-07-30 11:16:39,642 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"4690ef42-66ea-4167-8cac-9b7142f3ac18","method":"Page.getElements","params":{"selector":".user-item, .user-card, .connection-item","pageId":14}}
[D 2025-07-30 11:16:39,646 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"4690ef42-66ea-4167-8cac-9b7142f3ac18","result":{"elements":[{"elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","tagName":"view"}]}}
[I 2025-07-30 11:16:39,647 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674703F00>]
[I 2025-07-30 11:16:39,647 minium element#521 _get_elements_by_css] try to get elements: button[bindtap*='connect'], .connect-btn
[D 2025-07-30 11:16:39,650 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"f946fe62-521d-482b-9225-37a210ebc6ba","method":"Element.getElements","params":{"selector":"button[bindtap*='connect'], .connect-btn","elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","pageId":14}}
[D 2025-07-30 11:16:39,655 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"f946fe62-521d-482b-9225-37a210ebc6ba","result":{"elements":[]}}
[D 2025-07-30 11:16:39,656 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"631ac730-34e6-4821-aff7-47d00f4259dc","method":"Element.getProperties","params":{"names":["node_id"],"elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","pageId":14}}
[D 2025-07-30 11:16:39,659 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"631ac730-34e6-4821-aff7-47d00f4259dc","result":{"properties":[null]}}
[D 2025-07-30 11:16:39,660 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"2e99b7ca-c91b-473c-a618-8911d49ee8c5","method":"Element.getAttributes","params":{"names":["node_id"],"elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","pageId":14}}
[D 2025-07-30 11:16:39,663 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"2e99b7ca-c91b-473c-a618-8911d49ee8c5","result":{"attributes":[null]}}
[W 2025-07-30 11:16:39,663 minium element#526 _get_elements_by_css] Could not found any element 'button[bindtap*='connect'], .connect-btn' you need
[D 2025-07-30 11:16:39,664 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:39,666 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"fbd11008-2761-44d5-8433-e0597251ea4e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[]}}
[D 2025-07-30 11:16:39,669 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"fbd11008-2761-44d5-8433-e0597251ea4e","result":{"result":[{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"},{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}]}}
[I 2025-07-30 11:16:39,669 minium.App0832 app#971 navigate_back] NavigateBack from:/pages/user_connection/index
[D 2025-07-30 11:16:39,669 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"a60d44c9-c900-428a-b9e5-9f1928d857c4","method":"App.callWxMethod","params":{"method":"navigateBack","args":[{"delta":1}]}}
[D 2025-07-30 11:16:39,671 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"navigateBack_before_global","args":[{"delta":1}]}}
[D 2025-07-30 11:16:39,676 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"a60d44c9-c900-428a-b9e5-9f1928d857c4","result":{"result":{}}}
[W 2025-07-30 11:16:44,687 minium.App0832 app#976 navigate_back] route has not change, may be navigate back fail
[D 2025-07-30 11:16:44,688 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"8c74e42c-9a9f-492d-9dff-69f367f86a70","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:44,689 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"8c74e42c-9a9f-492d-9dff-69f367f86a70","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:16:44,690 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:44,690 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"491896f2-9c47-48f8-b72f-0d520d8567ae","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:44,692 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"491896f2-9c47-48f8-b72f-0d520d8567ae","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:45,770 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:16:45,771 minium minitest#799 _miniTearDown] =========Current case Down: test_02_user_connection_flow=========
[I 2025-07-30 11:16:45,771 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:16:45,771 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"881233e0-b5e7-4d54-897c-6ec7fbf48533","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:45,829 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"881233e0-b5e7-4d54-897c-6ec7fbf48533","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:16:45,830 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:16:45,834 minium basenative#63 wrapper] call BaseNative.get_start_up end 
[I 2025-07-30 11:16:45,838 minium minitest#432 _miniSetUp] =========Current case: test_03_complete_order_flow_with_data=========
[I 2025-07-30 11:16:45,839 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.complete_functional_test, case info: CompleteFunctionalTest.test_03_complete_order_flow_with_data
[I 2025-07-30 11:16:45,839 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:16:45,839 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:16:45,840 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:16:45,840 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:16:45,840 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:16:45,867 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:16:45,867 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"5b0c52c7-a22c-43f2-9562-db292da623f7","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:45,954 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"5b0c52c7-a22c-43f2-9562-db292da623f7","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:16:45,955 minium minitest#487 _miniSetUp] =========case: test_03_complete_order_flow_with_data start=========
[I 2025-07-30 11:16:46,956 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:16:46,956 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"fd74858b-9900-47eb-808b-eaf418d5f27a","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:46,958 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"fd74858b-9900-47eb-808b-eaf418d5f27a","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:16:46,958 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:46,959 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"a5442f0e-413f-4b07-8b0d-77bdb332be0c","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:46,960 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"a5442f0e-413f-4b07-8b0d-77bdb332be0c","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 11:16:46,961 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"3221ddb1-6ab2-4a19-a017-abf05bace134","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:16:46,963 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:16:57,019 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"3221ddb1-6ab2-4a19-a017-abf05bace134","error":{"message":"timeout"}}
[E 2025-07-30 11:16:57,019 minium.Conn6128 connection#668 __on_message] [3221ddb1-6ab2-4a19-a017-abf05bace134]: timeout
[I 2025-07-30 11:16:57,019 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 3221ddb1-6ab2-4a19-a017-abf05bace134
[W 2025-07-30 11:16:57,020 minium.App0832 app#1172 _change_route_async] 可能因频繁调用switchTab导致timeout
[W 2025-07-30 11:17:12,033 minium.App0832 app#1180 _change_route_async] recall switchTab
[D 2025-07-30 11:17:12,033 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [*************]{"id":"290143ea-4e6c-43ac-ab67-94ee685f593f","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:17:12,035 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 11:17:12,039 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"290143ea-4e6c-43ac-ab67-94ee685f593f","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:17:12,040 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 290143ea-4e6c-43ac-ab67-94ee685f593f
[D 2025-07-30 11:17:27,044 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"45ba91fc-ad32-4788-8197-342e1a347191","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:17:27,046 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"45ba91fc-ad32-4788-8197-342e1a347191","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:17:27,046 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:17:27,047 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"36a7df39-ed33-4007-86b8-8c9c8afa6199","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:17:27,049 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"36a7df39-ed33-4007-86b8-8c9c8afa6199","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[W 2025-07-30 11:17:27,050 minium.App0832 app#1013 switch_tab] Switch tab(/pages/order/index) but(/pages/user_connection/index)
[D 2025-07-30 11:17:30,051 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"e5e1e2df-88cb-485f-99ff-c167cb1c9ab6","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:17:30,053 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"e5e1e2df-88cb-485f-99ff-c167cb1c9ab6","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:17:30,053 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:17:30,054 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"0c427915-366b-4bbc-a239-44737ec17e99","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:17:30,055 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"0c427915-366b-4bbc-a239-44737ec17e99","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:17:33,056 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item
[D 2025-07-30 11:17:33,057 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"33420e15-55ce-40f9-87ba-0df69024c91d","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item","pageId":14}}
[I 2025-07-30 11:18:33,073 minium page#716 _get_elements_by_css] try to get elements: .dish-card, .food-item, .menu-item
[D 2025-07-30 11:18:33,074 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"439b7a85-a622-453c-8544-2499a48aa9d0","method":"Page.getElements","params":{"selector":".dish-card, .food-item, .menu-item","pageId":14}}
[I 2025-07-30 11:19:33,086 minium page#716 _get_elements_by_css] try to get elements: .cart-btn, .basket-btn, .shopping-cart, button[bindtap*='cart']
[D 2025-07-30 11:19:33,087 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"9fb6735f-1055-4e92-92af-b4b057ea44b7","method":"Page.getElements","params":{"selector":".cart-btn, .basket-btn, .shopping-cart, button[bindtap*='cart']","pageId":14}}
[D 2025-07-30 11:20:35,096 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"4a813458-9e64-4e74-b6ae-b9b9ceb53790","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:20:35,101 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"4a813458-9e64-4e74-b6ae-b9b9ceb53790","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:20:35,101 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:20:35,102 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"00ac866c-c980-4026-a782-86500fc14839","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:20:35,105 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"00ac866c-c980-4026-a782-86500fc14839","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:20:35,106 minium page#716 _get_elements_by_css] try to get elements: .van-toast, .toast, .success-tip
[D 2025-07-30 11:20:35,106 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"5a00c03a-aa79-480a-bd04-38cd04ab696b","method":"Page.getElements","params":{"selector":".van-toast, .toast, .success-tip","pageId":14}}
[D 2025-07-30 11:20:53,287 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"navigateBack","timeStamp":1753845653271,"webviewId":3,"routeEventId":"3_1753845653045","renderer":"webview"},1753845653273]}}
[I 2025-07-30 11:20:53,289 minium.App0832 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'navigateBack', 'timeStamp': 1753845653271, 'webviewId': 3, 'routeEventId': '3_1753845653045', 'renderer': 'webview'}, 1753845653273]}
[I 2025-07-30 11:20:53,291 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:20:53,292 minium minitest#799 _miniTearDown] =========Current case Down: test_03_complete_order_flow_with_data=========
[I 2025-07-30 11:20:53,293 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:20:53,293 minium.Conn6128 connection#427 _safely_send] SEND > [*************]{"id":"9f9d0426-466c-401e-a3ff-b5c0f65ab857","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:20:53,343 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753845653340,"webviewId":2,"routeEventId":"2_1753845653279","renderer":"webview"},1753845653342]}}
[I 2025-07-30 11:20:53,347 minium.App0832 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753845653340, 'webviewId': 2, 'routeEventId': '2_1753845653279', 'renderer': 'webview'}, 1753845653342]}
[D 2025-07-30 11:20:53,422 minium.Conn6128 connection#660 __on_message] RECV < [*************]{"id":"9f9d0426-466c-401e-a3ff-b5c0f65ab857","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:20:53,423 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:20:53,424 minium basenative#63 wrapper] call BaseNative.get_start_up end 
