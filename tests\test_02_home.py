#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import unittest
from minium import minitest
from utils.base_test import BaseTest


class TestHome(BaseTest):
    """首页功能测试"""
    
    def setUp(self):
        super().setUp()
        # 确保用户已登录
        self.clear_app_data()
        login_success = self.login_with_account()
        if not login_success:
            self.fail("登录失败，无法进行首页测试")
    
    def test_01_home_page_elements(self):
        """测试首页基本元素"""
        print("🧪 测试首页基本元素")
        
        # 确保在首页
        self.switch_to_tab("/pages/home/<USER>")
        
        # 验证欢迎卡片
        self.assert_element_exists(".home-welcome-card")
        self.assert_page_contains_text("欢迎")
        
        # 验证用户信息
        welcome_card = self.assert_element_exists(".home-welcome-left")
        self.assert_element_exists(".home-welcome-img")
        
        # 验证通知轮播区域
        try:
            self.assert_element_exists(".notice-carousel")
            print("✅ 通知轮播区域存在")
        except:
            print("⚠️ 通知轮播区域未找到")
        
        # 验证今日菜单卡片
        self.assert_element_exists(".home-menu-card")
        self.assert_element_exists(".home-menu-header")
        
        # 验证菜单标题
        menu_title = self.page.get_element(".home-menu-title")
        if menu_title:
            title_text = menu_title.inner_text
            self.assertIn("菜单", title_text, "应该显示菜单相关标题")
        
        # 验证去点菜按钮
        self.assert_element_exists(".theme-link")
        
        self.take_screenshot("home_page_elements")
        print("✅ 首页基本元素验证完成")
    
    def test_02_menu_display(self):
        """测试菜单展示功能"""
        print("🧪 测试菜单展示功能")
        
        self.switch_to_tab("/pages/home/<USER>")
        
        # 等待菜单数据加载
        time.sleep(3)
        
        # 检查菜单列表
        try:
            menu_list = self.page.get_element(".home-menu-list")
            if menu_list:
                # 检查菜品项
                menu_items = self.page.get_elements(".menu-item")
                print(f"📋 找到 {len(menu_items)} 个菜品")
                
                if menu_items:
                    # 验证第一个菜品的元素
                    first_item = menu_items[0]
                    
                    # 检查菜品图片
                    item_image = first_item.get_element(".menu-item-image")
                    if item_image:
                        print("✅ 菜品图片存在")
                    
                    # 检查菜品名称
                    item_name = first_item.get_element(".menu-item-name")
                    if item_name:
                        print(f"📝 菜品名称: {item_name.inner_text}")
                
                else:
                    print("⚠️ 暂无菜品数据")
            else:
                print("⚠️ 菜单列表未找到")
        
        except Exception as e:
            print(f"⚠️ 菜单展示测试异常: {e}")
        
        self.take_screenshot("home_menu_display")
        print("✅ 菜单展示功能测试完成")
    
    def test_03_navigation_buttons(self):
        """测试首页导航按钮"""
        print("🧪 测试首页导航按钮")
        
        self.switch_to_tab("/pages/home/<USER>")
        
        # 测试去点菜按钮
        try:
            order_btn = self.page.get_element(".theme-link")
            if order_btn:
                order_btn.click()
                time.sleep(2)
                
                # 验证跳转到订餐页面
                current_page = self.app.get_current_page()
                if "order" in current_page.path:
                    print("✅ 去点菜按钮跳转成功")
                    self.take_screenshot("navigate_to_order")
                    
                    # 返回首页
                    self.switch_to_tab("/pages/home/<USER>")
                else:
                    print("⚠️ 去点菜按钮跳转失败")
        
        except Exception as e:
            print(f"⚠️ 去点菜按钮测试异常: {e}")
        
        # 测试菜品详情跳转
        try:
            menu_items = self.page.get_elements(".menu-item")
            if menu_items:
                first_item = menu_items[0]
                first_item.click()
                time.sleep(2)
                
                # 验证跳转到详情页
                current_page = self.app.get_current_page()
                if "detail" in current_page.path:
                    print("✅ 菜品详情跳转成功")
                    self.take_screenshot("navigate_to_detail")
                    
                    # 返回首页
                    self.go_back()
                else:
                    print("⚠️ 菜品详情跳转失败")
        
        except Exception as e:
            print(f"⚠️ 菜品详情跳转测试异常: {e}")
        
        print("✅ 首页导航按钮测试完成")
    
    def test_04_notice_carousel(self):
        """测试通知轮播功能"""
        print("🧪 测试通知轮播功能")
        
        self.switch_to_tab("/pages/home/<USER>")
        
        try:
            # 查找通知区域
            notice_area = self.page.get_element(".notice-carousel, .notice-banner")
            if notice_area:
                # 获取当前通知内容
                notice_text = notice_area.inner_text
                print(f"📢 当前通知: {notice_text}")
                
                # 等待轮播切换
                time.sleep(4)
                
                # 再次获取通知内容，检查是否有变化
                new_notice_text = notice_area.inner_text
                print(f"📢 轮播后通知: {new_notice_text}")
                
                # 点击通知区域
                notice_area.click()
                time.sleep(2)
                
                # 检查是否跳转到通知页面
                current_page = self.app.get_current_page()
                if "notification" in current_page.path or "message" in current_page.path:
                    print("✅ 通知点击跳转成功")
                    self.take_screenshot("notice_click_navigation")
                    self.go_back()
                else:
                    print("⚠️ 通知点击未跳转")
            
            else:
                print("⚠️ 通知轮播区域未找到")
        
        except Exception as e:
            print(f"⚠️ 通知轮播测试异常: {e}")
        
        self.take_screenshot("notice_carousel")
        print("✅ 通知轮播功能测试完成")
    
    def test_05_refresh_functionality(self):
        """测试首页刷新功能"""
        print("🧪 测试首页刷新功能")
        
        self.switch_to_tab("/pages/home/<USER>")
        
        try:
            # 模拟下拉刷新
            page_element = self.page.get_element("page")
            if page_element:
                # 获取页面初始状态
                initial_content = page_element.inner_text
                
                # 执行刷新操作（模拟下拉）
                self.app.evaluate("""
                    const page = getCurrentPages()[getCurrentPages().length - 1];
                    if (page.onPullDownRefresh) {
                        page.onPullDownRefresh();
                    }
                """)
                
                # 等待刷新完成
                time.sleep(3)
                
                # 检查页面是否有更新
                updated_content = page_element.inner_text
                print("✅ 页面刷新操作完成")
        
        except Exception as e:
            print(f"⚠️ 刷新功能测试异常: {e}")
        
        self.take_screenshot("home_refresh")
        print("✅ 首页刷新功能测试完成")
    
    def test_06_data_loading_states(self):
        """测试数据加载状态"""
        print("🧪 测试数据加载状态")
        
        # 清除缓存重新加载
        self.app.evaluate("wx.clearStorageSync()")
        self.switch_to_tab("/pages/home/<USER>")
        
        # 检查加载状态
        try:
            # 查找loading元素
            loading_elements = self.page.get_elements(".loading, .van-loading, .skeleton")
            if loading_elements:
                print("✅ 找到加载状态元素")
                
                # 等待加载完成
                time.sleep(5)
                
                # 验证加载完成后的状态
                self.wait_for_page_load()
                
                # 检查是否有错误状态
                error_elements = self.page.get_elements(".error, .empty, .network-error")
                if error_elements:
                    print("⚠️ 发现错误或空状态")
                else:
                    print("✅ 数据加载正常")
            
            else:
                print("⚠️ 未找到加载状态元素")
        
        except Exception as e:
            print(f"⚠️ 数据加载状态测试异常: {e}")
        
        self.take_screenshot("data_loading_states")
        print("✅ 数据加载状态测试完成")


if __name__ == '__main__':
    unittest.main()
