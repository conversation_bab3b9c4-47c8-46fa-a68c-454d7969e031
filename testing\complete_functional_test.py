#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
楠楠家厨小程序完整功能测试
包括账号创建、用户关联、完整业务流程测试
"""

import os
import sys
import time
import json
import unittest
import requests
from datetime import datetime

try:
    from minium import MiniTest
    print("✅ minium导入成功")
except ImportError as e:
    print(f"❌ minium导入失败: {e}")
    sys.exit(1)


class CompleteFunctionalTest(MiniTest):
    """完整功能测试"""
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.test_results = []
        cls.test_users = []
        cls.api_base = "http://8.148.231.104:3000/api"
        print("🔧 完整功能测试初始化")
        
        # 创建测试用户
        cls._create_test_users()
    
    @classmethod
    def _create_test_users(cls):
        """创建测试用户"""
        print("👥 创建测试用户...")
        
        # 测试用户数据
        test_users_data = [
            {
                "name": "测试用户A",
                "phone": "13800000001", 
                "password": "test123456"
            },
            {
                "name": "测试用户B",
                "phone": "13800000002",
                "password": "test123456"
            }
        ]
        
        for user_data in test_users_data:
            try:
                # 尝试注册用户
                response = requests.post(
                    f"{cls.api_base}/auth/register",
                    json=user_data,
                    timeout=10
                )
                
                if response.status_code == 201:
                    result = response.json()
                    user_info = {
                        "id": result["data"]["user"]["id"],
                        "name": user_data["name"],
                        "phone": user_data["phone"],
                        "password": user_data["password"],
                        "token": result["data"]["token"]
                    }
                    cls.test_users.append(user_info)
                    print(f"✅ 创建用户成功: {user_data['name']}")
                    
                elif response.status_code == 409:
                    # 用户已存在，尝试登录
                    login_response = requests.post(
                        f"{cls.api_base}/auth/login",
                        json={
                            "username": user_data["phone"],
                            "password": user_data["password"]
                        },
                        timeout=10
                    )
                    
                    if login_response.status_code == 200:
                        result = login_response.json()
                        user_info = {
                            "id": result["data"]["user"]["id"],
                            "name": user_data["name"],
                            "phone": user_data["phone"],
                            "password": user_data["password"],
                            "token": result["data"]["token"]
                        }
                        cls.test_users.append(user_info)
                        print(f"✅ 用户已存在，登录成功: {user_data['name']}")
                    else:
                        print(f"❌ 用户登录失败: {user_data['name']}")
                        
                else:
                    print(f"❌ 创建用户失败: {user_data['name']} - {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 创建用户异常: {user_data['name']} - {e}")
        
        print(f"📊 成功创建/获取 {len(cls.test_users)} 个测试用户")
    
    def setUp(self):
        super().setUp()
        self.app = self.mini.app
        print(f"🚀 开始测试: {self._testMethodName}")
        time.sleep(1)
    
    def tearDown(self):
        print(f"✅ 测试完成: {self._testMethodName}")
        super().tearDown()
    
    def log_result(self, test_name, success, details="", critical=False):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "critical": critical,
            "timestamp": datetime.now().isoformat()
        }
        self.__class__.test_results.append(result)
        
        status = "✅" if success else "❌"
        priority = "🔥" if critical else "📋"
        print(f"{status} {priority} {test_name}: {details}")
    
    def test_01_user_registration_and_login(self):
        """测试用户注册和登录功能"""
        print("🧪 测试用户注册和登录功能")
        
        try:
            # 导航到登录页面
            self.app.navigate_to("/pages/login/index")
            time.sleep(2)
            
            login_page = self.app.get_current_page()
            self.assertIn("login", login_page.path)
            
            # 测试注册页面跳转
            register_links = login_page.get_elements("text[bindtap*='register'], .register-link")
            if register_links:
                register_links[0].click()
                time.sleep(2)
                
                register_page = self.app.get_current_page()
                if "register" in register_page.path:
                    self.log_result("注册页面跳转", True, "成功跳转到注册页", critical=True)
                    
                    # 测试注册表单
                    self._test_registration_form(register_page)
                    
                    # 返回登录页面
                    self.app.navigate_back()
                    time.sleep(1)
                else:
                    self.log_result("注册页面跳转", False, "未跳转到注册页", critical=True)
            else:
                self.log_result("注册入口", False, "未找到注册入口", critical=True)
            
            # 测试登录功能
            self._test_login_functionality(login_page)
            
        except Exception as e:
            self.log_result("用户注册登录测试", False, f"测试异常: {e}", critical=True)
    
    def _test_registration_form(self, page):
        """测试注册表单"""
        try:
            # 检查表单字段
            form_fields = {
                "用户名输入": "input[placeholder*='用户名'], input[placeholder*='姓名']",
                "手机号输入": "input[placeholder*='手机'], input[placeholder*='电话']", 
                "密码输入": "input[placeholder*='密码']",
                "注册按钮": "button[bindtap*='register'], .register-btn"
            }
            
            for field_name, selector in form_fields.items():
                try:
                    element = page.get_element(selector)
                    if element:
                        self.log_result(f"注册表单-{field_name}", True, "字段存在", critical=True)
                    else:
                        self.log_result(f"注册表单-{field_name}", False, "字段不存在", critical=True)
                except:
                    self.log_result(f"注册表单-{field_name}", False, "查找失败", critical=True)
            
            # 尝试填写表单
            try:
                username_input = page.get_element("input[placeholder*='用户名'], input[placeholder*='姓名']")
                phone_input = page.get_element("input[placeholder*='手机'], input[placeholder*='电话']")
                password_input = page.get_element("input[placeholder*='密码']")
                
                if username_input and phone_input and password_input:
                    username_input.input("测试用户C")
                    phone_input.input("13800000003")
                    password_input.input("test123456")
                    
                    self.log_result("注册表单填写", True, "表单填写成功", critical=True)
                    
                    # 尝试提交注册
                    register_btn = page.get_element("button[bindtap*='register'], .register-btn")
                    if register_btn:
                        register_btn.click()
                        time.sleep(3)
                        self.log_result("注册提交", True, "注册提交完成", critical=True)
                else:
                    self.log_result("注册表单填写", False, "表单字段不完整", critical=True)
                    
            except Exception as e:
                self.log_result("注册表单操作", False, f"操作失败: {e}", critical=True)
                
        except Exception as e:
            self.log_result("注册表单测试", False, f"测试异常: {e}", critical=True)
    
    def _test_login_functionality(self, page):
        """测试登录功能"""
        try:
            # 切换到账号登录
            password_tabs = page.get_elements(".tab-item[data-type='password'], .password-tab")
            if password_tabs:
                password_tabs[0].click()
                time.sleep(1)
                self.log_result("登录方式切换", True, "切换到账号登录", critical=True)
            
            # 使用测试用户登录
            if self.test_users:
                test_user = self.test_users[0]
                
                username_input = page.get_element("input[placeholder*='用户名'], input[placeholder*='账号']")
                password_input = page.get_element("input[placeholder*='密码']")
                
                if username_input and password_input:
                    username_input.input(test_user["phone"])
                    password_input.input(test_user["password"])
                    
                    login_btn = page.get_element("button[bindtap*='login'], .login-btn")
                    if login_btn:
                        login_btn.click()
                        time.sleep(3)
                        
                        # 检查登录结果
                        current_page = self.app.get_current_page()
                        if "login" not in current_page.path:
                            self.log_result("账号登录", True, f"登录成功，跳转到: {current_page.path}", critical=True)
                        else:
                            self.log_result("账号登录", False, "登录失败，仍在登录页", critical=True)
                    else:
                        self.log_result("登录按钮", False, "未找到登录按钮", critical=True)
                else:
                    self.log_result("登录表单", False, "登录表单不完整", critical=True)
            else:
                self.log_result("测试用户", False, "没有可用的测试用户", critical=True)
                
        except Exception as e:
            self.log_result("登录功能测试", False, f"测试异常: {e}", critical=True)
    
    def test_02_user_connection_flow(self):
        """测试用户关联流程"""
        print("🧪 测试用户关联流程")
        
        if len(self.test_users) < 2:
            self.log_result("用户关联前置条件", False, "测试用户不足2个", critical=True)
            return
        
        try:
            # 确保已登录
            self._ensure_logged_in()
            
            # 导航到个人中心
            self.app.switch_tab("/pages/mine/index")
            time.sleep(2)
            
            mine_page = self.app.get_current_page()
            
            # 查找用户关联入口
            self._test_user_connection_entry(mine_page)
            
            # 测试用户搜索和关联
            self._test_user_search_and_connect()
            
        except Exception as e:
            self.log_result("用户关联流程测试", False, f"测试异常: {e}", critical=True)
    
    def _ensure_logged_in(self):
        """确保用户已登录"""
        try:
            current_page = self.app.get_current_page()
            if "login" in current_page.path:
                # 需要登录
                if self.test_users:
                    self._test_login_functionality(current_page)
        except Exception as e:
            print(f"⚠️ 登录检查异常: {e}")
    
    def _test_user_connection_entry(self, page):
        """测试用户关联入口"""
        try:
            connection_found = False
            clickable_elements = page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                try:
                    text = element.inner_text
                    if "用户关联" in text or "关联用户" in text:
                        connection_found = True
                        self.log_result("用户关联入口", True, f"找到入口: {text}", critical=True)
                        
                        element.click()
                        time.sleep(3)
                        
                        current_page = self.app.get_current_page()
                        if "connection" in current_page.path or "user_connection" in current_page.path:
                            self.log_result("用户关联页面", True, f"跳转到: {current_page.path}", critical=True)
                            
                            # 测试关联页面功能
                            self._test_connection_page_features(current_page)
                            
                            # 返回个人中心
                            self.app.navigate_back()
                            time.sleep(1)
                        else:
                            self.log_result("用户关联页面", False, "未跳转到关联页面", critical=True)
                        break
                except:
                    continue
            
            if not connection_found:
                self.log_result("用户关联入口", False, "未找到用户关联入口", critical=True)
                
        except Exception as e:
            self.log_result("用户关联入口测试", False, f"测试异常: {e}", critical=True)
    
    def _test_connection_page_features(self, page):
        """测试关联页面功能"""
        try:
            # 检查搜索功能
            search_inputs = page.get_elements("input[placeholder*='搜索'], .search-input")
            if search_inputs:
                self.log_result("用户搜索功能", True, "搜索框存在", critical=True)
                
                # 尝试搜索
                search_inputs[0].input("测试")
                time.sleep(2)
                self.log_result("用户搜索操作", True, "搜索操作完成", critical=True)
            else:
                self.log_result("用户搜索功能", False, "搜索框不存在", critical=True)
            
            # 检查用户列表
            user_items = page.get_elements(".user-item, .user-card, .connection-item")
            if user_items:
                self.log_result("可关联用户列表", True, f"找到{len(user_items)}个用户", critical=True)
                
                # 尝试点击第一个用户
                if len(user_items) > 0:
                    first_user = user_items[0]
                    
                    # 查找关联按钮
                    connect_btns = first_user.get_elements("button[bindtap*='connect'], .connect-btn")
                    if connect_btns:
                        connect_btns[0].click()
                        time.sleep(2)
                        self.log_result("发送关联申请", True, "关联申请发送成功", critical=True)
                    else:
                        self.log_result("关联按钮", False, "未找到关联按钮", critical=True)
            else:
                self.log_result("可关联用户列表", False, "未找到用户列表", critical=True)
                
        except Exception as e:
            self.log_result("关联页面功能测试", False, f"测试异常: {e}", critical=True)
    
    def _test_user_search_and_connect(self):
        """测试用户搜索和关联"""
        try:
            # 使用API直接测试关联功能
            if len(self.test_users) >= 2:
                user_a = self.test_users[0]
                user_b = self.test_users[1]
                
                # 用户A向用户B发送关联申请
                response = requests.post(
                    f"{self.api_base}/connections/request",
                    json={
                        "receiverId": user_b["id"],
                        "message": "测试关联申请"
                    },
                    headers={"Authorization": f"Bearer {user_a['token']}"},
                    timeout=10
                )
                
                if response.status_code == 201:
                    self.log_result("API关联申请", True, "关联申请发送成功", critical=True)
                    
                    # 用户B处理关联申请
                    connection_data = response.json()["data"]
                    connection_id = connection_data["id"]
                    
                    accept_response = requests.put(
                        f"{self.api_base}/connections/{connection_id}/respond",
                        json={"action": "accept"},
                        headers={"Authorization": f"Bearer {user_b['token']}"},
                        timeout=10
                    )
                    
                    if accept_response.status_code == 200:
                        self.log_result("API关联处理", True, "关联申请处理成功", critical=True)
                    else:
                        self.log_result("API关联处理", False, f"处理失败: {accept_response.status_code}", critical=True)
                        
                else:
                    self.log_result("API关联申请", False, f"申请失败: {response.status_code}", critical=True)
                    
        except Exception as e:
            self.log_result("用户搜索关联测试", False, f"测试异常: {e}", critical=True)
    
    def test_03_complete_order_flow_with_data(self):
        """测试完整订餐流程（包含数据）"""
        print("🧪 测试完整订餐流程（包含数据）")
        
        try:
            # 确保已登录
            self._ensure_logged_in()
            
            # 导航到订餐页面
            self.app.switch_tab("/pages/order/index")
            time.sleep(3)
            
            order_page = self.app.get_current_page()
            
            # 测试菜品数据加载
            self._test_dish_data_loading(order_page)
            
            # 测试添加菜品到购物车
            self._test_add_to_cart(order_page)
            
            # 测试购物车功能
            self._test_shopping_cart_functionality(order_page)
            
            # 测试订单创建
            self._test_order_creation()
            
        except Exception as e:
            self.log_result("完整订餐流程测试", False, f"测试异常: {e}", critical=True)
    
    def _test_dish_data_loading(self, page):
        """测试菜品数据加载"""
        try:
            # 等待数据加载
            time.sleep(3)
            
            # 检查菜品元素
            dish_elements = page.get_elements(".dish-card, .food-item, .menu-item")
            self.log_result("菜品数据加载", True, f"加载了{len(dish_elements)}个菜品", critical=True)
            
            if dish_elements:
                # 检查第一个菜品的详细信息
                first_dish = dish_elements[0]
                
                # 检查菜品名称
                name_elements = first_dish.get_elements(".dish-name, .food-name, .name")
                if name_elements:
                    dish_name = name_elements[0].inner_text
                    self.log_result("菜品名称显示", True, f"菜品名称: {dish_name}", critical=False)
                
                # 检查菜品图片
                image_elements = first_dish.get_elements("image, .dish-image")
                if image_elements:
                    self.log_result("菜品图片显示", True, "菜品图片存在", critical=False)
                
                # 检查添加按钮
                add_buttons = first_dish.get_elements("button[bindtap*='add'], .add-btn, .plus-btn")
                if add_buttons:
                    self.log_result("菜品添加按钮", True, "添加按钮存在", critical=True)
                else:
                    self.log_result("菜品添加按钮", False, "添加按钮不存在", critical=True)
            
        except Exception as e:
            self.log_result("菜品数据加载测试", False, f"测试异常: {e}", critical=True)
    
    def _test_add_to_cart(self, page):
        """测试添加到购物车"""
        try:
            dish_elements = page.get_elements(".dish-card, .food-item, .menu-item")
            
            if dish_elements:
                # 尝试添加第一个菜品
                first_dish = dish_elements[0]
                add_buttons = first_dish.get_elements("button[bindtap*='add'], .add-btn, .plus-btn")
                
                if add_buttons:
                    # 点击添加按钮
                    add_buttons[0].click()
                    time.sleep(1)
                    
                    # 再次点击增加数量
                    add_buttons[0].click()
                    time.sleep(1)
                    
                    self.log_result("添加菜品到购物车", True, "菜品添加成功", critical=True)
                    
                    # 检查数量显示
                    count_elements = first_dish.get_elements(".count, .quantity, .van-stepper__input")
                    if count_elements:
                        count = count_elements[0].inner_text or count_elements[0].value
                        self.log_result("菜品数量显示", True, f"数量: {count}", critical=True)
                else:
                    self.log_result("添加菜品到购物车", False, "未找到添加按钮", critical=True)
            else:
                self.log_result("菜品列表", False, "未找到菜品", critical=True)
                
        except Exception as e:
            self.log_result("添加到购物车测试", False, f"测试异常: {e}", critical=True)
    
    def _test_shopping_cart_functionality(self, page):
        """测试购物车功能"""
        try:
            # 查找购物车按钮
            cart_buttons = page.get_elements(".cart-btn, .basket-btn, .shopping-cart, button[bindtap*='cart']")
            
            if cart_buttons:
                self.log_result("购物车按钮", True, f"找到{len(cart_buttons)}个购物车按钮", critical=True)
                
                # 点击购物车
                cart_buttons[0].click()
                time.sleep(3)
                
                # 检查是否跳转到购物车页面
                current_page = self.app.get_current_page()
                if "today_order" in current_page.path or "cart" in current_page.path:
                    self.log_result("购物车页面跳转", True, f"跳转到: {current_page.path}", critical=True)
                    
                    # 测试购物车页面功能
                    self._test_cart_page_operations(current_page)
                    
                else:
                    self.log_result("购物车页面跳转", False, "未跳转到购物车页面", critical=True)
            else:
                self.log_result("购物车按钮", False, "未找到购物车按钮", critical=True)
                
        except Exception as e:
            self.log_result("购物车功能测试", False, f"测试异常: {e}", critical=True)
    
    def _test_cart_page_operations(self, page):
        """测试购物车页面操作"""
        try:
            # 检查购物车商品
            cart_items = page.get_elements(".cart-item, .order-item")
            self.log_result("购物车商品", True, f"购物车有{len(cart_items)}个商品", critical=True)
            
            if cart_items:
                # 测试数量调整
                quantity_controls = page.get_elements(".van-stepper, .quantity-control")
                if quantity_controls:
                    self.log_result("数量调整控件", True, f"找到{len(quantity_controls)}个数量控件", critical=True)
                
                # 测试删除商品
                delete_buttons = page.get_elements("button[bindtap*='delete'], .delete-btn")
                if delete_buttons:
                    self.log_result("删除商品按钮", True, "删除按钮存在", critical=True)
            
            # 检查提交订单按钮
            submit_buttons = page.get_elements("button[bindtap*='submit'], .submit-btn, .confirm-btn")
            if submit_buttons:
                self.log_result("提交订单按钮", True, "提交按钮存在", critical=True)
                
                # 尝试提交订单
                submit_buttons[0].click()
                time.sleep(3)
                self.log_result("订单提交操作", True, "订单提交完成", critical=True)
            else:
                self.log_result("提交订单按钮", False, "提交按钮不存在", critical=True)
                
        except Exception as e:
            self.log_result("购物车页面操作测试", False, f"测试异常: {e}", critical=True)
    
    def _test_order_creation(self):
        """测试订单创建"""
        try:
            # 检查是否跳转到订单页面或显示成功提示
            time.sleep(2)
            current_page = self.app.get_current_page()
            
            if "order" in current_page.path and "today_order" not in current_page.path:
                self.log_result("订单创建跳转", True, f"跳转到订单页面: {current_page.path}", critical=True)
            else:
                # 检查是否有成功提示
                toast_elements = current_page.get_elements(".van-toast, .toast, .success-tip")
                if toast_elements:
                    self.log_result("订单创建反馈", True, "显示了提交反馈", critical=True)
                else:
                    self.log_result("订单创建反馈", False, "无明确的提交反馈", critical=False)
            
        except Exception as e:
            self.log_result("订单创建测试", False, f"测试异常: {e}", critical=True)


def run_complete_test():
    """运行完整测试"""
    print("🚀 开始运行楠楠家厨小程序完整功能测试")
    print("=" * 70)
    print("📱 小程序AppID: wx82283b353918af82")
    print("🔌 微信开发者工具端口: 25209")
    print("🧪 测试内容: 账号创建、用户关联、完整业务流程")
    print("=" * 70)
    
    start_time = datetime.now()
    
    # 运行测试
    suite = unittest.TestLoader().loadTestsFromTestCase(CompleteFunctionalTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 获取详细测试结果
    detailed_results = CompleteFunctionalTest.test_results
    test_users = CompleteFunctionalTest.test_users
    
    # 统计结果
    total_functional = len(detailed_results)
    passed_functional = sum(1 for r in detailed_results if r['success'])
    failed_functional = total_functional - passed_functional
    critical_failed = len([r for r in detailed_results if not r['success'] and r.get('critical', False)])
    
    # 生成报告
    complete_report = {
        "test_time": start_time.isoformat(),
        "duration_seconds": duration,
        "test_users_created": len(test_users),
        "framework_tests": {
            "total": result.testsRun,
            "passed": result.testsRun - len(result.failures) - len(result.errors),
            "failed": len(result.failures),
            "errors": len(result.errors)
        },
        "functional_tests": {
            "total": total_functional,
            "passed": passed_functional,
            "failed": failed_functional,
            "critical_failed": critical_failed
        },
        "overall_success": result.wasSuccessful() and critical_failed == 0,
        "test_users": [{"name": u["name"], "phone": u["phone"]} for u in test_users],
        "detailed_results": detailed_results,
        "test_coverage": {
            "user_authentication": "用户注册登录系统",
            "user_connection": "用户关联系统",
            "order_flow": "完整订餐流程",
            "cart_functionality": "购物车功能",
            "data_integration": "数据集成测试"
        }
    }

    # 保存报告
    with open("complete_functional_test_report.json", 'w', encoding='utf-8') as f:
        json.dump(complete_report, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print("\n" + "=" * 70)
    print("🎯 完整功能测试总结")
    print("=" * 70)
    print(f"⏱️ 测试耗时: {duration:.2f} 秒")
    print(f"👥 创建测试用户: {len(test_users)} 个")
    print(f"📊 框架测试: {complete_report['framework_tests']['passed']}/{complete_report['framework_tests']['total']} 通过")
    print(f"🔧 功能测试: {passed_functional}/{total_functional} 通过")
    print(f"🔥 关键功能失败: {critical_failed} 个")
    
    if total_functional > 0:
        success_rate = passed_functional / total_functional * 100
        print(f"🎯 功能成功率: {success_rate:.1f}%")
    
    # 分类统计
    categories = {}
    for result in detailed_results:
        category = result['test_name'].split('-')[0] if '-' in result['test_name'] else result['test_name'].split('页面')[0]
        if category not in categories:
            categories[category] = {'passed': 0, 'total': 0}
        categories[category]['total'] += 1
        if result['success']:
            categories[category]['passed'] += 1
    
    print(f"\n📋 功能分类结果:")
    for category, stats in categories.items():
        if stats['total'] > 0:
            rate = stats['passed'] / stats['total'] * 100
            status = "✅" if rate >= 80 else "⚠️" if rate >= 50 else "❌"
            print(f"  {status} {category}: {stats['passed']}/{stats['total']} ({rate:.0f}%)")
    
    print(f"\n📊 详细报告: complete_functional_test_report.json")
    print("=" * 70)
    
    return complete_report['overall_success']


if __name__ == "__main__":
    success = run_complete_test()
    
    if success:
        print("🎉 完整功能测试成功！")
    else:
        print("⚠️ 部分功能需要优化，请查看详细报告")
