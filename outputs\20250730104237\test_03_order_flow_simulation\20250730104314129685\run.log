[I 2025-07-30 10:43:14,175 minium minitest#432 _miniSetUp] =========Current case: test_03_order_flow_simulation=========
[I 2025-07-30 10:43:14,176 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_03_order_flow_simulation
[I 2025-07-30 10:43:14,176 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:43:14,176 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:43:14,177 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:43:14,177 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:43:14,177 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:43:14,178 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:43:14,178 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"2697696b-7964-4578-8e3a-64c8677ba4e6","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:14,239 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"2697696b-7964-4578-8e3a-64c8677ba4e6","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 10:43:14,285 minium minitest#487 _miniSetUp] =========case: test_03_order_flow_simulation start=========
[I 2025-07-30 10:43:15,286 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:43:15,288 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"3db342ac-65f1-45b2-99ca-aa85866748f9","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:15,291 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:15,519 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843395513,"webviewId":5,"routeEventId":"5_1753843395307","renderer":"webview"},1753843395514]}}
[I 2025-07-30 10:43:15,520 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843395513, 'webviewId': 5, 'routeEventId': '5_1753843395307', 'renderer': 'webview'}, 1753843395514]}
[D 2025-07-30 10:43:15,521 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"3db342ac-65f1-45b2-99ca-aa85866748f9","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:15,521 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 3db342ac-65f1-45b2-99ca-aa85866748f9
[D 2025-07-30 10:43:18,522 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"6df627d1-2d1d-4736-835b-8d2da10fdfae","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:18,525 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"6df627d1-2d1d-4736-835b-8d2da10fdfae","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:43:18,526 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:18,526 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"b5582656-9b7d-4e3b-afeb-eac28b802dc6","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:18,529 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"b5582656-9b7d-4e3b-afeb-eac28b802dc6","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:43:18,530 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button, .dish-card, .food-item
[D 2025-07-30 10:43:18,531 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"ae299bb4-dfce-4f82-9817-b64732f241c6","method":"Page.getElements","params":{"selector":"view[bindtap], button, .dish-card, .food-item","pageId":5}}
[D 2025-07-30 10:43:18,539 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"ae299bb4-dfce-4f82-9817-b64732f241c6","result":{"elements":[{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","tagName":"view"},{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","tagName":"view"}]}}
[I 2025-07-30 10:43:18,540 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AF30>, <minium.miniprogram.base_driver.element.BaseElement object at 0x000002253375AC10>]
[D 2025-07-30 10:43:18,541 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"0c340ead-8210-4a18-a996-cbe3804d74bc","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:43:18,590 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"0c340ead-8210-4a18-a996-cbe3804d74bc","result":{"properties":["小菜\n今天\n火锅\n麻\n加入购物车"]}}
[D 2025-07-30 10:43:18,591 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"b58ea13e-4de5-4e99-b219-f27f0cd12987","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:43:18,594 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"b58ea13e-4de5-4e99-b219-f27f0cd12987","result":{"styles":["auto"]}}
[D 2025-07-30 10:43:18,595 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"72d6f2b2-c2fd-4ffe-ac58-a49cd8560dc1","method":"Element.tap","params":{"elementId":"2ce25892-5df6-4504-8036-80bc8a1f0330","pageId":5}}
[D 2025-07-30 10:43:18,705 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"72d6f2b2-c2fd-4ffe-ac58-a49cd8560dc1","result":{"pageX":238,"pageY":82,"clientX":238,"clientY":82}}
[D 2025-07-30 10:43:18,788 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/detail/index"}]}}
[D 2025-07-30 10:43:20,343 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/detail/index","query":{},"openType":"navigateTo","timeStamp":1753843400337,"webviewId":11,"routeEventId":"11_1753843399702","renderer":"webview"},1753843400338]}}
[I 2025-07-30 10:43:20,344 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/detail/index', 'query': {}, 'openType': 'navigateTo', 'timeStamp': 1753843400337, 'webviewId': 11, 'routeEventId': '11_1753843399702', 'renderer': 'webview'}, 1753843400338]}
[D 2025-07-30 10:43:20,708 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"a762f8c6-f081-4833-892a-d5bc4326017b","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 10:43:20,716 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"a762f8c6-f081-4833-892a-d5bc4326017b","result":{"properties":["红烧肉\n今天\n火锅\n甜\n加入购物车"]}}
[D 2025-07-30 10:43:20,720 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"f2e1045f-a8df-4755-af5f-eadedb2a070b","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 10:43:20,729 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"f2e1045f-a8df-4755-af5f-eadedb2a070b","result":{"styles":["auto"]}}
[D 2025-07-30 10:43:20,730 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"9c34b94f-a6f3-4883-9491-73b4c54afdd8","method":"Element.tap","params":{"elementId":"24deafb9-b660-44f1-84c9-83e09490e7f1","pageId":5}}
[D 2025-07-30 10:43:20,794 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"9c34b94f-a6f3-4883-9491-73b4c54afdd8","result":{"pageX":238,"pageY":237,"clientX":238,"clientY":237}}
[I 2025-07-30 10:43:22,797 minium page#716 _get_elements_by_css] try to get elements: .cart, .basket, .shopping, button[bindtap*='cart']
[D 2025-07-30 10:43:22,798 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"b4758d7e-bc07-4d41-8d33-8113d867ab6c","method":"Page.getElements","params":{"selector":".cart, .basket, .shopping, button[bindtap*='cart']","pageId":5}}
[D 2025-07-30 10:43:22,805 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"b4758d7e-bc07-4d41-8d33-8113d867ab6c","result":{"elements":[]}}
[W 2025-07-30 10:43:22,806 minium page#747 _get_elements_by_css] Could not found any element '.cart, .basket, .shopping, button[bindtap*='cart']' you need
[I 2025-07-30 10:43:22,808 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:43:22,808 minium minitest#799 _miniTearDown] =========Current case Down: test_03_order_flow_simulation=========
[I 2025-07-30 10:43:22,809 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:43:22,810 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"f438c83a-f84c-4a14-a449-528bb4b22931","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:22,884 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"f438c83a-f84c-4a14-a449-528bb4b22931","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmcZWdV773Wep49nbnm6upOes7cGcnEIIgBAcNwQUA+cMEZ0dfhXj9X8ZXX4ZXPq3D1SkRArigKKlcvqEhUMBCGzJCkE5LuTs9TVXXNZz57ep613j/2qUql0+lOQvUl6ezvJ6k+Z5/nnD57nz6/Ws8aMUoM5OTk5Jw16Pv9BnJycs5xcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5JxdcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5Jxd9DN9goicjfeRk5PzfAERn9H6p6syTxaXXG5ycl5QrIjLy
[I 2025-07-30 10:43:23,194 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:43:23,195 minium basenative#63 wrapper] call BaseNative.get_start_up end 
