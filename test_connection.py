#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试minium与微信开发者工具的连接 - 端口25209
"""

import os
import sys
import time

def test_minium_connection():
    print("🚀 测试minium与微信开发者工具连接")
    print("🔌 端口: 25209")
    print("📱 项目: wx82283b353918af82")
    print("=" * 50)
    
    try:
        # 导入minium
        from minium import MiniTest
        print("✅ minium导入成功")
        
        # 创建配置
        config = {
            "project_path": "E:/wx-nan",
            "app_id": "wx82283b353918af82", 
            "test_port": 25209,
            "close_ide": False,
            "auto_relaunch": True
        }
        
        print("📋 配置信息:")
        print(f"  项目路径: {config['project_path']}")
        print(f"  AppID: {config['app_id']}")
        print(f"  端口: {config['test_port']}")
        
        # 创建测试类
        class ConnectionTest(MiniTest):
            def setUp(self):
                super().setUp()
                self.app = self.mini.app
                print("✅ 测试类初始化成功")
            
            def test_connection(self):
                print("🧪 测试连接...")
                
                # 获取当前页面
                current_page = self.app.get_current_page()
                page_path = current_page.path
                print(f"📱 当前页面: {page_path}")
                
                # 截图测试
                screenshot_path = "connection_test.png"
                self.app.screen_shot(screenshot_path)
                print(f"📸 截图保存: {screenshot_path}")
                
                return True
        
        # 设置配置
        ConnectionTest.CONFIG = config
        
        # 运行测试
        import unittest
        suite = unittest.TestLoader().loadTestsFromTestCase(ConnectionTest)
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        if result.wasSuccessful():
            print("\n🎉 连接测试成功！")
            print("✅ minium已成功连接到微信开发者工具")
            print("✅ 小程序项目加载正常")
            return True
        else:
            print("\n❌ 连接测试失败！")
            if result.failures:
                print("失败原因:")
                for failure in result.failures:
                    print(f"  {failure[1]}")
            if result.errors:
                print("错误信息:")
                for error in result.errors:
                    print(f"  {error[1]}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minium_connection()
    
    if success:
        print("\n🎯 下一步可以运行完整的功能测试")
    else:
        print("\n🔧 请检查:")
        print("  1. 微信开发者工具是否已启动")
        print("  2. 小程序项目是否已加载")
        print("  3. 服务端口25209是否已开启")
        print("  4. 自动化接口是否已启用")
