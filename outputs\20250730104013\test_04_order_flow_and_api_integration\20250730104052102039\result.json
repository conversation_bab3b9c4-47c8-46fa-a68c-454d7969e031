{"case_name": "test_04_order_flow_and_api_integration", "run_time": "20250730 10:40:52", "test_type": "ComprehensiveFunctionalTest", "case_doc": "测试订餐流程和API集成", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843252.1760306, "is_failure": false, "is_error": false, "module": "E:.wx-nan.comprehensive_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/user_connection/index", "path": "images\\setup.png", "ts": 1753843252, "datetime": "2025-07-30 10:40:52", "use_region": false}, {"name": "teardown", "url": "/pages/order/index", "path": "images\\teardown.png", "ts": 1753843256, "datetime": "2025-07-30 10:40:56", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843256.5555422, "appId": "", "appName": "", "source": {"code": ["    def test_04_order_flow_and_api_integration(self):\n", "        \"\"\"测试订餐流程和API集成\"\"\"\n", "        print(\"🧪 测试订餐流程和API集成\")\n", "        \n", "        try:\n", "            # 导航到订餐页面\n", "            self.app.switch_tab(\"/pages/order/index\")\n", "            time.sleep(3)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 检查菜品数据加载\n", "            dish_elements = current_page.get_elements(\".dish-card, .food-item, .menu-item\")\n", "            if dish_elements:\n", "                self.log_test_result(\"菜品数据加载\", True, f\"加载了{len(dish_elements)}个菜品\")\n", "                \n", "                # 测试菜品交互\n", "                if len(dish_elements) > 0:\n", "                    first_dish = dish_elements[0]\n", "                    \n", "                    # 尝试添加菜品\n", "                    add_buttons = first_dish.get_elements(\".add-btn, .plus-btn, button[bindtap*='add']\")\n", "                    if add_buttons:\n", "                        add_buttons[0].click()\n", "                        time.sleep(1)\n", "                        self.log_test_result(\"菜品添加功能\", True, \"菜品添加操作完成\")\n", "                        \n", "                        # 检查购物车状态\n", "                        cart_elements = current_page.get_elements(\".cart-btn, .basket-btn, .shopping-cart\")\n", "                        if cart_elements:\n", "                            self.log_test_result(\"购物车状态更新\", True, \"购物车按钮存在\")\n", "                            \n", "                            # 点击购物车\n", "                            cart_elements[0].click()\n", "                            time.sleep(2)\n", "                            \n", "                            # 检查是否跳转到购物车页面\n", "                            new_page = self.app.get_current_page()\n", "                            if \"order\" in new_page.path or \"cart\" in new_page.path:\n", "                                self.log_test_result(\"购物车页面跳转\", True, f\"跳转到: {new_page.path}\")\n", "                                self._test_cart_functionality(new_page)\n", "                            else:\n", "                                self.log_test_result(\"购物车页面跳转\", False, \"未跳转到购物车\")\n", "                    else:\n", "                        self.log_test_result(\"菜品添加功能\", False, \"未找到添加按钮\")\n", "            else:\n", "                self.log_test_result(\"菜品数据加载\", False, \"未加载到菜品数据\")\n", "            \n", "        except Exception as e:\n", "            self.log_test_result(\"订餐流程测试\", False, f\"测试异常: {e}\")\n"], "start": 252}, "filename": "result.json"}