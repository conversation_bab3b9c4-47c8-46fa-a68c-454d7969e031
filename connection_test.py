#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
微信开发者工具连接测试
"""

import unittest
from minium import MiniTest

class ConnectionTest(MiniTest):
    def test_connection(self):
        """测试连接"""
        print("🧪 测试minium连接")
        
        # 获取应用实例
        app = self.mini.app
        self.assertIsNotNone(app, "应用实例应该存在")
        
        # 获取当前页面
        page = app.get_current_page()
        self.assertIsNotNone(page, "当前页面应该存在")
        
        print(f"📱 当前页面: {page.path}")
        print("✅ 连接测试成功")

if __name__ == "__main__":
    suite = unittest.TestLoader().loadTestsFromTestCase(ConnectionTest)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    if result.wasSuccessful():
        print("🎉 连接正常！")
    else:
        print("❌ 连接失败！")
