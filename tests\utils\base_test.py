#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import json
import unittest
from minium import minitest, ddt, data


class BaseTest(minitest.MiniTest):
    """基础测试类，提供通用的测试方法和工具"""
    
    def setUp(self):
        """测试前置操作"""
        super().setUp()
        self.app = self.mini.app
        self.page = None
        
        # 测试配置
        self.test_config = {
            "api_base_url": "http://8.148.231.104:3000/api",
            "test_user": {
                "username": "test_user",
                "password": "test123456",
                "phone": "13800138000"
            },
            "timeout": 10000,
            "retry_count": 3
        }
        
        print(f"🚀 开始测试: {self._testMethodName}")
    
    def tearDown(self):
        """测试后置操作"""
        if self.page:
            try:
                self.page.get_element("view").click()  # 确保页面响应
            except:
                pass
        
        print(f"✅ 测试完成: {self._testMethodName}")
        super().tearDown()
    
    def wait_for_page_load(self, timeout=5):
        """等待页面加载完成"""
        time.sleep(1)  # 基础等待
        
        # 等待loading消失
        try:
            loading_elements = self.page.get_elements(".loading, .van-loading")
            if loading_elements:
                for element in loading_elements:
                    self.wait_for_element_invisible(element, timeout)
        except:
            pass
    
    def wait_for_element_visible(self, selector, timeout=5):
        """等待元素可见"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                element = self.page.get_element(selector)
                if element and element.is_displayed():
                    return element
            except:
                pass
            time.sleep(0.5)
        
        raise AssertionError(f"元素 {selector} 在 {timeout}s 内未变为可见")
    
    def wait_for_element_invisible(self, element_or_selector, timeout=5):
        """等待元素不可见"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                if isinstance(element_or_selector, str):
                    element = self.page.get_element(element_or_selector)
                else:
                    element = element_or_selector
                
                if not element or not element.is_displayed():
                    return True
            except:
                return True
            time.sleep(0.5)
        
        return False
    
    def safe_click(self, selector, timeout=5):
        """安全点击元素"""
        element = self.wait_for_element_visible(selector, timeout)
        element.click()
        time.sleep(0.5)  # 点击后等待
        return element
    
    def safe_input(self, selector, text, timeout=5):
        """安全输入文本"""
        element = self.wait_for_element_visible(selector, timeout)
        element.input(text)
        time.sleep(0.3)
        return element
    
    def navigate_to_page(self, page_path):
        """导航到指定页面"""
        print(f"📱 导航到页面: {page_path}")
        self.app.navigate_to(page_path)
        self.page = self.app.get_current_page()
        self.wait_for_page_load()
        return self.page
    
    def switch_to_tab(self, page_path):
        """切换到Tab页面"""
        print(f"📱 切换到Tab: {page_path}")
        self.app.switch_tab(page_path)
        self.page = self.app.get_current_page()
        self.wait_for_page_load()
        return self.page
    
    def go_back(self):
        """返回上一页"""
        print("⬅️ 返回上一页")
        self.app.navigate_back()
        self.page = self.app.get_current_page()
        self.wait_for_page_load()
        return self.page
    
    def assert_page_contains_text(self, text):
        """断言页面包含指定文本"""
        page_text = self.page.get_element("page").inner_text
        self.assertIn(text, page_text, f"页面不包含文本: {text}")
    
    def assert_element_exists(self, selector):
        """断言元素存在"""
        element = self.page.get_element(selector)
        self.assertIsNotNone(element, f"元素不存在: {selector}")
        return element
    
    def assert_element_text(self, selector, expected_text):
        """断言元素文本"""
        element = self.assert_element_exists(selector)
        actual_text = element.inner_text
        self.assertEqual(actual_text, expected_text, 
                        f"元素文本不匹配. 期望: {expected_text}, 实际: {actual_text}")
    
    def take_screenshot(self, name=None):
        """截图"""
        if not name:
            name = f"{self._testMethodName}_{int(time.time())}"
        
        screenshot_path = f"tests/reports/screenshots/{name}.png"
        self.app.screen_shot(screenshot_path)
        print(f"📸 截图保存: {screenshot_path}")
        return screenshot_path
    
    def clear_app_data(self):
        """清理应用数据"""
        try:
            # 清除storage
            self.app.evaluate("wx.clearStorageSync()")
            print("🧹 应用数据已清理")
        except Exception as e:
            print(f"⚠️ 清理数据失败: {e}")
    
    def login_with_account(self, username=None, password=None):
        """使用账号密码登录"""
        username = username or self.test_config["test_user"]["username"]
        password = password or self.test_config["test_user"]["password"]
        
        print(f"🔐 开始登录: {username}")
        
        # 导航到登录页
        self.navigate_to_page("/pages/login/index")
        
        # 切换到账号登录
        self.safe_click(".tab-item[data-type='password']")
        
        # 输入用户名和密码
        self.safe_input("input[placeholder*='用户名']", username)
        self.safe_input("input[placeholder*='密码']", password)
        
        # 点击登录
        self.safe_click(".login-btn")
        
        # 等待登录完成
        self.wait_for_page_load(timeout=10)
        
        # 验证登录成功
        current_page = self.app.get_current_page()
        page_path = current_page.path
        
        if "login" not in page_path:
            print("✅ 登录成功")
            return True
        else:
            print("❌ 登录失败")
            return False
