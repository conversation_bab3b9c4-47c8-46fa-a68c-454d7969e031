#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import unittest
from minium import minitest
from utils.base_test import BaseTest


class TestUserManagement(BaseTest):
    """用户管理功能测试"""
    
    def setUp(self):
        super().setUp()
        # 确保用户已登录
        self.clear_app_data()
        login_success = self.login_with_account()
        if not login_success:
            self.fail("登录失败，无法进行用户管理测试")
    
    def test_01_mine_page_elements(self):
        """测试个人中心页面元素"""
        print("🧪 测试个人中心页面元素")
        
        # 导航到个人中心
        self.switch_to_tab("/pages/mine/index")
        
        # 验证用户信息区域
        try:
            self.assert_element_exists(".user-info, .profile-section")
            print("✅ 用户信息区域存在")
            
            # 检查用户头像
            avatar = self.page.get_element(".user-avatar, .avatar")
            if avatar:
                print("✅ 用户头像存在")
            
            # 检查用户名称
            username = self.page.get_element(".user-name, .username")
            if username:
                print(f"👤 用户名称: {username.inner_text}")
        
        except Exception as e:
            print(f"⚠️ 用户信息区域验证失败: {e}")
        
        # 验证功能菜单
        menu_items = [
            "我的菜品",
            "新增菜品", 
            "用户关联",
            "关联历史",
            "通知中心",
            "退出登录"
        ]
        
        for item_text in menu_items:
            try:
                # 查找包含指定文本的元素
                elements = self.page.get_elements("view, text")
                found = False
                for element in elements:
                    if item_text in element.inner_text:
                        print(f"✅ 找到菜单项: {item_text}")
                        found = True
                        break
                
                if not found:
                    print(f"⚠️ 未找到菜单项: {item_text}")
            
            except Exception as e:
                print(f"⚠️ 检查菜单项 {item_text} 失败: {e}")
        
        self.take_screenshot("mine_page_elements")
        print("✅ 个人中心页面元素验证完成")
    
    def test_02_my_dishes_navigation(self):
        """测试我的菜品页面导航"""
        print("🧪 测试我的菜品页面导航")
        
        self.switch_to_tab("/pages/mine/index")
        
        try:
            # 查找我的菜品按钮
            my_dishes_btn = None
            clickable_elements = self.page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                if "我的菜品" in element.inner_text:
                    my_dishes_btn = element
                    break
            
            if my_dishes_btn:
                my_dishes_btn.click()
                time.sleep(2)
                
                # 验证跳转到我的菜品页面
                current_page = self.app.get_current_page()
                if "my-dishes" in current_page.path:
                    print("✅ 成功跳转到我的菜品页面")
                    
                    # 验证我的菜品页面元素
                    self._verify_my_dishes_page()
                    
                    # 返回个人中心
                    self.go_back()
                else:
                    print("⚠️ 未能跳转到我的菜品页面")
            else:
                print("⚠️ 我的菜品按钮未找到")
        
        except Exception as e:
            print(f"⚠️ 我的菜品导航测试失败: {e}")
        
        self.take_screenshot("my_dishes_navigation")
        print("✅ 我的菜品导航测试完成")
    
    def test_03_add_dish_navigation(self):
        """测试新增菜品页面导航"""
        print("🧪 测试新增菜品页面导航")
        
        self.switch_to_tab("/pages/mine/index")
        
        try:
            # 查找新增菜品按钮
            add_dish_btn = None
            clickable_elements = self.page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                if "新增菜品" in element.inner_text:
                    add_dish_btn = element
                    break
            
            if add_dish_btn:
                add_dish_btn.click()
                time.sleep(2)
                
                # 验证跳转到新增菜品页面
                current_page = self.app.get_current_page()
                if "dish/add" in current_page.path:
                    print("✅ 成功跳转到新增菜品页面")
                    
                    # 验证新增菜品页面元素
                    self._verify_add_dish_page()
                    
                    # 返回个人中心
                    self.go_back()
                else:
                    print("⚠️ 未能跳转到新增菜品页面")
            else:
                print("⚠️ 新增菜品按钮未找到")
        
        except Exception as e:
            print(f"⚠️ 新增菜品导航测试失败: {e}")
        
        self.take_screenshot("add_dish_navigation")
        print("✅ 新增菜品导航测试完成")
    
    def test_04_user_connection_navigation(self):
        """测试用户关联页面导航"""
        print("🧪 测试用户关联页面导航")
        
        self.switch_to_tab("/pages/mine/index")
        
        try:
            # 查找用户关联按钮
            connection_btn = None
            clickable_elements = self.page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                if "用户关联" in element.inner_text and "历史" not in element.inner_text:
                    connection_btn = element
                    break
            
            if connection_btn:
                connection_btn.click()
                time.sleep(2)
                
                # 验证跳转到用户关联页面
                current_page = self.app.get_current_page()
                if "user_connection" in current_page.path:
                    print("✅ 成功跳转到用户关联页面")
                    
                    # 验证用户关联页面元素
                    self._verify_user_connection_page()
                    
                    # 返回个人中心
                    self.go_back()
                else:
                    print("⚠️ 未能跳转到用户关联页面")
            else:
                print("⚠️ 用户关联按钮未找到")
        
        except Exception as e:
            print(f"⚠️ 用户关联导航测试失败: {e}")
        
        self.take_screenshot("user_connection_navigation")
        print("✅ 用户关联导航测试完成")
    
    def test_05_notification_center_navigation(self):
        """测试通知中心页面导航"""
        print("🧪 测试通知中心页面导航")
        
        self.switch_to_tab("/pages/mine/index")
        
        try:
            # 查找通知中心按钮
            notification_btn = None
            clickable_elements = self.page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                if "通知中心" in element.inner_text:
                    notification_btn = element
                    break
            
            if notification_btn:
                notification_btn.click()
                time.sleep(2)
                
                # 验证跳转到通知中心页面
                current_page = self.app.get_current_page()
                if "notification_center" in current_page.path:
                    print("✅ 成功跳转到通知中心页面")
                    
                    # 验证通知中心页面元素
                    self._verify_notification_center_page()
                    
                    # 返回个人中心
                    self.go_back()
                else:
                    print("⚠️ 未能跳转到通知中心页面")
            else:
                print("⚠️ 通知中心按钮未找到")
        
        except Exception as e:
            print(f"⚠️ 通知中心导航测试失败: {e}")
        
        self.take_screenshot("notification_center_navigation")
        print("✅ 通知中心导航测试完成")
    
    def test_06_logout_functionality(self):
        """测试退出登录功能"""
        print("🧪 测试退出登录功能")
        
        self.switch_to_tab("/pages/mine/index")
        
        try:
            # 查找退出登录按钮
            logout_btn = None
            clickable_elements = self.page.get_elements("view[bindtap], button")
            
            for element in clickable_elements:
                if "退出登录" in element.inner_text:
                    logout_btn = element
                    break
            
            if logout_btn:
                logout_btn.click()
                time.sleep(1)
                
                # 查找确认对话框
                try:
                    confirm_btn = self.page.get_element(".van-dialog__confirm, button[bindtap*='confirm']")
                    if confirm_btn:
                        confirm_btn.click()
                        time.sleep(3)
                        
                        # 验证是否跳转到登录页面
                        current_page = self.app.get_current_page()
                        if "login" in current_page.path:
                            print("✅ 退出登录成功，已跳转到登录页面")
                            
                            # 重新登录以便后续测试
                            self.login_with_account()
                        else:
                            print("⚠️ 退出登录后未跳转到登录页面")
                    else:
                        print("⚠️ 确认对话框未找到")
                
                except Exception as e:
                    print(f"⚠️ 处理确认对话框失败: {e}")
            else:
                print("⚠️ 退出登录按钮未找到")
        
        except Exception as e:
            print(f"⚠️ 退出登录测试失败: {e}")
        
        self.take_screenshot("logout_functionality")
        print("✅ 退出登录功能测试完成")
    
    def _verify_my_dishes_page(self):
        """验证我的菜品页面"""
        try:
            # 检查页面标题
            page_title = self.page.get_element(".page-title, .navbar-title")
            if page_title and "菜品" in page_title.inner_text:
                print("✅ 我的菜品页面标题正确")
            
            # 检查菜品列表
            dish_list = self.page.get_element(".dishes-list, .dish-list")
            if dish_list:
                print("✅ 菜品列表区域存在")
                
                # 检查菜品项
                dish_items = self.page.get_elements(".dish-card, .dish-item")
                print(f"🍽️ 我的菜品数量: {len(dish_items)}")
            
            # 检查空状态
            empty_state = self.page.get_element(".empty-state, .empty")
            if empty_state:
                print("📭 显示空状态")
        
        except Exception as e:
            print(f"⚠️ 验证我的菜品页面失败: {e}")
    
    def _verify_add_dish_page(self):
        """验证新增菜品页面"""
        try:
            # 检查表单元素
            form_elements = [
                "input[placeholder*='菜品名称']",
                "textarea[placeholder*='描述']",
                "input[placeholder*='配料']",
                ".image-upload, .upload-area"
            ]
            
            for selector in form_elements:
                try:
                    element = self.page.get_element(selector)
                    if element:
                        print(f"✅ 表单元素存在: {selector}")
                except:
                    print(f"⚠️ 表单元素未找到: {selector}")
            
            # 检查提交按钮
            submit_btn = self.page.get_element(".submit-btn, button[bindtap*='submit']")
            if submit_btn:
                print("✅ 提交按钮存在")
        
        except Exception as e:
            print(f"⚠️ 验证新增菜品页面失败: {e}")
    
    def _verify_user_connection_page(self):
        """验证用户关联页面"""
        try:
            # 检查搜索功能
            search_input = self.page.get_element("input[placeholder*='搜索'], .search-input")
            if search_input:
                print("✅ 搜索功能存在")
            
            # 检查用户列表
            user_list = self.page.get_element(".user-list, .users")
            if user_list:
                print("✅ 用户列表区域存在")
                
                user_items = self.page.get_elements(".user-item, .user-card")
                print(f"👥 可关联用户数量: {len(user_items)}")
        
        except Exception as e:
            print(f"⚠️ 验证用户关联页面失败: {e}")
    
    def _verify_notification_center_page(self):
        """验证通知中心页面"""
        try:
            # 检查通知列表
            notification_list = self.page.get_element(".notification-list, .notices")
            if notification_list:
                print("✅ 通知列表区域存在")
                
                notification_items = self.page.get_elements(".notification-item, .notice-item")
                print(f"📢 通知数量: {len(notification_items)}")
            
            # 检查空状态
            empty_state = self.page.get_element(".empty-state, .empty")
            if empty_state:
                print("📭 显示空状态")
        
        except Exception as e:
            print(f"⚠️ 验证通知中心页面失败: {e}")


if __name__ == '__main__':
    unittest.main()
