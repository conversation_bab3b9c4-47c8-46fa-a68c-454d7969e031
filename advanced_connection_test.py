#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级连接测试 - 尝试不同的WebSocket路径和协议
"""

import socket
import time
import json

def test_websocket_paths():
    """测试不同的WebSocket路径"""
    print("🌐 测试不同的WebSocket路径...")
    
    # 常见的微信开发者工具WebSocket路径
    paths = [
        "",  # 根路径
        "/",  # 根路径
        "/ws",  # 常见WebSocket路径
        "/websocket",  # 完整WebSocket路径
        "/devtools/page",  # DevTools页面路径
        "/json",  # Chrome DevTools协议
        "/json/list",  # Chrome DevTools页面列表
    ]
    
    for path in paths:
        url = f"ws://localhost:25209{path}"
        print(f"📡 尝试连接: {url}")
        
        try:
            import websocket
            
            # 尝试连接
            ws = websocket.create_connection(url, timeout=5)
            
            print(f"✅ 连接成功: {url}")
            
            # 尝试发送测试消息
            try:
                test_msg = json.dumps({"id": 1, "method": "Runtime.evaluate", "params": {"expression": "1+1"}})
                ws.send(test_msg)
                
                # 等待响应
                response = ws.recv()
                print(f"📨 收到响应: {response[:100]}...")
                
                ws.close()
                return url, True
                
            except Exception as e:
                print(f"⚠️ 消息发送失败: {e}")
                ws.close()
                return url, False
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
    
    return None, False

def test_http_endpoints():
    """测试HTTP端点"""
    print("\n🌐 测试HTTP端点...")
    
    import urllib.request
    import urllib.error
    
    endpoints = [
        "/json",
        "/json/list", 
        "/json/version",
        "/devtools/page",
        "/",
    ]
    
    for endpoint in endpoints:
        url = f"http://localhost:25209{endpoint}"
        print(f"📡 尝试访问: {url}")
        
        try:
            with urllib.request.urlopen(url, timeout=5) as response:
                data = response.read().decode('utf-8')
                print(f"✅ HTTP访问成功: {response.status}")
                print(f"📄 响应内容: {data[:200]}...")
                
                # 如果是JSON响应，尝试解析
                try:
                    json_data = json.loads(data)
                    print(f"📋 JSON数据: {json_data}")
                except:
                    pass
                    
                return url, True
                
        except urllib.error.HTTPError as e:
            print(f"❌ HTTP错误: {e.code} {e.reason}")
        except Exception as e:
            print(f"❌ 访问失败: {e}")
    
    return None, False

def test_minium_with_custom_config():
    """使用自定义配置测试minium"""
    print("\n🧪 测试minium连接...")
    
    try:
        from minium import MiniTest
        
        # 创建测试类
        class QuickTest(MiniTest):
            def setUp(self):
                super().setUp()
                print("✅ minium初始化成功")
            
            def test_quick_connection(self):
                print("🧪 快速连接测试")
                
                # 获取应用实例
                app = self.mini.app
                print(f"📱 应用实例: {app}")
                
                # 获取当前页面
                page = app.get_current_page()
                print(f"📄 当前页面: {page.path}")
                
                # 简单截图
                app.screen_shot("quick_test.png")
                print("📸 截图完成: quick_test.png")
                
                return True
        
        # 运行测试
        import unittest
        suite = unittest.TestLoader().loadTestsFromTestCase(QuickTest)
        runner = unittest.TextTestRunner(verbosity=1)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except Exception as e:
        print(f"❌ minium测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 高级连接诊断测试")
    print("📱 目标: 微信开发者工具 (端口25209)")
    print("=" * 50)
    
    # 测试1: 基础端口连接
    print("🔌 测试基础端口连接...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', 25209))
        sock.close()
        
        if result == 0:
            print("✅ 端口25209连接成功")
        else:
            print(f"❌ 端口连接失败: {result}")
            return False
    except Exception as e:
        print(f"❌ 端口测试异常: {e}")
        return False
    
    # 测试2: HTTP端点
    http_url, http_ok = test_http_endpoints()
    
    # 测试3: WebSocket路径
    ws_url, ws_ok = test_websocket_paths()
    
    # 测试4: minium连接
    print("\n" + "=" * 50)
    minium_ok = test_minium_with_custom_config()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"  端口连接: ✅")
    print(f"  HTTP访问: {'✅' if http_ok else '❌'}")
    print(f"  WebSocket: {'✅' if ws_ok else '❌'}")
    print(f"  minium连接: {'✅' if minium_ok else '❌'}")
    
    if minium_ok:
        print("\n🎉 所有测试成功！")
        print("✅ minium可以正常连接微信开发者工具")
        print("✅ 现在可以运行完整的小程序测试了")
        return True
    else:
        print("\n⚠️ 部分测试失败")
        if http_ok:
            print(f"💡 HTTP端点工作正常: {http_url}")
        if ws_ok:
            print(f"💡 WebSocket路径工作正常: {ws_url}")
        
        print("\n🔧 建议检查:")
        print("  1. 确认小程序项目已在开发者工具中打开")
        print("  2. 确认项目AppID为: wx82283b353918af82")
        print("  3. 尝试重启微信开发者工具")
        print("  4. 检查是否有防火墙阻止连接")
        
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🚀 准备运行完整的小程序功能测试...")
    else:
        print("\n❌ 连接问题需要先解决")
