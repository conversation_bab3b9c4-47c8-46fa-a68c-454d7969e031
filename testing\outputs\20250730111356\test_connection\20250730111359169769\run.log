[I 2025-07-30 11:13:59,171 minium minitest#432 _miniSetUp] =========Current case: test_connection=========
[I 2025-07-30 11:13:59,172 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.connection_test, case info: ConnectionTest.test_connection
[I 2025-07-30 11:13:59,172 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:13:59,172 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:13:59,172 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:13:59,172 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:13:59,173 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:13:59,174 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:13:59,174 minium.Conn9904 connection#427 _safely_send] SEND > [2849230459904]{"id":"78e9095e-cfca-4ad9-b636-1e0ae73be049","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,260 minium.Conn9904 connection#660 __on_message] RECV < [2849230459904]{"id":"78e9095e-cfca-4ad9-b636-1e0ae73be049","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:13:59,262 minium minitest#487 _miniSetUp] =========case: test_connection start=========
[I 2025-07-30 11:13:59,263 minium minitest#788 _callTestMethod] call test method
[I 2025-07-30 11:13:59,433 minium minitest#897 capture] capture assertIsNotNone-success.png
[D 2025-07-30 11:13:59,433 minium.Conn9904 connection#427 _safely_send] SEND > [2849230459904]{"id":"6cad4b15-5264-4cfe-bdae-69756b369c5f","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,499 minium.Conn9904 connection#660 __on_message] RECV < [2849230459904]{"id":"6cad4b15-5264-4cfe-bdae-69756b369c5f","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[D 2025-07-30 11:13:59,501 minium.Conn9904 connection#427 _safely_send] SEND > [2849230459904]{"id":"7ad90619-85e9-4040-b0d0-720c0088f7b1","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:13:59,502 minium.Conn9904 connection#660 __on_message] RECV < [2849230459904]{"id":"7ad90619-85e9-4040-b0d0-720c0088f7b1","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 11:13:59,503 minium.App4608 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:13:59,503 minium.Conn9904 connection#427 _safely_send] SEND > [2849230459904]{"id":"bb458d22-6829-4e11-9fce-5a6e3d7da7ef","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:13:59,505 minium.Conn9904 connection#660 __on_message] RECV < [2849230459904]{"id":"bb458d22-6829-4e11-9fce-5a6e3d7da7ef","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:13:59,507 minium minitest#897 capture] capture assertIsNotNone-success_111359507868.png
[D 2025-07-30 11:13:59,508 minium.Conn9904 connection#427 _safely_send] SEND > [2849230459904]{"id":"24b89c3c-aea5-4c00-abf2-91205edf6449","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,583 minium.Conn9904 connection#660 __on_message] RECV < [2849230459904]{"id":"24b89c3c-aea5-4c00-abf2-91205edf6449","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:13:59,585 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:13:59,585 minium minitest#799 _miniTearDown] =========Current case Down: test_connection=========
[I 2025-07-30 11:13:59,585 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:13:59,586 minium.Conn9904 connection#427 _safely_send] SEND > [2849230459904]{"id":"0f21ad2e-422f-4821-a52a-e73de84c2a4b","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:13:59,667 minium.Conn9904 connection#660 __on_message] RECV < [2849230459904]{"id":"0f21ad2e-422f-4821-a52a-e73de84c2a4b","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 11:13:59,669 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:13:59,669 minium basenative#63 wrapper] call BaseNative.get_start_up end 
