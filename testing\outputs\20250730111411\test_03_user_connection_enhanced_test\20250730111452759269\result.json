{"case_name": "test_03_user_connection_enhanced_test", "run_time": "20250730 11:14:52", "test_type": "EnhancedFunctionalTest", "case_doc": "用户关联增强测试", "success": true, "failures": "", "errors": "", "start_timestamp": 1753845292.844584, "is_failure": false, "is_error": false, "module": "E:.wx-nan.testing.enhanced_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/mine/index", "path": "images\\setup.png", "ts": 1753845292, "datetime": "2025-07-30 11:14:52", "use_region": false}, {"name": "teardown", "url": "/pages/mine/index", "path": "images\\teardown.png", "ts": 1753845315, "datetime": "2025-07-30 11:15:15", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753845315.2058203, "appId": "", "appName": "", "source": {"code": ["    def test_03_user_connection_enhanced_test(self):\n", "        \"\"\"用户关联增强测试\"\"\"\n", "        print(\"🧪 用户关联增强测试\")\n", "        \n", "        try:\n", "            # 导航到个人中心\n", "            self.app.switch_tab(\"/pages/mine/index\")\n", "            time.sleep(2)\n", "            \n", "            mine_page = self.app.get_current_page()\n", "            \n", "            # 测试用户关联入口和功能\n", "            self._test_user_connection_enhanced(mine_page)\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"用户关联增强测试\", False, f\"测试异常: {e}\", critical=True)\n"], "start": 512}, "filename": "result.json"}