{"case_name": "test_03_user_association_functionality", "run_time": "20250730 10:40:44", "test_type": "ComprehensiveFunctionalTest", "case_doc": "测试用户关联功能", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843244.603492, "is_failure": false, "is_error": false, "module": "E:.wx-nan.comprehensive_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/login/index", "path": "images\\setup.png", "ts": 1753843244, "datetime": "2025-07-30 10:40:44", "use_region": false}, {"name": "teardown", "url": "/pages/user_connection/index", "path": "images\\teardown.png", "ts": 1753843252, "datetime": "2025-07-30 10:40:52", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843252.0968192, "appId": "", "appName": "", "source": {"code": ["    def test_03_user_association_functionality(self):\n", "        \"\"\"测试用户关联功能\"\"\"\n", "        print(\"🧪 测试用户关联功能\")\n", "        \n", "        try:\n", "            # 导航到个人中心\n", "            self.app.switch_tab(\"/pages/mine/index\")\n", "            time.sleep(2)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 查找用户关联入口\n", "            association_found = False\n", "            clickable_elements = current_page.get_elements(\"view[bindtap], button\")\n", "            \n", "            for element in clickable_elements:\n", "                try:\n", "                    text = element.inner_text\n", "                    if \"用户关联\" in text or \"关联\" in text:\n", "                        association_found = True\n", "                        self.log_test_result(\"用户关联入口\", True, f\"找到关联入口: {text}\")\n", "                        \n", "                        # 点击进入用户关联页面\n", "                        element.click()\n", "                        time.sleep(3)\n", "                        \n", "                        # 检查是否跳转到关联页面\n", "                        new_page = self.app.get_current_page()\n", "                        if \"connection\" in new_page.path or \"关联\" in new_page.path:\n", "                            self.log_test_result(\"用户关联页面跳转\", True, f\"成功跳转到: {new_page.path}\")\n", "                            \n", "                            # 测试关联页面功能\n", "                            self._test_association_page_features(new_page)\n", "                        else:\n", "                            self.log_test_result(\"用户关联页面跳转\", False, \"未跳转到关联页面\")\n", "                        break\n", "                except:\n", "                    continue\n", "            \n", "            if not association_found:\n", "                self.log_test_result(\"用户关联入口\", False, \"未找到用户关联入口\")\n", "            \n", "        except Exception as e:\n", "            self.log_test_result(\"用户关联测试\", False, f\"测试异常: {e}\")\n"], "start": 175}, "filename": "result.json"}