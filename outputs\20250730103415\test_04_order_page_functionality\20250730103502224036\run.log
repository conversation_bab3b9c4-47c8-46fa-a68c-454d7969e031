[I 2025-07-30 10:35:02,228 minium minitest#432 _miniSetUp] =========Current case: test_04_order_page_functionality=========
[I 2025-07-30 10:35:02,228 minium minitest#435 _miniSetUp] package info: E:.wx-nan.complete_miniprogram_test, case info: CompleteMiniProgramTest.test_04_order_page_functionality
[I 2025-07-30 10:35:02,228 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:35:02,229 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:35:02,229 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:35:02,229 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:35:02,229 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:35:02,230 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:35:02,231 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"0e479a64-7780-4a4a-a529-a0f6f1dc9d07","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:02,325 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"0e479a64-7780-4a4a-a529-a0f6f1dc9d07","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXeAHMWZNv6+VR0m7M4G5SyUEQiRBBgL23BkOWGyjTHGYDhzfMbhvjO+s30c9jl8Z86/4+Bsg882xsYmHAYMnAEDThgMAoSQBCghaVdhd7VhdkKnqvr9Ud09PT09s7MrCQlUj6D3mZrq6qqeqWfeeqq6Gy3HAwAAQEQhhOKKK674XuahyigoKCjsCxAAQMTwteKKK6743uX7KpYJgyUFBYW3C6ICsRdBEg8wWg4AIgBEJObAUVPFFVd8RB7237Av753y9zCWUTGLgsI7HnsY42hj2Ecpi4LCQYVolx+D4oxOZZrRF6VBCgpvazTWEdnBR6U1FV8GorslcSFEvTwiOpYbqRzFFVf8QOaijjETy9N8mc36MokRS
[I 2025-07-30 10:35:02,327 minium minitest#487 _miniSetUp] =========case: test_04_order_page_functionality start=========
[I 2025-07-30 10:35:03,328 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:35:03,329 minium.Conn9680 connection#427 _safely_send] ASYNC_SEND > [2116691399680]{"id":"5e3156cc-52a7-48a6-bd37-da34223d0f24","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:03,330 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:35:03,562 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753842903557,"webviewId":5,"routeEventId":"5_1753842903353","renderer":"webview"},1753842903558]}}
[D 2025-07-30 10:35:03,563 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"5e3156cc-52a7-48a6-bd37-da34223d0f24","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:35:03,564 minium.App4384 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753842903557, 'webviewId': 5, 'routeEventId': '5_1753842903353', 'renderer': 'webview'}, 1753842903558]}
[I 2025-07-30 10:35:03,564 minium.Conn9680 connection#704 _handle_async_msg] received async msg: 5e3156cc-52a7-48a6-bd37-da34223d0f24
[D 2025-07-30 10:35:06,565 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"9ca79f2b-12db-4e3c-aca4-81ab375f8cc4","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:35:06,569 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"9ca79f2b-12db-4e3c-aca4-81ab375f8cc4","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:35:06,569 minium.App4384 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:35:06,570 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"80e4da19-88e9-4530-a237-a17c29eb4458","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:35:06,572 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"80e4da19-88e9-4530-a237-a17c29eb4458","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:35:06,575 minium minitest#897 capture] capture assertIn-success.png
[D 2025-07-30 10:35:06,576 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"5cfbe52e-7d58-47e2-905a-90b21e4cce75","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:06,701 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"5cfbe52e-7d58-47e2-905a-90b21e4cce75","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:06,704 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 10:35:06,704 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"5d6c96d6-9662-4b7d-a77b-07c156f8ec36","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":5}}
[D 2025-07-30 10:35:06,710 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"5d6c96d6-9662-4b7d-a77b-07c156f8ec36","result":{"elements":[]}}
[W 2025-07-30 10:35:06,711 minium page#747 _get_elements_by_css] Could not found any element 'view[bindtap], button' you need
[D 2025-07-30 10:35:06,711 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"5acaaf3c-7669-4a66-9de4-7c565bd58de5","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:06,882 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"5acaaf3c-7669-4a66-9de4-7c565bd58de5","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:06,888 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:35:06,888 minium minitest#799 _miniTearDown] =========Current case Down: test_04_order_page_functionality=========
[I 2025-07-30 10:35:06,889 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:35:06,890 minium.Conn9680 connection#427 _safely_send] SEND > [2116691399680]{"id":"98eca09b-55c4-4b0b-97c7-4092099a6a43","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:35:07,016 minium.Conn9680 connection#660 __on_message] RECV < [2116691399680]{"id":"98eca09b-55c4-4b0b-97c7-4092099a6a43","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgJUV1P37Oqaruu7w3zAw4MywuLIqBGQQR0CiQCCiKaIjBSAxovlERNGqMgzFKUBH9CfhN5KsQxLjgQlyiUUCDAnGJft0ABeZrUBYFhllklvfe3bq76pzfH9Xdt+/y7ntv5s36+jNv7q1ebnXfurc+95xPnTqFndgCAAAgooiU5dmUS5QoMXtgzjIlSpQosSNAAICI+XZZHl0uUaLEXFHaMiVKlNixoLy0O1gKe0q5RIkSs0dpy5QoUWLHgmY+pUSJEiW2AyXLlChRYseiwDJF3aEsjy6XKFFi1ih1mRIlSuxYlB5TiRIldixKlilRosSOhS6UEUDK8uzKw+EnOhUfEUApUkREWEbclNhDISLM4pidY98H/
[I 2025-07-30 10:35:07,020 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:35:07,021 minium basenative#63 wrapper] call BaseNative.get_start_up end 
