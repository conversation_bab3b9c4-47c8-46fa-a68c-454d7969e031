[I 2025-07-30 10:43:23,211 minium minitest#432 _miniSetUp] =========Current case: test_04_data_persistence_and_state=========
[I 2025-07-30 10:43:23,211 minium minitest#435 _miniSetUp] package info: E:.wx-nan.stable_functional_test, case info: StableFunctionalTest.test_04_data_persistence_and_state
[I 2025-07-30 10:43:23,212 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:43:23,212 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:43:23,213 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:43:23,214 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:43:23,215 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:43:23,218 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:43:23,218 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"e6743819-e704-4262-a21e-8f1e63864afb","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:23,286 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"e6743819-e704-4262-a21e-8f1e63864afb","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmcZWdV773Wep49nbnm6upOes7cGcnEIIgBAcNwQUA+cMEZ0dfhXj9X8ZXX4ZXPq3D1SkRArigKKlcvqEhUMBCGzJCkE5LuTs9TVXXNZz57ep613j/2qUql0+lOQvUl6ezvJ6k+Z5/nnD57nz6/Ws8aMUoM5OTk5Jw16Pv9BnJycs5xcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5JxdcpXJyck5u+Qqk5OTc3bJVSYnJ+fskqtMTk7O2SVXmZycnLNLrjI5OTlnl1xlcnJyzi65yuTk5Jxd9DN9goicjfeRk5PzfAERn9H6p6syTxaXXG5ycl5QrIjLy
[I 2025-07-30 10:43:23,594 minium minitest#487 _miniSetUp] =========case: test_04_data_persistence_and_state start=========
[I 2025-07-30 10:43:24,595 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:43:24,597 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"2bd78636-bd89-4c04-bd5e-08deffe2a695","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:24,603 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:24,852 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753843404843,"webviewId":2,"routeEventId":"2_1753843404628","renderer":"webview"},1753843404845]}}
[I 2025-07-30 10:43:24,853 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843404843, 'webviewId': 2, 'routeEventId': '2_1753843404628', 'renderer': 'webview'}, 1753843404845]}
[D 2025-07-30 10:43:24,853 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"2bd78636-bd89-4c04-bd5e-08deffe2a695","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:24,854 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 2bd78636-bd89-4c04-bd5e-08deffe2a695
[D 2025-07-30 10:43:25,855 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"4d1f9a74-f706-4a6d-b1af-e972590e3d24","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:25,860 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"4d1f9a74-f706-4a6d-b1af-e972590e3d24","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:43:25,861 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:25,862 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"ded3f97e-9478-4753-9ddc-7ff7128aaed2","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:25,871 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"ded3f97e-9478-4753-9ddc-7ff7128aaed2","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:25,872 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"749d1f1a-b64a-4b80-ba47-cb50c5f8d07f","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:25,875 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/order/index"}]}}
[D 2025-07-30 10:43:26,102 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/order/index","query":{},"openType":"switchTab","timeStamp":1753843406098,"webviewId":5,"routeEventId":"5_1753843405894","renderer":"webview"},1753843406099]}}
[I 2025-07-30 10:43:26,103 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/order/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843406098, 'webviewId': 5, 'routeEventId': '5_1753843405894', 'renderer': 'webview'}, 1753843406099]}
[D 2025-07-30 10:43:26,104 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"749d1f1a-b64a-4b80-ba47-cb50c5f8d07f","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:26,104 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 749d1f1a-b64a-4b80-ba47-cb50c5f8d07f
[D 2025-07-30 10:43:27,105 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"c516142e-3568-40c6-837a-c91e833413d1","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:27,111 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"c516142e-3568-40c6-837a-c91e833413d1","result":{"pageId":5,"path":"pages/order/index","query":{}}}
[D 2025-07-30 10:43:27,112 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:27,114 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"2635cd90-a880-4ce8-83b1-aff0c8b9c76e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:27,123 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"2635cd90-a880-4ce8-83b1-aff0c8b9c76e","result":{"result":{"pageId":5,"path":"pages/order/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:27,125 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"71c56383-6491-4090-94ed-c9ad8df6d0d1","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:43:27,128 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 10:43:27,387 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753843407375,"webviewId":3,"routeEventId":"3_1753843407149","renderer":"webview"},1753843407378]}}
[I 2025-07-30 10:43:27,391 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843407375, 'webviewId': 3, 'routeEventId': '3_1753843407149', 'renderer': 'webview'}, 1753843407378]}
[D 2025-07-30 10:43:27,395 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"71c56383-6491-4090-94ed-c9ad8df6d0d1","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:27,744 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 71c56383-6491-4090-94ed-c9ad8df6d0d1
[D 2025-07-30 10:43:28,747 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"e99d64f7-e764-4a31-9bc3-e6c86fb9e6d7","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:28,750 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"e99d64f7-e764-4a31-9bc3-e6c86fb9e6d7","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 10:43:28,752 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:28,753 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"20c21692-45c6-49b9-a11f-6c1aad90650f","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:28,757 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"20c21692-45c6-49b9-a11f-6c1aad90650f","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:28,758 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"f0744fef-25f8-4c37-ade1-46b5dbe05da0","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:28,761 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:43:28,996 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/home/<USER>","query":{},"openType":"switchTab","timeStamp":1753843408991,"webviewId":2,"routeEventId":"2_1753843408779","renderer":"webview"},1753843408992]}}
[I 2025-07-30 10:43:28,997 minium.App7152 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/home/<USER>', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753843408991, 'webviewId': 2, 'routeEventId': '2_1753843408779', 'renderer': 'webview'}, 1753843408992]}
[D 2025-07-30 10:43:29,000 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"f0744fef-25f8-4c37-ade1-46b5dbe05da0","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:43:29,003 minium.Conn2448 connection#704 _handle_async_msg] received async msg: f0744fef-25f8-4c37-ade1-46b5dbe05da0
[D 2025-07-30 10:43:30,006 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"c423839c-17d8-40b4-9fd9-ba355e1819bc","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:43:30,008 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"c423839c-17d8-40b4-9fd9-ba355e1819bc","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:43:30,008 minium.App7152 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:43:30,009 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"e739c442-911e-40d4-9b01-96eb1d751dcd","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:43:30,010 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"e739c442-911e-40d4-9b01-96eb1d751dcd","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:43:30,011 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"2da30c8d-b9ca-41b3-94d6-0a5eedd19793","method":"App.callFunction","params":{"functionDeclaration":"getCurrentPages().length","args":[]}}
[D 2025-07-30 10:43:30,012 minium.Conn2448 connection#427 _safely_send] ASYNC_SEND > [2358795752448]{"id":"0b2d930e-c59a-4495-a8d3-64939307309b","method":"App.callFunction","params":{"functionDeclaration":"wx.getStorageInfoSync()","args":[]}}
[I 2025-07-30 10:43:30,012 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:43:30,013 minium minitest#799 _miniTearDown] =========Current case Down: test_04_data_persistence_and_state=========
[D 2025-07-30 10:43:30,013 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"2da30c8d-b9ca-41b3-94d6-0a5eedd19793","error":{"message":"Arg string terminates parameters early"}}
[E 2025-07-30 10:43:30,014 minium.Conn2448 connection#668 __on_message] [2da30c8d-b9ca-41b3-94d6-0a5eedd19793]: Arg string terminates parameters early
[I 2025-07-30 10:43:30,014 minium minitest#897 capture] capture teardown.png
[I 2025-07-30 10:43:30,014 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 2da30c8d-b9ca-41b3-94d6-0a5eedd19793
[D 2025-07-30 10:43:30,014 minium.Conn2448 connection#427 _safely_send] SEND > [2358795752448]{"id":"9d2d0e84-20b7-4faa-b096-1d1222450f64","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:43:30,015 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"0b2d930e-c59a-4495-a8d3-64939307309b","error":{"message":"Arg string terminates parameters early"}}
[E 2025-07-30 10:43:30,015 minium.Conn2448 connection#668 __on_message] [0b2d930e-c59a-4495-a8d3-64939307309b]: Arg string terminates parameters early
[I 2025-07-30 10:43:30,015 minium.Conn2448 connection#704 _handle_async_msg] received async msg: 0b2d930e-c59a-4495-a8d3-64939307309b
[D 2025-07-30 10:43:30,094 minium.Conn2448 connection#660 __on_message] RECV < [2358795752448]{"id":"9d2d0e84-20b7-4faa-b096-1d1222450f64","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgXEWVP35O3bWX12/Jvi9kDyEIhCAGFWWPiqyCMsogCCPDuOBvZhxFhxHX74jOMOCCjIooyCKyKzCAiBCEhISQBMhGlpflvZe39OvlrlW/P+re29W3b/frl8UEUh/Ifaer69atut316XM+tVy0HA8AAAARGWPSlra0pb2f7YhlJCQkJA4ECAAgYvRa2tKWtrT3ry19GQkJiQMLNbIOUGxGKT2g5Utb2tLeR5sQ8rbRZaJCJSQk3u4QA599hDp0lqEgyUVC4p0H0SvZx6L2nmUkuUhIHA7Yd7oRWAYRIuJoaAdXbTp/UFeAYeWXtrSlfUBtcSiomfy84yPicK81PF2mGf9F+jgSEm9rNOOzDMuvaTZiaswdk
[I 2025-07-30 10:43:30,096 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:43:30,096 minium basenative#63 wrapper] call BaseNative.get_start_up end 
