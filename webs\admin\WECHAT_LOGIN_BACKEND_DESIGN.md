# 微信扫码登录后台服务器设计方案

## 🎯 技术架构

### 1. 技术栈选择
- **Node.js + Express** 或 **Python + FastAPI** 或 **Java + Spring Boot**
- **Redis** - 存储二维码状态和临时数据
- **MySQL/PostgreSQL** - 存储用户数据和微信绑定关系
- **微信开放平台 API** - 获取用户信息

### 2. 数据库设计

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    email VARCHAR(100),
    password_hash VARCHAR(255),
    avatar VARCHAR(500),
    status TINYINT DEFAULT 1, -- 1:正常 0:禁用
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 微信用户绑定表
CREATE TABLE wechat_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    openid VARCHAR(100) UNIQUE NOT NULL,
    unionid VARCHAR(100),
    nickname VARCHAR(100),
    avatar VARCHAR(500),
    gender TINYINT, -- 1:男 2:女 0:未知
    city VARCHAR(50),
    province VARCHAR(50),
    country VARCHAR(50),
    access_token VARCHAR(500),
    refresh_token VARCHAR(500),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_openid (openid),
    INDEX idx_unionid (unionid)
);

-- 管理员权限表
CREATE TABLE admin_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    openid VARCHAR(100),
    permission_level ENUM('super_admin', 'admin', 'operator') DEFAULT 'operator',
    granted_by BIGINT, -- 授权人ID
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL, -- 权限过期时间，NULL表示永不过期
    status TINYINT DEFAULT 1, -- 1:有效 0:无效
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    INDEX idx_openid (openid),
    INDEX idx_user_id (user_id)
);

-- 登录日志表
CREATE TABLE login_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    openid VARCHAR(100),
    login_type ENUM('password', 'wechat') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('success', 'failed') NOT NULL,
    failure_reason VARCHAR(255),
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_login_time (login_time)
);
```

### 3. Redis 数据结构

```redis
# 二维码状态存储
qr_code:{key} = {
    "status": "waiting|scanned|confirmed|expired",
    "openid": "用户openid",
    "user_info": "用户信息JSON",
    "created_at": "创建时间",
    "expires_at": "过期时间"
}

# 微信access_token缓存
wechat_token:{openid} = {
    "access_token": "访问令牌",
    "refresh_token": "刷新令牌",
    "expires_at": "过期时间"
}

# 登录会话
session:{token} = {
    "user_id": "用户ID",
    "openid": "微信openid",
    "login_type": "登录方式",
    "created_at": "创建时间",
    "expires_at": "过期时间"
}
```

## 🔧 API 接口设计

### 1. 生成二维码接口

```javascript
// POST /api/auth/wechat/qrcode
{
  "scene": "admin_login",
  "width": 430,
  "autoColor": false,
  "lineColor": {"r": 0, "g": 0, "b": 0}
}

// Response
{
  "success": true,
  "data": {
    "qrCodeUrl": "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=xxx",
    "key": "qr_12345678",
    "expiresIn": 300
  }
}
```

### 2. 检查扫码状态接口

```javascript
// GET /api/auth/wechat/scan-status/{key}

// Response
{
  "success": true,
  "data": {
    "status": "scanned", // waiting|scanned|confirmed|success|expired
    "userInfo": {
      "openid": "xxx",
      "nickname": "用户昵称",
      "avatar": "头像URL"
    },
    "token": "jwt_token" // 仅在status为success时返回
  }
}
```

### 3. 微信回调接口

```javascript
// POST /api/auth/wechat/callback
{
  "code": "微信授权码",
  "state": "状态参数"
}

// Response
{
  "success": true,
  "data": {
    "openid": "用户openid",
    "userInfo": "用户信息",
    "hasPermission": true
  }
}
```

## 💻 核心代码实现

### 1. Node.js + Express 实现

```javascript
const express = require('express');
const redis = require('redis');
const mysql = require('mysql2/promise');
const axios = require('axios');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

const app = express();
const redisClient = redis.createClient();

// 微信配置
const WECHAT_CONFIG = {
  appId: process.env.WECHAT_APP_ID,
  appSecret: process.env.WECHAT_APP_SECRET,
  redirectUri: process.env.WECHAT_REDIRECT_URI
};

// 生成二维码
app.post('/api/auth/wechat/qrcode', async (req, res) => {
  try {
    const qrKey = `qr_${uuidv4()}`;
    const expiresIn = 300; // 5分钟
    
    // 获取微信access_token
    const accessToken = await getWechatAccessToken();
    
    // 创建临时二维码
    const qrResponse = await axios.post(
      `https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=${accessToken}`,
      {
        expire_seconds: expiresIn,
        action_name: "QR_STR_SCENE",
        action_info: {
          scene: {
            scene_str: qrKey
          }
        }
      }
    );
    
    if (qrResponse.data.errcode) {
      throw new Error(qrResponse.data.errmsg);
    }
    
    const ticket = qrResponse.data.ticket;
    const qrCodeUrl = `https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=${ticket}`;
    
    // 存储二维码状态到Redis
    await redisClient.setex(
      `qr_code:${qrKey}`,
      expiresIn,
      JSON.stringify({
        status: 'waiting',
        created_at: new Date().toISOString(),
        expires_at: new Date(Date.now() + expiresIn * 1000).toISOString()
      })
    );
    
    res.json({
      success: true,
      data: {
        qrCodeUrl,
        key: qrKey,
        expiresIn
      }
    });
  } catch (error) {
    console.error('生成二维码失败:', error);
    res.status(500).json({
      success: false,
      message: '生成二维码失败'
    });
  }
});

// 检查扫码状态
app.get('/api/auth/wechat/scan-status/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const qrData = await redisClient.get(`qr_code:${key}`);
    
    if (!qrData) {
      return res.json({
        success: true,
        data: { status: 'expired' }
      });
    }
    
    const data = JSON.parse(qrData);
    
    // 检查是否过期
    if (new Date() > new Date(data.expires_at)) {
      await redisClient.del(`qr_code:${key}`);
      return res.json({
        success: true,
        data: { status: 'expired' }
      });
    }
    
    // 如果登录成功，生成JWT token
    if (data.status === 'confirmed' && data.openid) {
      const user = await getUserByOpenid(data.openid);
      if (user && await checkAdminPermission(data.openid)) {
        const token = jwt.sign(
          { 
            userId: user.id, 
            openid: data.openid,
            loginType: 'wechat'
          },
          process.env.JWT_SECRET,
          { expiresIn: '24h' }
        );
        
        // 记录登录日志
        await logLogin(user.id, data.openid, 'wechat', req.ip, req.get('User-Agent'), 'success');
        
        // 清除二维码数据
        await redisClient.del(`qr_code:${key}`);
        
        return res.json({
          success: true,
          data: {
            status: 'success',
            userInfo: {
              id: user.id,
              username: user.username,
              nickname: data.user_info?.nickname,
              avatar: data.user_info?.avatar || user.avatar
            },
            token
          }
        });
      }
    }
    
    res.json({
      success: true,
      data: {
        status: data.status,
        userInfo: data.user_info
      }
    });
  } catch (error) {
    console.error('检查扫码状态失败:', error);
    res.status(500).json({
      success: false,
      message: '检查状态失败'
    });
  }
});

// 微信回调处理
app.post('/api/auth/wechat/callback', async (req, res) => {
  try {
    const { code, state } = req.body;
    
    // 用授权码换取access_token
    const tokenResponse = await axios.get(
      `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${WECHAT_CONFIG.appId}&secret=${WECHAT_CONFIG.appSecret}&code=${code}&grant_type=authorization_code`
    );
    
    if (tokenResponse.data.errcode) {
      throw new Error(tokenResponse.data.errmsg);
    }
    
    const { access_token, openid, refresh_token, expires_in } = tokenResponse.data;
    
    // 获取用户信息
    const userInfoResponse = await axios.get(
      `https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}&lang=zh_CN`
    );
    
    if (userInfoResponse.data.errcode) {
      throw new Error(userInfoResponse.data.errmsg);
    }
    
    const userInfo = userInfoResponse.data;
    
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(openid);
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '您没有访问后台管理系统的权限'
      });
    }
    
    // 更新二维码状态
    if (state && state.startsWith('qr_')) {
      const qrData = await redisClient.get(`qr_code:${state}`);
      if (qrData) {
        const data = JSON.parse(qrData);
        data.status = 'confirmed';
        data.openid = openid;
        data.user_info = userInfo;
        
        await redisClient.setex(
          `qr_code:${state}`,
          300,
          JSON.stringify(data)
        );
      }
    }
    
    // 保存或更新微信用户信息
    await saveWechatUser(openid, userInfo, access_token, refresh_token, expires_in);
    
    res.json({
      success: true,
      data: {
        openid,
        userInfo,
        hasPermission: true
      }
    });
  } catch (error) {
    console.error('微信回调处理失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 辅助函数
async function getWechatAccessToken() {
  // 从缓存获取或重新申请access_token
  const cached = await redisClient.get('wechat_access_token');
  if (cached) {
    return cached;
  }
  
  const response = await axios.get(
    `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WECHAT_CONFIG.appId}&secret=${WECHAT_CONFIG.appSecret}`
  );
  
  if (response.data.errcode) {
    throw new Error(response.data.errmsg);
  }
  
  const { access_token, expires_in } = response.data;
  await redisClient.setex('wechat_access_token', expires_in - 60, access_token);
  
  return access_token;
}

async function checkAdminPermission(openid) {
  const connection = await mysql.createConnection(DB_CONFIG);
  
  try {
    const [rows] = await connection.execute(
      'SELECT * FROM admin_permissions WHERE openid = ? AND status = 1 AND (expires_at IS NULL OR expires_at > NOW())',
      [openid]
    );
    
    return rows.length > 0;
  } finally {
    await connection.end();
  }
}

async function getUserByOpenid(openid) {
  const connection = await mysql.createConnection(DB_CONFIG);
  
  try {
    const [rows] = await connection.execute(
      'SELECT u.* FROM users u JOIN wechat_users wu ON u.id = wu.user_id WHERE wu.openid = ?',
      [openid]
    );
    
    return rows[0] || null;
  } finally {
    await connection.end();
  }
}

async function saveWechatUser(openid, userInfo, accessToken, refreshToken, expiresIn) {
  const connection = await mysql.createConnection(DB_CONFIG);
  
  try {
    await connection.beginTransaction();
    
    // 检查用户是否存在
    const [existingUsers] = await connection.execute(
      'SELECT * FROM wechat_users WHERE openid = ?',
      [openid]
    );
    
    if (existingUsers.length > 0) {
      // 更新现有用户
      await connection.execute(
        'UPDATE wechat_users SET nickname = ?, avatar = ?, access_token = ?, refresh_token = ?, expires_at = ?, updated_at = NOW() WHERE openid = ?',
        [
          userInfo.nickname,
          userInfo.headimgurl,
          accessToken,
          refreshToken,
          new Date(Date.now() + expiresIn * 1000),
          openid
        ]
      );
    } else {
      // 创建新用户
      const [userResult] = await connection.execute(
        'INSERT INTO users (username, avatar, status, role) VALUES (?, ?, 1, "user")',
        [`wechat_${openid}`, userInfo.headimgurl]
      );
      
      await connection.execute(
        'INSERT INTO wechat_users (user_id, openid, unionid, nickname, avatar, gender, city, province, country, access_token, refresh_token, expires_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          userResult.insertId,
          openid,
          userInfo.unionid,
          userInfo.nickname,
          userInfo.headimgurl,
          userInfo.sex,
          userInfo.city,
          userInfo.province,
          userInfo.country,
          accessToken,
          refreshToken,
          new Date(Date.now() + expiresIn * 1000)
        ]
      );
    }
    
    await connection.commit();
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    await connection.end();
  }
}

async function logLogin(userId, openid, loginType, ipAddress, userAgent, status, failureReason = null) {
  const connection = await mysql.createConnection(DB_CONFIG);
  
  try {
    await connection.execute(
      'INSERT INTO login_logs (user_id, openid, login_type, ip_address, user_agent, status, failure_reason) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [userId, openid, loginType, ipAddress, userAgent, status, failureReason]
    );
  } finally {
    await connection.end();
  }
}

module.exports = app;
```

### 2. 权限管理接口

```javascript
// 添加管理员权限
app.post('/api/admin/permissions', authenticateAdmin, async (req, res) => {
  try {
    const { openid, permissionLevel, expiresAt } = req.body;
    const grantedBy = req.user.userId;
    
    const connection = await mysql.createConnection(DB_CONFIG);
    
    await connection.execute(
      'INSERT INTO admin_permissions (user_id, openid, permission_level, granted_by, expires_at) VALUES ((SELECT user_id FROM wechat_users WHERE openid = ?), ?, ?, ?, ?)',
      [openid, openid, permissionLevel, grantedBy, expiresAt]
    );
    
    await connection.end();
    
    res.json({
      success: true,
      message: '权限添加成功'
    });
  } catch (error) {
    console.error('添加权限失败:', error);
    res.status(500).json({
      success: false,
      message: '添加权限失败'
    });
  }
});

// 撤销管理员权限
app.delete('/api/admin/permissions/:openid', authenticateAdmin, async (req, res) => {
  try {
    const { openid } = req.params;
    
    const connection = await mysql.createConnection(DB_CONFIG);
    
    await connection.execute(
      'UPDATE admin_permissions SET status = 0 WHERE openid = ?',
      [openid]
    );
    
    await connection.end();
    
    res.json({
      success: true,
      message: '权限撤销成功'
    });
  } catch (error) {
    console.error('撤销权限失败:', error);
    res.status(500).json({
      success: false,
      message: '撤销权限失败'
    });
  }
});
```

## 🔒 安全考虑

### 1. 权限控制
- 只有预先授权的微信账号才能登录后台
- 支持多级权限管理（超级管理员、管理员、操作员）
- 权限可设置过期时间

### 2. 数据安全
- 敏感信息加密存储
- 使用HTTPS传输
- JWT token设置合理过期时间
- 记录详细的登录日志

### 3. 防护措施
- 二维码有效期限制（5分钟）
- 登录频率限制
- IP白名单（可选）
- 异常登录检测

## 📋 部署清单

### 1. 环境变量配置
```bash
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
WECHAT_REDIRECT_URI=https://your-domain.com/api/auth/wechat/callback
JWT_SECRET=your_jwt_secret
REDIS_URL=redis://localhost:6379
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=password
DB_NAME=nannan_kitchen
```

### 2. 微信开放平台配置
- 创建网站应用
- 配置授权回调域名
- 获取AppID和AppSecret
- 设置接口权限

### 3. 服务器配置
- 安装Node.js、Redis、MySQL
- 配置HTTPS证书
- 设置防火墙规则
- 配置日志轮转

这个方案提供了完整的微信扫码登录功能，包括前端组件、后端API、数据库设计和安全考虑。您可以根据实际需求进行调整和优化。
