[I 2025-07-30 10:40:15,197 minium minitest#432 _miniSetUp] =========Current case: test_01_api_connectivity_and_response=========
[I 2025-07-30 10:40:15,197 minium minitest#435 _miniSetUp] package info: E:.wx-nan.comprehensive_functional_test, case info: ComprehensiveFunctionalTest.test_01_api_connectivity_and_response
[I 2025-07-30 10:40:15,198 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 10:40:15,198 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 10:40:15,198 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 10:40:15,198 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 10:40:15,198 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 10:40:15,252 minium minitest#897 capture] capture setup.png
[D 2025-07-30 10:40:15,253 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"5edcb5a9-f15f-40c8-8d74-19e99997508e","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:15,336 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"5edcb5a9-f15f-40c8-8d74-19e99997508e","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsvXmgXEWZNv6+dbZe7p59h5CEsIQguwijOOxxQxFRURkFYWD8dJz5zQyOyji4zPjNMP6GkVHAURFFEQZBAiM4gIoIQgIhJAGykeUmNzc3d+nby1nr/f6oc05Xnz7dt+9NQhJSD6Tu29V16lSd7nr6fZ+qOgdt1wcAAEBEIlK2spWt7H1sxyyjoKCgsD/AAAAR49fKVraylb1vbeXLKCgo7F/osbWfYjPO+X6tX9nKVvZe2oyxQ0aXiStVUFA41CEHPnsJfewiY0GRi4LCmw+yV7KXVU2cZRS5KCgcDth7upFYBhFi4mhqh2dtuXzYVoBxlVe2spW9X215KqiV8mLgI+J4zzU+XaYV/0X5OAoKhzRa8VnG5de0G
[I 2025-07-30 10:40:15,338 minium minitest#487 _miniSetUp] =========case: test_01_api_connectivity_and_response start=========
[I 2025-07-30 10:40:16,339 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 10:40:16,384 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"cda7a96f-060f-4506-a509-4deb064f05a0","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:16,392 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"cda7a96f-060f-4506-a509-4deb064f05a0","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:16,393 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:16,394 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"f1a4963c-4c23-4788-b16d-49e0a5b1185b","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:16,396 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"f1a4963c-4c23-4788-b16d-49e0a5b1185b","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:40:16,397 minium.Conn6976 connection#427 _safely_send] ASYNC_SEND > [2264960766976]{"id":"4ae7248d-0bde-4fe9-befd-06d3a011e156","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:40:16,398 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/home/<USER>"}]}}
[D 2025-07-30 10:40:16,406 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"4ae7248d-0bde-4fe9-befd-06d3a011e156","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 10:40:16,406 minium.Conn6976 connection#704 _handle_async_msg] received async msg: 4ae7248d-0bde-4fe9-befd-06d3a011e156
[D 2025-07-30 10:40:31,412 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"fbf4966a-caa5-46dd-a8c9-d1a9dfcf1825","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:31,414 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"fbf4966a-caa5-46dd-a8c9-d1a9dfcf1825","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:31,414 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:31,415 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"5964e3be-9d8b-444a-83c4-a474628eea1e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:31,418 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"5964e3be-9d8b-444a-83c4-a474628eea1e","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[D 2025-07-30 10:40:34,419 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"53f5d347-e47b-4271-b6a7-4aa083b9bea9","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 10:40:34,421 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"53f5d347-e47b-4271-b6a7-4aa083b9bea9","result":{"pageId":2,"path":"pages/home/<USER>","query":{}}}
[D 2025-07-30 10:40:34,422 minium.App1680 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 10:40:34,422 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"5cb4061e-e7f7-4095-a8c5-79f1cc45bf8d","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 10:40:34,424 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"5cb4061e-e7f7-4095-a8c5-79f1cc45bf8d","result":{"result":{"pageId":2,"path":"pages/home/<USER>","query":{},"renderer":"webview"}}}
[I 2025-07-30 10:40:34,424 minium page#716 _get_elements_by_css] try to get elements: page
[D 2025-07-30 10:40:34,425 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"1d1f67ce-acff-4531-bff7-d1ed034912ec","method":"Page.getElements","params":{"selector":"page","pageId":2}}
[D 2025-07-30 10:40:34,427 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"1d1f67ce-acff-4531-bff7-d1ed034912ec","result":{"elements":[{"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","tagName":"body","nodeId":"d143550e"}]}}
[I 2025-07-30 10:40:34,428 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.CustomElement object at 0x0000020F5A2EBE00>]
[D 2025-07-30 10:40:34,428 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"9bff1f6d-b3ae-4485-9d5c-68ff1d9c9fb3","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"a46f413b-2cb2-4151-8174-b9d9ff7ba433","pageId":2,"nodeId":"d143550e"}}
[D 2025-07-30 10:40:34,430 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"9bff1f6d-b3ae-4485-9d5c-68ff1d9c9fb3","result":{"properties":["伦少，欢迎\n今日菜单已开放点菜，快来选你喜欢的菜吧～\n小李点了菜品：小菜\n推荐菜单\n热门菜品推荐\n去点菜 \n红烧肉\n小菜\n留言\n（暂无留言）\n更多 \n暂无留言\n点击开始第一条留言"]}}
[I 2025-07-30 10:40:34,431 minium page#716 _get_elements_by_css] try to get elements: .error, .network-error, .empty-state
[D 2025-07-30 10:40:34,432 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"739e2350-3617-4385-8647-567142e9883f","method":"Page.getElements","params":{"selector":".error, .network-error, .empty-state","pageId":2}}
[D 2025-07-30 10:40:34,440 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"739e2350-3617-4385-8647-567142e9883f","result":{"elements":[]}}
[W 2025-07-30 10:40:34,440 minium page#747 _get_elements_by_css] Could not found any element '.error, .network-error, .empty-state' you need
[I 2025-07-30 10:40:34,441 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 10:40:34,441 minium minitest#799 _miniTearDown] =========Current case Down: test_01_api_connectivity_and_response=========
[I 2025-07-30 10:40:34,441 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 10:40:34,442 minium.Conn6976 connection#427 _safely_send] SEND > [2264960766976]{"id":"1d7280e5-cc24-4e43-8c27-0bd063be4861","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 10:40:34,539 minium.Conn6976 connection#660 __on_message] RECV < [2264960766976]{"id":"1d7280e5-cc24-4e43-8c27-0bd063be4861","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsfXmgXEWV9zl1115evyXJy55AyA4hEQhBDI4w7HFDIoKgIgOC8jmiM9/MoKLj4DLjNyIzDIwCbogiCCJgYEQHUBFBSEgISYBsZHlZ3nt5S79e7lr1/VH33q6+fbtfv5fEJKR+kPtOV9etW3W769fn/Gq5aDkeAAAAIjLGpC1taUv7ANsRy0hISEgcDBAAQMTotbSlLW1pH1hb+jISEhIHF2pkHaTYjFJ6UMuXtrSlvZ82IeSI0WWiQiUkJI50iIHPfkIdPstwkOQiIfHWg+iV7GdRo2cZSS4SEkcD9p9uBJZBhIg4GtrBVZvOH9QVYET5pS1taR9UWxwKaiY/7/iIONJrjUyXacZ/kT6OhMQRjWZ8lhH5Nc1GT
[I 2025-07-30 10:40:34,540 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 10:40:34,540 minium basenative#63 wrapper] call BaseNative.get_start_up end 
