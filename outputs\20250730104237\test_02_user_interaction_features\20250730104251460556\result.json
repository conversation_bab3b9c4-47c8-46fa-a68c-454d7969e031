{"case_name": "test_02_user_interaction_features", "run_time": "20250730 10:42:51", "test_type": "StableFunctionalTest", "case_doc": "测试用户交互功能", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843371.5539205, "is_failure": false, "is_error": false, "module": "E:.wx-nan.stable_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/mine/index", "path": "images\\setup.png", "ts": 1753843371, "datetime": "2025-07-30 10:42:51", "use_region": false}, {"name": "teardown", "url": "/pages/mine/index", "path": "images\\teardown.png", "ts": 1753843394, "datetime": "2025-07-30 10:43:14", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843394.1278045, "appId": "", "appName": "", "source": {"code": ["    def test_02_user_interaction_features(self):\n", "        \"\"\"测试用户交互功能\"\"\"\n", "        print(\"🧪 测试用户交互功能\")\n", "        \n", "        try:\n", "            # 回到个人中心测试功能入口\n", "            self.app.switch_tab(\"/pages/mine/index\")\n", "            time.sleep(2)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 查找各种功能入口\n", "            clickable_elements = current_page.get_elements(\"view[bindtap], button\")\n", "            \n", "            found_features = {\n", "                \"用户关联\": <PERSON><PERSON><PERSON>,\n", "                \"我的菜品\": <PERSON><PERSON><PERSON>,\n", "                \"新增菜品\": <PERSON><PERSON><PERSON>,\n", "                \"通知中心\": <PERSON><PERSON><PERSON>,\n", "                \"消息\": False\n", "            }\n", "            \n", "            for element in clickable_elements:\n", "                try:\n", "                    text = element.inner_text.strip()\n", "                    if text:\n", "                        for feature in found_features:\n", "                            if feature in text:\n", "                                found_features[feature] = True\n", "                                self.log_result(f\"功能入口-{feature}\", True, f\"找到入口: {text}\")\n", "                except:\n", "                    continue\n", "            \n", "            # 记录未找到的功能\n", "            for feature, found in found_features.items():\n", "                if not found:\n", "                    self.log_result(f\"功能入口-{feature}\", False, \"未找到入口\")\n", "            \n", "            # 尝试点击第一个可点击元素测试交互\n", "            if clickable_elements:\n", "                try:\n", "                    first_element = clickable_elements[0]\n", "                    element_text = first_element.inner_text.strip()\n", "                    first_element.click()\n", "                    time.sleep(2)\n", "                    \n", "                    # 检查是否有页面变化\n", "                    new_page = self.app.get_current_page()\n", "                    if new_page.path != current_page.path:\n", "                        self.log_result(\"页面交互跳转\", True, f\"从{current_page.path}跳转到{new_page.path}\")\n", "                        \n", "                        # 返回个人中心\n", "                        self.app.navigate_back()\n", "                        time.sleep(1)\n", "                    else:\n", "                        self.log_result(\"页面交互响应\", True, f\"点击了'{element_text}'，页面有响应\")\n", "                        \n", "                except Exception as e:\n", "                    self.log_result(\"交互测试\", False, f\"交互异常: {e}\")\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"用户交互测试\", False, f\"异常: {e}\")\n"], "start": 106}, "filename": "result.json"}