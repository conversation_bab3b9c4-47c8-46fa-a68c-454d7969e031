{"case_name": "test_01_enhanced_order_flow_with_cart", "run_time": "20250730 11:14:13", "test_type": "EnhancedFunctionalTest", "case_doc": "测试增强的订餐流程（重点测试购物车）", "success": true, "failures": "", "errors": "", "start_timestamp": 1753845253.685094, "is_failure": false, "is_error": false, "module": "E:.wx-nan.testing.enhanced_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "", "path": "images\\setup.png", "ts": 1753845253, "datetime": "2025-07-30 11:14:13", "use_region": false}, {"name": "assertIn-success", "url": "/pages/order/index", "path": "images\\assertIn-success.png", "ts": 1753845272, "datetime": "2025-07-30 11:14:32", "use_region": false}, {"name": "teardown", "url": "/pages/order/index", "path": "images\\teardown.png", "ts": 1753845284, "datetime": "2025-07-30 11:14:44", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [{"name": "assertIn", "ret": true, "msg": null, "img": "assertIn-success", "wxml": ""}], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753845284.9529614, "appId": "", "appName": "", "source": {"code": ["    def test_01_enhanced_order_flow_with_cart(self):\n", "        \"\"\"测试增强的订餐流程（重点测试购物车）\"\"\"\n", "        print(\"🧪 测试增强的订餐流程（重点测试购物车）\")\n", "        \n", "        try:\n", "            # 导航到订餐页面\n", "            self.app.switch_tab(\"/pages/order/index\")\n", "            time.sleep(3)\n", "            \n", "            order_page = self.app.get_current_page()\n", "            self.assertIn(\"order\", order_page.path)\n", "            \n", "            # 详细测试菜品数据\n", "            self._test_detailed_dish_data(order_page)\n", "            \n", "            # 重点测试购物车功能\n", "            self._test_enhanced_cart_functionality(order_page)\n", "            \n", "            # 测试订单提交流程\n", "            self._test_order_submission_flow()\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"增强订餐流程测试\", False, f\"测试异常: {e}\", critical=True)\n"], "start": 58}, "filename": "result.json"}