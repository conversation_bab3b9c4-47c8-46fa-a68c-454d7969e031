[I 2025-07-30 11:16:30,043 minium minitest#432 _miniSetUp] =========Current case: test_02_user_connection_flow=========
[I 2025-07-30 11:16:30,044 minium minitest#435 _miniSetUp] package info: E:.wx-nan.testing.complete_functional_test, case info: CompleteFunctionalTest.test_02_user_connection_flow
[I 2025-07-30 11:16:30,044 minium basenative#59 wrapper] call BaseNative.check_connection
[I 2025-07-30 11:16:30,044 minium basenative#63 wrapper] call BaseNative.check_connection end True
[I 2025-07-30 11:16:30,044 minium basenative#59 wrapper] call BaseNative.back_to_miniprogram
[I 2025-07-30 11:16:30,045 minium basenative#794 back_to_miniprogram] try back_to_miniprogram 3
[I 2025-07-30 11:16:30,045 minium basenative#63 wrapper] call BaseNative.back_to_miniprogram end 
[I 2025-07-30 11:16:30,046 minium minitest#897 capture] capture setup.png
[D 2025-07-30 11:16:30,046 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"1791bd97-139c-4d7a-8689-69e6907b5c1d","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:30,122 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"1791bd97-139c-4d7a-8689-69e6907b5c1d","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAALUCAIAAAB/wuV0AAAAAXNSR0IArs4c6QAAIABJREFUeJzs3XlcE3f+P/BXEpCgCCgBIiIeWKgHAkq9peuBt67VWls81gO21UXrT9rVFqpfLbiyra5Fqm3BY7HaWpVaL0A8urZeFTlEa6H1Rg2XgIKEI8nvj0kmkwMI1OGI7+ejD5tMJjOfAPPK+/P5zCSCyqpqEEIIb4TN3QBCiJmjlCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL0oZQgi/KGUIIfyilCGE8ItShhDCL
[I 2025-07-30 11:16:30,123 minium minitest#487 _miniSetUp] =========case: test_02_user_connection_flow start=========
[I 2025-07-30 11:16:31,124 minium minitest#788 _callTestMethod] call test method
[D 2025-07-30 11:16:31,125 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"94684aa4-9888-497a-98a5-8222ab8f0523","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:31,129 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"94684aa4-9888-497a-98a5-8222ab8f0523","result":{"pageId":13,"path":"pages/login/index","query":{}}}
[D 2025-07-30 11:16:31,129 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:31,130 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"75f9a0be-f24c-4b1c-82ea-e7cec8f1ce6c","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:31,136 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"75f9a0be-f24c-4b1c-82ea-e7cec8f1ce6c","result":{"result":{"pageId":13,"path":"pages/login/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:31,137 minium page#716 _get_elements_by_css] try to get elements: .tab-item[data-type='password'], .password-tab
[D 2025-07-30 11:16:31,137 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"e8d4bd4b-676a-482a-b4fd-7d13e2809267","method":"Page.getElements","params":{"selector":".tab-item[data-type='password'], .password-tab","pageId":13}}
[D 2025-07-30 11:16:31,147 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"e8d4bd4b-676a-482a-b4fd-7d13e2809267","result":{"elements":[{"elementId":"b4d1adec-c590-4af3-ac51-085170c319d5","tagName":"view"}]}}
[I 2025-07-30 11:16:31,147 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x000002667472BE00>]
[D 2025-07-30 11:16:31,147 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"feadc43a-45cf-4525-b648-f9024fc400b3","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"b4d1adec-c590-4af3-ac51-085170c319d5","pageId":13}}
[D 2025-07-30 11:16:31,162 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"feadc43a-45cf-4525-b648-f9024fc400b3","result":{"styles":["auto"]}}
[D 2025-07-30 11:16:31,162 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"2f1fdb32-8a11-486a-9043-a7cf2af073b5","method":"Element.tap","params":{"elementId":"b4d1adec-c590-4af3-ac51-085170c319d5","pageId":13}}
[D 2025-07-30 11:16:31,228 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"2f1fdb32-8a11-486a-9043-a7cf2af073b5","result":{"pageX":262.5,"pageY":170,"clientX":262.5,"clientY":170}}
[I 2025-07-30 11:16:33,230 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='用户名'], input[placeholder*='账号']
[D 2025-07-30 11:16:33,230 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"5b11ad51-0532-4bf7-8607-0ba6abf6f464","method":"Page.getElements","params":{"selector":"input[placeholder*='\u7528\u6237\u540d'], input[placeholder*='\u8d26\u53f7']","pageId":13}}
[D 2025-07-30 11:16:33,236 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"5b11ad51-0532-4bf7-8607-0ba6abf6f464","result":{"elements":[{"elementId":"6f043ae6-4d8f-4bd0-9614-17ec6d5e7df6","tagName":"input"}]}}
[I 2025-07-30 11:16:33,236 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.FormElement object at 0x000002667472B770>]
[I 2025-07-30 11:16:33,237 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='密码']
[D 2025-07-30 11:16:33,238 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"fee045f0-de31-4cfc-af6e-95901b603c45","method":"Page.getElements","params":{"selector":"input[placeholder*='\u5bc6\u7801']","pageId":13}}
[D 2025-07-30 11:16:33,241 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"fee045f0-de31-4cfc-af6e-95901b603c45","result":{"elements":[{"elementId":"e7f7a828-04c0-49bd-b776-50491c4f95e4","tagName":"input"}]}}
[I 2025-07-30 11:16:33,242 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.FormElement object at 0x0000026674761D10>]
[D 2025-07-30 11:16:33,242 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"fa4f7dcf-e64f-4a1c-b418-8e8654397e0a","method":"Element.callFunction","params":{"functionName":"input.input","args":["13800000001",false],"elementId":"6f043ae6-4d8f-4bd0-9614-17ec6d5e7df6","pageId":13}}
[D 2025-07-30 11:16:33,253 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"fa4f7dcf-e64f-4a1c-b418-8e8654397e0a","result":{}}
[D 2025-07-30 11:16:33,253 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"dbfcdb73-bf6e-452e-99b8-df48b9526ab9","method":"Element.callFunction","params":{"functionName":"input.input","args":["test123456",false],"elementId":"e7f7a828-04c0-49bd-b776-50491c4f95e4","pageId":13}}
[D 2025-07-30 11:16:33,259 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"dbfcdb73-bf6e-452e-99b8-df48b9526ab9","result":{}}
[I 2025-07-30 11:16:33,260 minium page#716 _get_elements_by_css] try to get elements: button[bindtap*='login'], .login-btn
[D 2025-07-30 11:16:33,260 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"1fce81a5-6382-415d-9d3d-fcccc5dde85b","method":"Page.getElements","params":{"selector":"button[bindtap*='login'], .login-btn","pageId":13}}
[D 2025-07-30 11:16:33,263 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"1fce81a5-6382-415d-9d3d-fcccc5dde85b","result":{"elements":[]}}
[W 2025-07-30 11:16:33,264 minium page#747 _get_elements_by_css] Could not found any element 'button[bindtap*='login'], .login-btn' you need
[D 2025-07-30 11:16:33,267 minium.Conn6128 connection#427 _safely_send] ASYNC_SEND > [2639063586128]{"id":"5fc528a1-b08c-49eb-89a0-505a03fd9c2e","method":"App.callWxMethod","params":{"method":"switchTab","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:16:33,273 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"switchTab_before_global","args":[{"url":"/pages/mine/index"}]}}
[D 2025-07-30 11:16:33,509 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"onAppRouteDone","args":[{"path":"pages/mine/index","query":{},"openType":"switchTab","timeStamp":1753845393505,"webviewId":3,"routeEventId":"3_1753845393292","renderer":"webview"},1753845393506]}}
[D 2025-07-30 11:16:33,509 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"5fc528a1-b08c-49eb-89a0-505a03fd9c2e","result":{"result":{"errMsg":"switchTab:ok"}}}
[I 2025-07-30 11:16:33,510 minium.App0832 app#1226 _on_route_changed] Route changed, {'name': 'onAppRouteDone', 'args': [{'path': 'pages/mine/index', 'query': {}, 'openType': 'switchTab', 'timeStamp': 1753845393505, 'webviewId': 3, 'routeEventId': '3_1753845393292', 'renderer': 'webview'}, 1753845393506]}
[I 2025-07-30 11:16:33,510 minium.Conn6128 connection#704 _handle_async_msg] received async msg: 5fc528a1-b08c-49eb-89a0-505a03fd9c2e
[D 2025-07-30 11:16:35,511 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"0c5132ab-4ca9-404d-ad7b-689cf54df991","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:35,513 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"0c5132ab-4ca9-404d-ad7b-689cf54df991","result":{"pageId":3,"path":"pages/mine/index","query":{}}}
[D 2025-07-30 11:16:35,513 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:35,514 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"f1e0c412-5885-4592-845f-59bc32037435","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:35,517 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"f1e0c412-5885-4592-845f-59bc32037435","result":{"result":{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:35,517 minium page#716 _get_elements_by_css] try to get elements: view[bindtap], button
[D 2025-07-30 11:16:35,517 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"04623392-715f-413e-9df1-d8788f966f50","method":"Page.getElements","params":{"selector":"view[bindtap], button","pageId":3}}
[D 2025-07-30 11:16:35,521 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"04623392-715f-413e-9df1-d8788f966f50","result":{"elements":[{"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","tagName":"button"},{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","tagName":"button"},{"elementId":"71327459-6375-4580-be0b-b903370354ec","tagName":"button"},{"elementId":"53f99b3e-7e59-4ced-ac46-0af2a89d7852","tagName":"button"},{"elementId":"85e522b0-51f1-4caf-9fd2-32433048c569","tagName":"button"},{"elementId":"aa3dc000-1de4-4c13-b078-4d2334d923da","tagName":"button"}]}}
[I 2025-07-30 11:16:35,521 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674761D10>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674763110>, <minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674723820>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000266747236F0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000266746F3BF0>, <minium.miniprogram.base_driver.element.BaseElement object at 0x00000266747038A0>]
[D 2025-07-30 11:16:35,522 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"ca3b8720-debd-46cf-8ba9-8ee4c2cb9b02","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"49b86b4e-3dd7-46d0-8435-0db739bc4cc6","pageId":3}}
[D 2025-07-30 11:16:35,524 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"ca3b8720-debd-46cf-8ba9-8ee4c2cb9b02","result":{"properties":["留言"]}}
[D 2025-07-30 11:16:35,525 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"9e93200d-d473-49bf-8f95-6db2a5ee010e","method":"Element.getDOMProperties","params":{"names":["innerText"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:16:35,528 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"9e93200d-d473-49bf-8f95-6db2a5ee010e","result":{"properties":["用户关联"]}}
[D 2025-07-30 11:16:35,528 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"beb8bf14-a7ab-4a75-b0b3-a91d10f6a7c4","method":"Element.getStyles","params":{"names":["pointer-events"],"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:16:35,531 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"beb8bf14-a7ab-4a75-b0b3-a91d10f6a7c4","result":{"styles":["auto"]}}
[D 2025-07-30 11:16:35,533 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"32bef056-0604-4f23-9b34-4140ff9d3e05","method":"Element.tap","params":{"elementId":"9a160ed5-adb2-4707-80dd-6fd3154d3721","pageId":3}}
[D 2025-07-30 11:16:35,591 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"navigateTo_before_global","args":[{"url":"/pages/user_connection/index"}]}}
[D 2025-07-30 11:16:35,628 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"32bef056-0604-4f23-9b34-4140ff9d3e05","result":{"pageX":187.5,"pageY":244,"clientX":187.5,"clientY":244}}
[D 2025-07-30 11:16:39,629 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"65554ad7-f077-4282-83b7-a4d27524f2dc","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:39,632 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"65554ad7-f077-4282-83b7-a4d27524f2dc","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:16:39,633 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:39,633 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"f4c2c78a-122f-4b81-b59d-4b45d99a78ee","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:39,635 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"f4c2c78a-122f-4b81-b59d-4b45d99a78ee","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:39,636 minium page#716 _get_elements_by_css] try to get elements: input[placeholder*='搜索'], .search-input
[D 2025-07-30 11:16:39,636 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"0032c29b-7b60-4d24-993a-53937526f68b","method":"Page.getElements","params":{"selector":"input[placeholder*='\u641c\u7d22'], .search-input","pageId":14}}
[D 2025-07-30 11:16:39,641 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"0032c29b-7b60-4d24-993a-53937526f68b","result":{"elements":[]}}
[W 2025-07-30 11:16:39,641 minium page#747 _get_elements_by_css] Could not found any element 'input[placeholder*='搜索'], .search-input' you need
[I 2025-07-30 11:16:39,642 minium page#716 _get_elements_by_css] try to get elements: .user-item, .user-card, .connection-item
[D 2025-07-30 11:16:39,642 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"4690ef42-66ea-4167-8cac-9b7142f3ac18","method":"Page.getElements","params":{"selector":".user-item, .user-card, .connection-item","pageId":14}}
[D 2025-07-30 11:16:39,646 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"4690ef42-66ea-4167-8cac-9b7142f3ac18","result":{"elements":[{"elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","tagName":"view"}]}}
[I 2025-07-30 11:16:39,647 minium page#749 _get_elements_by_css] find elements success: [<minium.miniprogram.base_driver.element.BaseElement object at 0x0000026674703F00>]
[I 2025-07-30 11:16:39,647 minium element#521 _get_elements_by_css] try to get elements: button[bindtap*='connect'], .connect-btn
[D 2025-07-30 11:16:39,650 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"f946fe62-521d-482b-9225-37a210ebc6ba","method":"Element.getElements","params":{"selector":"button[bindtap*='connect'], .connect-btn","elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","pageId":14}}
[D 2025-07-30 11:16:39,655 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"f946fe62-521d-482b-9225-37a210ebc6ba","result":{"elements":[]}}
[D 2025-07-30 11:16:39,656 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"631ac730-34e6-4821-aff7-47d00f4259dc","method":"Element.getProperties","params":{"names":["node_id"],"elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","pageId":14}}
[D 2025-07-30 11:16:39,659 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"631ac730-34e6-4821-aff7-47d00f4259dc","result":{"properties":[null]}}
[D 2025-07-30 11:16:39,660 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"2e99b7ca-c91b-473c-a618-8911d49ee8c5","method":"Element.getAttributes","params":{"names":["node_id"],"elementId":"02fd0160-5a42-460b-bed6-0a6c7e866b88","pageId":14}}
[D 2025-07-30 11:16:39,663 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"2e99b7ca-c91b-473c-a618-8911d49ee8c5","result":{"attributes":[null]}}
[W 2025-07-30 11:16:39,663 minium element#526 _get_elements_by_css] Could not found any element 'button[bindtap*='connect'], .connect-btn' you need
[D 2025-07-30 11:16:39,664 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:39,666 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"fbd11008-2761-44d5-8433-e0597251ea4e","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[]}}
[D 2025-07-30 11:16:39,669 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"fbd11008-2761-44d5-8433-e0597251ea4e","result":{"result":[{"pageId":3,"path":"pages/mine/index","query":{},"renderer":"webview"},{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}]}}
[I 2025-07-30 11:16:39,669 minium.App0832 app#971 navigate_back] NavigateBack from:/pages/user_connection/index
[D 2025-07-30 11:16:39,669 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"a60d44c9-c900-428a-b9e5-9f1928d857c4","method":"App.callWxMethod","params":{"method":"navigateBack","args":[{"delta":1}]}}
[D 2025-07-30 11:16:39,671 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"method":"App.bindingCalled","params":{"name":"navigateBack_before_global","args":[{"delta":1}]}}
[D 2025-07-30 11:16:39,676 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"a60d44c9-c900-428a-b9e5-9f1928d857c4","result":{"result":{}}}
[W 2025-07-30 11:16:44,687 minium.App0832 app#976 navigate_back] route has not change, may be navigate back fail
[D 2025-07-30 11:16:44,688 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"8c74e42c-9a9f-492d-9dff-69f367f86a70","method":"App.getCurrentPage","params":{}}
[D 2025-07-30 11:16:44,689 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"8c74e42c-9a9f-492d-9dff-69f367f86a70","result":{"pageId":14,"path":"pages/user_connection/index","query":{}}}
[D 2025-07-30 11:16:44,690 minium.App0832 app#618 _evaluate_js] evaluate js file getCurrentPages [ALL]
[D 2025-07-30 11:16:44,690 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"491896f2-9c47-48f8-b72f-0d520d8567ae","method":"App.callFunction","params":{"functionDeclaration":"function miniumGetCurrentPages(e){var r=getCurrentPages();return e&&(r=[r.pop()]),r=r.map((function(e){if(e){let{route:r}=e;const n=r.indexOf(\"__plugin__/\");return n>-1&&(r=\"plugin-private://\"+r.slice(n+11)),{pageId:e.__wxWebviewId__,path:r,query:e.options,renderer:e.renderer}}})),e?r.pop():r}","args":[true]}}
[D 2025-07-30 11:16:44,692 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"491896f2-9c47-48f8-b72f-0d520d8567ae","result":{"result":{"pageId":14,"path":"pages/user_connection/index","query":{},"renderer":"webview"}}}
[I 2025-07-30 11:16:45,770 minium minitest#796 _callTestMethod] end test method
[D 2025-07-30 11:16:45,771 minium minitest#799 _miniTearDown] =========Current case Down: test_02_user_connection_flow=========
[I 2025-07-30 11:16:45,771 minium minitest#897 capture] capture teardown.png
[D 2025-07-30 11:16:45,771 minium.Conn6128 connection#427 _safely_send] SEND > [2639063586128]{"id":"881233e0-b5e7-4d54-897c-6ec7fbf48533","method":"App.captureScreenshot","params":{}}
[D 2025-07-30 11:16:45,829 minium.Conn6128 connection#660 __on_message] RECV < [2639063586128]{"id":"881233e0-b5e7-4d54-897c-6ec7fbf48533","result":{"data":"iVBORw0KGgoAAAANSUhEUgAAAXcAAAKCCAIAAAClFQ6eAAAAAXNSR0IArs4c6QAAIABJREFUeJzsnX18FdWd/z/nzNzHPAEC3oBAQRCxFgO0QCvq+kRxkVjqFrt2XbGN0Ya2K9t22y4CL0G7tuv+bG1JBdOC626tWJc2LDXFSleqrUYJEW3RiiBgyIWEhzzdh7kzc35/zMOd+5TchEAmyffdvo6fe+7M3JnLuZ98z/ecOcNiigoAAGNMCEGaNGnS/axtlyEIgjgXcACMMfs1adKkSfevpliGIIhzC7eVe5yPNGnSQ0qfi1jGzvoQBDG4cLpDfyH3y1HIVghiaJD2W+4X0zkrlyFzIYihjXNkus8HSeZl4DxKT1pkOB5p0qSHsBbOqKKX+/Y6L5N//JK6IUU9BOFCnPnavPfpZVzTix5Tj/4iBMhNCGJQkfzB5p+QM
[I 2025-07-30 11:16:45,830 minium basenative#59 wrapper] call BaseNative.get_start_up
[I 2025-07-30 11:16:45,834 minium basenative#63 wrapper] call BaseNative.get_start_up end 
