{"case_name": "test_02_user_connection_flow", "run_time": "20250730 11:16:30", "test_type": "CompleteFunctionalTest", "case_doc": "测试用户关联流程", "success": true, "failures": "", "errors": "", "start_timestamp": 1753845390.1237392, "is_failure": false, "is_error": false, "module": "E:.wx-nan.testing.complete_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/mine/index", "path": "images\\setup.png", "ts": 1753845390, "datetime": "2025-07-30 11:16:30", "use_region": false}, {"name": "teardown", "url": "/pages/user_connection/index", "path": "images\\teardown.png", "ts": 1753845405, "datetime": "2025-07-30 11:16:45", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753845405.835777, "appId": "", "appName": "", "source": {"code": ["    def test_02_user_connection_flow(self):\n", "        \"\"\"测试用户关联流程\"\"\"\n", "        print(\"🧪 测试用户关联流程\")\n", "        \n", "        if len(self.test_users) < 2:\n", "            self.log_result(\"用户关联前置条件\", False, \"测试用户不足2个\", critical=True)\n", "            return\n", "        \n", "        try:\n", "            # 确保已登录\n", "            self._ensure_logged_in()\n", "            \n", "            # 导航到个人中心\n", "            self.app.switch_tab(\"/pages/mine/index\")\n", "            time.sleep(2)\n", "            \n", "            mine_page = self.app.get_current_page()\n", "            \n", "            # 查找用户关联入口\n", "            self._test_user_connection_entry(mine_page)\n", "            \n", "            # 测试用户搜索和关联\n", "            self._test_user_search_and_connect()\n", "            \n", "        except Exception as e:\n", "            self.log_result(\"用户关联流程测试\", False, f\"测试异常: {e}\", critical=True)\n"], "start": 267}, "filename": "result.json"}