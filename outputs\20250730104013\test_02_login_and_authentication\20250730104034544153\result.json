{"case_name": "test_02_login_and_authentication", "run_time": "20250730 10:40:34", "test_type": "ComprehensiveFunctionalTest", "case_doc": "测试登录和认证功能", "success": true, "failures": "", "errors": "", "start_timestamp": 1753843234.683915, "is_failure": false, "is_error": false, "module": "E:.wx-nan.comprehensive_functional_test", "package": "__main__", "failed_line_num": -1, "device": {"system_info": {"brand": "devtools", "model": "iPhone X", "system": "iOS 10.0.1", "platform": "devtools", "benchmarkLevel": -1, "pixelRatio": 3, "screenWidth": 375, "screenHeight": 812, "windowWidth": 375, "windowHeight": 642, "statusBarHeight": 44, "safeArea": {"top": 44, "left": 0, "right": 375, "bottom": 778, "width": 375, "height": 734}, "language": "zh_CN", "version": "8.0.5", "fontSizeSetting": 16, "SDKVersion": "3.8.12", "theme": "light", "host": {"env": "WeChat"}, "enableDebug": false, "mode": "default", "deviceOrientation": "portrait", "bluetoothEnabled": true, "locationEnabled": true, "wifiEnabled": true, "albumAuthorized": true, "cameraAuthorized": true, "locationAuthorized": true, "microphoneAuthorized": true, "notificationAuthorized": true, "notificationAlertAuthorized": true, "notificationBadgeAuthorized": true, "notificationSoundAuthorized": true, "phoneCalendarAuthorized": true, "bluetoothAuthorized": true, "locationReducedAccuracy": true, "devicePixelRatio": 3}}, "log_filename": "run.log", "error_type": "", "error_value": "", "error_extra_info": "", "error_stages": [], "screen_info": [{"name": "setup", "url": "/pages/home/<USER>", "path": "images\\setup.png", "ts": 1753843234, "datetime": "2025-07-30 10:40:34", "use_region": false}, {"name": "teardown", "url": "/pages/login/index", "path": "images\\teardown.png", "ts": 1753843244, "datetime": "2025-07-30 10:40:44", "use_region": false}], "step_info": [], "check_list": [], "assert_list": [], "autofix_info": [], "perf_data": "{\"startup\": 0, \"avg_cpu\": 0, \"max_cpu\": 0, \"cpu_data_list\": [], \"avg_mem\": 0, \"max_mem\": 0, \"mem_data_list\": [], \"avg_fps\": 0, \"min_fps_rt\": 0, \"fps_data_list\": [], \"fps_time_series_list\": [], \"cpu_time_series_list\": [], \"mem_time_series_list\": []}", "weapp_log_path": "weapp.log", "request_log_path": "request.log", "stop_timestamp": 1753843244.523507, "appId": "", "appName": "", "source": {"code": ["    def test_02_login_and_authentication(self):\n", "        \"\"\"测试登录和认证功能\"\"\"\n", "        print(\"🧪 测试登录和认证功能\")\n", "        \n", "        try:\n", "            # 导航到登录页\n", "            self.app.navigate_to(\"/pages/login/index\")\n", "            time.sleep(2)\n", "            \n", "            current_page = self.app.get_current_page()\n", "            \n", "            # 检查登录页面元素\n", "            login_elements = {\n", "                \"微信登录按钮\": \".wechat-login-btn, button[bindtap*='wechat']\",\n", "                \"账号登录切换\": \".tab-item, .login-tab\",\n", "                \"用户名输入框\": \"input[placeholder*='用户名'], input[placeholder*='账号']\",\n", "                \"密码输入框\": \"input[placeholder*='密码']\"\n", "            }\n", "            \n", "            for element_name, selector in login_elements.items():\n", "                try:\n", "                    element = current_page.get_element(selector)\n", "                    if element:\n", "                        self.log_test_result(f\"登录页面-{element_name}\", True, \"元素存在\")\n", "                    else:\n", "                        self.log_test_result(f\"登录页面-{element_name}\", False, \"元素不存在\")\n", "                except:\n", "                    self.log_test_result(f\"登录页面-{element_name}\", False, \"查找失败\")\n", "            \n", "            # 尝试切换到账号登录\n", "            tab_switched = self.safe_click(current_page, \".tab-item[data-type='password'], .password-tab\", \"切换账号登录\")\n", "            if tab_switched:\n", "                time.sleep(1)\n", "                \n", "                # 尝试输入测试账号\n", "                username_input = self.safe_input(current_page, \"input[placeholder*='用户名']\", \"test_user\", \"输入用户名\")\n", "                password_input = self.safe_input(current_page, \"input[placeholder*='密码']\", \"test123\", \"输入密码\")\n", "                \n", "                if username_input and password_input:\n", "                    # 尝试点击登录按钮\n", "                    login_clicked = self.safe_click(current_page, \".login-btn, button[bindtap*='login']\", \"点击登录\")\n", "                    if login_clicked:\n", "                        time.sleep(3)\n", "                        \n", "                        # 检查登录结果\n", "                        new_page = self.app.get_current_page()\n", "                        if \"login\" not in new_page.path:\n", "                            self.log_test_result(\"账号登录功能\", True, \"登录成功，页面跳转\")\n", "                        else:\n", "                            self.log_test_result(\"账号登录功能\", False, \"登录失败或未跳转\")\n", "            \n", "        except Exception as e:\n", "            self.log_test_result(\"登录认证测试\", False, f\"测试异常: {e}\")\n"], "start": 121}, "filename": "result.json"}